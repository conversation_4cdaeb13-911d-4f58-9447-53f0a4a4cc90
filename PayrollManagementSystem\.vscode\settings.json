{
    "files.encoding": "utf8",
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.formatOnType": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.wordWrap": "on",
    "editor.rulers": [120],
    "editor.minimap.enabled": true,
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,
    
    // C# specific settings
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    "dotnet.server.useOmnisharp": false,
    "dotnet.inlayHints.enableInlayHintsForParameters": true,
    "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true,
    "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true,
    "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true,
    "dotnet.inlayHints.enableInlayHintsForOtherParameters": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true,
    
    // XML/XAML settings
    "xml.format.enabled": true,
    "xml.format.splitAttributes": true,
    "xml.format.joinCDATALines": false,
    "xml.format.joinCommentLines": false,
    "xml.format.joinContentLines": false,
    "xml.format.spaceBeforeEmptyCloseTag": true,
    "xml.format.xsiSchemaLocationSplit": "onPair",
    
    // File associations
    "files.associations": {
        "*.xaml": "xml",
        "*.config": "xml",
        "*.props": "xml",
        "*.targets": "xml",
        "*.resx": "xml"
    },
    
    // Exclude patterns
    "files.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/Published": true,
        "**/Logs": true,
        "**/Backups": true,
        "**/Reports": true
    },
    
    // Search exclude patterns
    "search.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/Published": true,
        "**/node_modules": true,
        "**/Logs": true
    },
    
    // Git settings
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    "git.confirmSync": false,
    
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.fontFamily": "Consolas, 'Courier New', monospace",
    
    // Workbench settings
    "workbench.colorTheme": "Visual Studio Light",
    "workbench.iconTheme": "vs-seti",
    "workbench.startupEditor": "readme",
    
    // Explorer settings
    "explorer.confirmDelete": true,
    "explorer.confirmDragAndDrop": true,
    "explorer.sortOrder": "type",
    
    // Problems settings
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    
    // Extensions settings
    "extensions.autoUpdate": true,
    "extensions.ignoreRecommendations": false,
    
    // Language specific settings
    "[csharp]": {
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "editor.codeActionsOnSave": {
            "source.fixAll": true,
            "source.organizeImports": true
        }
    },
    "[xml]": {
        "editor.defaultFormatter": "redhat.vscode-xml",
        "editor.formatOnSave": true
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features",
        "editor.formatOnSave": true
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features",
        "editor.formatOnSave": true
    },
    "[markdown]": {
        "editor.defaultFormatter": "yzhang.markdown-all-in-one",
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    }
}
