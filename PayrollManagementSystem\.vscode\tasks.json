{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/PayrollManagementSystem.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "build-release", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/PayrollManagementSystem.csproj", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/PayrollManagementSystem.csproj", "--configuration", "Release", "--runtime", "win-x64", "--self-contained", "true", "--output", "${workspaceFolder}/Published"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/PayrollManagementSystem.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/PayrollManagementSystem.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/PayrollManagementSystem.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "ef-update-database", "command": "dotnet", "type": "process", "args": ["ef", "database", "update"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": "$msCompile", "group": "build"}, {"label": "ef-add-migration", "command": "dotnet", "type": "process", "args": ["ef", "migrations", "add", "${input:migrationName}"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": "$msCompile", "group": "build"}], "inputs": [{"id": "migrationName", "description": "Enter migration name", "default": "NewMigration", "type": "promptString"}]}