<Application x:Class="PayrollManagementSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- الخطوط العربية -->
                <ResourceDictionary>
                    <FontFamily x:Key="ArabicFont">Sakkal <PERSON>all<PERSON></FontFamily>
                    <FontFamily x:Key="ArabicBoldFont">Sakkal Majalla Bold</FontFamily>
                    <FontFamily x:Key="EnglishFont">Segoe UI</FontFamily>
                </ResourceDictionary>

                <!-- الألوان المخصصة -->
                <ResourceDictionary>
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
                    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#0D47A1"/>
                    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#BBDEFB"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="#FFC107"/>
                    <SolidColorBrush x:Key="AccentDarkBrush" Color="#FF8F00"/>
                    <SolidColorBrush x:Key="AccentLightBrush" Color="#FFF8E1"/>
                    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
                    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
                    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
                </ResourceDictionary>

                <!-- الأنماط العامة -->
                <ResourceDictionary>
                    <!-- نمط النص العربي -->
                    <Style x:Key="ArabicTextStyle" TargetType="TextBlock">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="TextAlignment" Value="Right"/>
                        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                    </Style>

                    <!-- نمط العناوين -->
                    <Style x:Key="HeaderTextStyle" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextStyle}">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                    </Style>

                    <!-- نمط العناوين الفرعية -->
                    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextStyle}">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryDarkBrush}"/>
                    </Style>

                    <!-- نمط التسميات -->
                    <Style x:Key="LabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextStyle}">
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                        <Setter Property="Margin" Value="0,0,0,5"/>
                    </Style>

                    <!-- نمط حقول الإدخال -->
                    <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="TextAlignment" Value="Right"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="Margin" Value="0,0,0,15"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                        <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
                    </Style>

                    <!-- نمط كلمة المرور -->
                    <Style x:Key="PasswordBoxStyle" TargetType="PasswordBox">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FlowDirection" Value="LeftToRight"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="Margin" Value="0,0,0,15"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>

                    <!-- نمط الأزرار الرئيسية -->
                    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="20,10"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="5"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>

                    <!-- نمط الأزرار الثانوية -->
                    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="20,10"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="5"/>
                    </Style>

                    <!-- نمط البطاقات -->
                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Padding" Value="20"/>
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                        <Setter Property="materialDesign:CardAssist.CornerRadius" Value="8"/>
                    </Style>

                    <!-- نمط الشبكة -->
                    <Style x:Key="DataGridStyle" TargetType="DataGrid">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
                        <Setter Property="RowBackground" Value="White"/>
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="10,8"/>
                        <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="10,12"/>
                    </Style>

                    <!-- نمط عناوين الأعمدة -->
                    <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                        <Setter Property="Padding" Value="10,12"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                    </Style>

                    <!-- نمط صفوف الشبكة -->
                    <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentLightBrush}"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- نمط القوائم -->
                    <Style x:Key="MenuStyle" TargetType="Menu">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Style>

                    <!-- نمط عناصر القائمة -->
                    <Style x:Key="MenuItemStyle" TargetType="MenuItem">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="15,8"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- نمط شريط الحالة -->
                    <Style x:Key="StatusBarStyle" TargetType="StatusBar">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="12"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="0,1,0,0"/>
                    </Style>

                    <!-- نمط شريط الأدوات -->
                    <Style x:Key="ToolBarStyle" TargetType="ToolBar">
                        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
