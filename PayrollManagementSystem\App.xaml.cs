using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Services;
using PayrollManagementSystem.ViewModels;
using Serilog;
using System;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Windows;
using System.Windows.Markup;

namespace PayrollManagementSystem
{
    /// <summary>
    /// التطبيق الرئيسي
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        private IConfiguration? _configuration;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // تكوين الثقافة العربية
                ConfigureArabicCulture();

                // تكوين التسجيل
                ConfigureLogging();

                // تكوين التطبيق
                await ConfigureApplication();

                // تكوين قاعدة البيانات
                await ConfigureDatabaseAsync();

                Log.Information("تم بدء تشغيل التطبيق بنجاح");

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "فشل في بدء تشغيل التطبيق");
                MessageBox.Show($"حدث خطأ في بدء تشغيل التطبيق:\n{ex.Message}", 
                    "خطأ في التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                Log.Information("إغلاق التطبيق");
                
                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }

                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في ملف منفصل
                File.WriteAllText("shutdown_error.log", $"{DateTime.Now}: {ex}");
            }

            base.OnExit(e);
        }

        private void ConfigureArabicCulture()
        {
            // تعيين الثقافة العربية العراقية
            var culture = new CultureInfo("ar-IQ");
            
            // تخصيص إعدادات التاريخ والوقت
            culture.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
            culture.DateTimeFormat.LongDatePattern = "dddd، dd MMMM yyyy";
            culture.DateTimeFormat.ShortTimePattern = "HH:mm";
            culture.DateTimeFormat.LongTimePattern = "HH:mm:ss";
            
            // تخصيص إعدادات الأرقام
            culture.NumberFormat.CurrencySymbol = "د.ع";
            culture.NumberFormat.CurrencyDecimalDigits = 3;
            culture.NumberFormat.NumberDecimalDigits = 3;
            
            // تطبيق الثقافة
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // تعيين اتجاه النص من اليمين إلى اليسار
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(XmlLanguage.GetLanguage("ar-IQ")));
        }

        private void ConfigureLogging()
        {
            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logsDirectory))
            {
                Directory.CreateDirectory(logsDirectory);
            }

            // تكوين Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .WriteTo.File(
                    path: Path.Combine(logsDirectory, "payroll-system-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10 MB
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();
        }

        private async Task ConfigureApplication()
        {
            // بناء التكوين
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables();

            _configuration = builder.Build();

            // بناء الخدمات
            var services = new ServiceCollection();
            ConfigureServices(services);

            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) => ConfigureServices(services))
                .UseSerilog()
                .Build();

            await _host.StartAsync();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            if (_configuration == null) return;

            // تسجيل التكوين
            services.AddSingleton(_configuration);

            // تسجيل DbContext
            services.AddDbContext<PayrollDbContext>(options =>
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.CommandTimeout(30);
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);
                });

                // تمكين التسجيل المفصل في وضع التطوير
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
            });

            // تسجيل الخدمات
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IPayrollService, PayrollService>();
            services.AddScoped<IAccountingService, AccountingService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<ISettingsService, SettingsService>();

            // تسجيل ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<EmployeeManagementViewModel>();
            services.AddTransient<PayrollManagementViewModel>();
            services.AddTransient<AccountingViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();

            // تسجيل النوافذ
            services.AddTransient<Views.LoginWindow>();
            services.AddTransient<Views.MainWindow>();

            // خدمات أخرى
            services.AddSingleton<ICurrentUserService, CurrentUserService>();
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<INavigationService, NavigationService>();
        }

        private async Task ConfigureDatabaseAsync()
        {
            try
            {
                if (_host == null) return;

                using var scope = _host.Services.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<PayrollDbContext>();

                // التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
                var created = await context.Database.EnsureCreatedAsync();
                
                if (created)
                {
                    Log.Information("تم إنشاء قاعدة البيانات بنجاح");
                }
                else
                {
                    // تطبيق أي تحديثات معلقة
                    var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                    if (pendingMigrations.Any())
                    {
                        await context.Database.MigrateAsync();
                        Log.Information("تم تطبيق تحديثات قاعدة البيانات");
                    }
                }

                Log.Information("تم تكوين قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "فشل في تكوين قاعدة البيانات");
                throw;
            }
        }

        /// <summary>
        /// الحصول على خدمة من حاوي الحقن
        /// </summary>
        /// <typeparam name="T">نوع الخدمة</typeparam>
        /// <returns>الخدمة المطلوبة</returns>
        public static T GetService<T>() where T : class
        {
            if (Current is App app && app._host != null)
            {
                return app._host.Services.GetRequiredService<T>();
            }
            throw new InvalidOperationException("التطبيق غير مهيأ بشكل صحيح");
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة
        /// </summary>
        private void Application_DispatcherUnhandledException(object sender, 
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Error(e.Exception, "خطأ غير معالج في التطبيق");
            
            MessageBox.Show($"حدث خطأ غير متوقع:\n{e.Exception.Message}\n\nسيتم إغلاق التطبيق.", 
                "خطأ في التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
            
            e.Handled = true;
            Shutdown();
        }
    }
}
