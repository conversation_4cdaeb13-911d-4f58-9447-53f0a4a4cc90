<Application x:Class="PayrollManagementSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:PayrollManagementSystem.Converters"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             StartupUri="Views/LoginWindow_Simple.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- المحولات -->
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
            <converters:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
            <converters:CurrencyConverter x:Key="CurrencyConverter"/>
            <converters:ArabicNumberConverter x:Key="ArabicNumberConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
