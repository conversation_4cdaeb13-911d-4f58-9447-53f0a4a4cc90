using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Services;
using PayrollManagementSystem.ViewModels;
using System;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Windows;
using System.Windows.Markup;

namespace PayrollManagementSystem
{
    /// <summary>
    /// التطبيق الرئيسي
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        private IConfiguration? _configuration;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // تكوين الثقافة العربية
                ConfigureArabicCulture();

                // تكوين التطبيق
                await ConfigureApplication();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في بدء تشغيل التطبيق:\n{ex.Message}", 
                    "خطأ في التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في ملف منفصل
                File.WriteAllText("shutdown_error.log", $"{DateTime.Now}: {ex}");
            }

            base.OnExit(e);
        }

        private void ConfigureArabicCulture()
        {
            // تعيين الثقافة العربية العراقية
            var culture = new CultureInfo("ar-IQ");
            
            // تخصيص إعدادات التاريخ والوقت
            culture.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
            culture.DateTimeFormat.LongDatePattern = "dddd، dd MMMM yyyy";
            culture.DateTimeFormat.ShortTimePattern = "HH:mm";
            culture.DateTimeFormat.LongTimePattern = "HH:mm:ss";
            
            // تخصيص إعدادات الأرقام
            culture.NumberFormat.CurrencySymbol = "د.ع";
            culture.NumberFormat.CurrencyDecimalDigits = 3;
            culture.NumberFormat.NumberDecimalDigits = 3;
            
            // تطبيق الثقافة
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // تعيين اتجاه النص من اليمين إلى اليسار
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(XmlLanguage.GetLanguage("ar-IQ")));
        }

        private async Task ConfigureApplication()
        {
            // بناء التكوين
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            _configuration = builder.Build();

            // بناء الخدمات
            var services = new ServiceCollection();
            ConfigureServices(services);

            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) => ConfigureServices(services))
                .Build();

            await _host.StartAsync();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            if (_configuration == null) return;

            // تسجيل التكوين
            services.AddSingleton(_configuration);

            // تسجيل DbContext مع In-Memory Database للاختبار
            services.AddDbContext<PayrollDbContext>(options =>
            {
                options.UseInMemoryDatabase("PayrollTestDB");
            });

            // تسجيل الخدمات
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IPayrollService, PayrollService>();
            services.AddScoped<IAccountingService, AccountingService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<ISettingsService, SettingsService>();

            // تسجيل ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<EmployeeManagementViewModel>();
            services.AddTransient<PayrollManagementViewModel>();
            services.AddTransient<AccountingViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();

            // تسجيل النوافذ
            services.AddTransient<Views.LoginWindow>();
            services.AddTransient<Views.MainWindow>();

            // خدمات أخرى
            services.AddSingleton<ICurrentUserService, CurrentUserService>();
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<INavigationService, NavigationService>();
        }

        /// <summary>
        /// الحصول على خدمة من حاوي الحقن
        /// </summary>
        /// <typeparam name="T">نوع الخدمة</typeparam>
        /// <returns>الخدمة المطلوبة</returns>
        public static T GetService<T>() where T : class
        {
            if (Current is App app && app._host != null)
            {
                return app._host.Services.GetRequiredService<T>();
            }
            throw new InvalidOperationException("التطبيق غير مهيأ بشكل صحيح");
        }
    }
}
