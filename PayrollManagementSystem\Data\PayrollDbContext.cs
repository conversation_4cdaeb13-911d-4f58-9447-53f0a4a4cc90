using Microsoft.EntityFrameworkCore;
using PayrollManagementSystem.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class PayrollDbContext : DbContext
    {
        public PayrollDbContext(DbContextOptions<PayrollDbContext> options) : base(options)
        {
        }

        // جداول إدارة المستخدمين
        public DbSet<User> Users { get; set; }
        public DbSet<UserGroup> UserGroups { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserGroupPermission> UserGroupPermissions { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<UserActivity> UserActivities { get; set; }

        // جداول الإعدادات والتهيئة
        public DbSet<Organization> Organizations { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<AccountingPeriod> AccountingPeriods { get; set; }
        public DbSet<SystemSetting> SystemSettings { get; set; }
        public DbSet<PayrollSetting> PayrollSettings { get; set; }

        // جداول شؤون الموظفين
        public DbSet<Qualification> Qualifications { get; set; }
        public DbSet<JobGrade> JobGrades { get; set; }
        public DbSet<JobStage> JobStages { get; set; }
        public DbSet<JobTitle> JobTitles { get; set; }
        public DbSet<Employee> Employees { get; set; }

        // جداول الحسابات
        public DbSet<ChartOfAccount> ChartOfAccounts { get; set; }
        public DbSet<Bank> Banks { get; set; }
        public DbSet<BankBranch> BankBranches { get; set; }
        public DbSet<BankAccount> BankAccounts { get; set; }

        // جداول القيود المحاسبية
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<PaymentVoucher> PaymentVouchers { get; set; }
        public DbSet<ReceiptVoucher> ReceiptVouchers { get; set; }

        // جداول الرواتب
        public DbSet<AllowanceType> AllowanceTypes { get; set; }
        public DbSet<DeductionType> DeductionTypes { get; set; }
        public DbSet<EmployeeAllowance> EmployeeAllowances { get; set; }
        public DbSet<EmployeeDeduction> EmployeeDeductions { get; set; }
        public DbSet<PayrollRecord> PayrollRecords { get; set; }
        public DbSet<PayrollRecordDetail> PayrollRecordDetails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين الفهارس
            ConfigureIndexes(modelBuilder);

            // تكوين العلاقات
            ConfigureRelationships(modelBuilder);

            // تكوين القيود
            ConfigureConstraints(modelBuilder);

            // تكوين البيانات الافتراضية
            SeedData(modelBuilder);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس المستخدمين
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.AccountNumber)
                .IsUnique();

            // فهارس الموظفين
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeNumber)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.IbanNumber)
                .IsUnique()
                .HasFilter("[IbanNumber] IS NOT NULL");

            // فهارس الحسابات
            modelBuilder.Entity<ChartOfAccount>()
                .HasIndex(c => c.Code)
                .IsUnique();

            modelBuilder.Entity<BankAccount>()
                .HasIndex(b => b.AccountNumber)
                .IsUnique();

            // فهارس القيود
            modelBuilder.Entity<JournalEntry>()
                .HasIndex(j => j.EntryNumber)
                .IsUnique();

            // فهارس الرواتب
            modelBuilder.Entity<PayrollRecord>()
                .HasIndex(p => new { p.EmployeeId, p.PayrollYear, p.PayrollMonth })
                .IsUnique();
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // علاقات المستخدمين
            modelBuilder.Entity<User>()
                .HasOne(u => u.UserGroup)
                .WithMany(ug => ug.Users)
                .HasForeignKey(u => u.UserGroupId)
                .OnDelete(DeleteBehavior.SetNull);

            // علاقات الأقسام الهرمية
            modelBuilder.Entity<Department>()
                .HasOne(d => d.ParentDepartment)
                .WithMany(d => d.SubDepartments)
                .HasForeignKey(d => d.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات الحسابات الهرمية
            modelBuilder.Entity<ChartOfAccount>()
                .HasOne(c => c.ParentAccount)
                .WithMany(c => c.SubAccounts)
                .HasForeignKey(c => c.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات الموظفين
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Department)
                .WithMany(d => d.Employees)
                .HasForeignKey(e => e.DepartmentId)
                .OnDelete(DeleteBehavior.SetNull);

            // علاقات القيود
            modelBuilder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.JournalEntry)
                .WithMany(je => je.Details)
                .HasForeignKey(jed => jed.JournalEntryId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقات الرواتب
            modelBuilder.Entity<PayrollRecordDetail>()
                .HasOne(prd => prd.PayrollRecord)
                .WithMany(pr => pr.Details)
                .HasForeignKey(prd => prd.PayrollRecordId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureConstraints(ModelBuilder modelBuilder)
        {
            // قيود الأرقام العشرية
            modelBuilder.Entity<ChartOfAccount>()
                .Property(c => c.OpeningBalanceDebit)
                .HasPrecision(18, 3);

            modelBuilder.Entity<ChartOfAccount>()
                .Property(c => c.OpeningBalanceCredit)
                .HasPrecision(18, 3);

            modelBuilder.Entity<JournalEntryDetail>()
                .Property(jed => jed.DebitAmount)
                .HasPrecision(18, 3);

            modelBuilder.Entity<JournalEntryDetail>()
                .Property(jed => jed.CreditAmount)
                .HasPrecision(18, 3);

            // قيود التواريخ
            modelBuilder.Entity<Employee>()
                .HasCheckConstraint("CK_Employee_BirthDate", "[BirthDate] < GETDATE()");

            modelBuilder.Entity<Employee>()
                .HasCheckConstraint("CK_Employee_HireDate", "[HireDate] <= GETDATE()");
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // بيانات المؤسسة الافتراضية
            modelBuilder.Entity<Organization>().HasData(
                new Organization
                {
                    Id = 1,
                    Number = "001",
                    Code = "ORG001",
                    Name = "المؤسسة الافتراضية",
                    Address = "بغداد - العراق",
                    Email = "<EMAIL>",
                    Phone = "+964-1-XXXXXXX",
                    CreatedBy = "System",
                    CreatedDate = DateTime.Now
                });

            // العملة الافتراضية
            modelBuilder.Entity<Currency>().HasData(
                new Currency
                {
                    Id = 1,
                    Number = "001",
                    Code = "IQD",
                    Name = "الدينار العراقي",
                    CurrencyCode = "IQD",
                    CurrencySubunit = "فلس",
                    CurrencyType = "محلية",
                    ExchangeRate = 1,
                    IsDefault = true,
                    Symbol = "د.ع",
                    DecimalPlaces = 3,
                    CreatedBy = "System",
                    CreatedDate = DateTime.Now
                });

            // مجموعة المدراء
            modelBuilder.Entity<UserGroup>().HasData(
                new UserGroup
                {
                    Id = 1,
                    Number = "001",
                    Code = "ADMIN",
                    Name = "مجموعة المدراء",
                    Description = "مجموعة المستخدمين الإداريين",
                    PermissionLevel = 10,
                    CreatedBy = "System",
                    CreatedDate = DateTime.Now
                });

            // المستخدم الافتراضي
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    AccountNumber = "ADM001",
                    AccountName = "مدير النظام",
                    Username = "admin",
                    PasswordHash = User.HashPassword("admin"),
                    AccountType = "مدير",
                    UserGroupId = 1,
                    Email = "<EMAIL>",
                    CreatedBy = "System",
                    CreatedDate = DateTime.Now
                });
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // تحديث معلومات التعديل تلقائياً
            var entries = ChangeTracker.Entries<BaseEntity>()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Added)
                {
                    entry.Entity.CreatedDate = DateTime.Now;
                    // يمكن تحديد المستخدم الحالي هنا
                }
                else if (entry.State == EntityState.Modified)
                {
                    entry.Entity.ModifiedDate = DateTime.Now;
                    // يمكن تحديد المستخدم الحالي هنا
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}
