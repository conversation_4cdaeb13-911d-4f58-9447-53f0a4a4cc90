using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// دليل الحسابات
    /// </summary>
    [Table("ChartOfAccounts")]
    public class ChartOfAccount : HierarchicalEntity
    {
        [Required]
        [StringLength(50)]
        [Display(Name = "نوع الحساب")]
        public string AccountType { get; set; } = string.Empty; // أصول، خصوم، حقوق ملكية، إيرادات، مصروفات

        [Required]
        [StringLength(20)]
        [Display(Name = "طبيعة الحساب")]
        public string AccountNature { get; set; } = string.Empty; // مدين، دائن

        [Display(Name = "حساب تحليلي")]
        public bool IsAnalytical { get; set; } = false;

        [Display(Name = "حساب نقدي")]
        public bool IsCashAccount { get; set; } = false;

        [Display(Name = "حساب بنكي")]
        public bool IsBankAccount { get; set; } = false;

        [Display(Name = "العملة")]
        public int? CurrencyId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الرصيد الافتتاحي مدين")]
        public decimal OpeningBalanceDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الرصيد الافتتاحي دائن")]
        public decimal OpeningBalanceCredit { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الرصيد الحالي")]
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(100)]
        [Display(Name = "رقم الحساب البنكي")]
        public string? BankAccountNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم البنك")]
        public string? BankName { get; set; }

        [StringLength(100)]
        [Display(Name = "فرع البنك")]
        public string? BankBranch { get; set; }

        // العلاقات
        [ForeignKey("CurrencyId")]
        public virtual Currency? Currency { get; set; }

        [ForeignKey("ParentId")]
        public virtual ChartOfAccount? ParentAccount { get; set; }

        public virtual ICollection<ChartOfAccount> SubAccounts { get; set; } = new List<ChartOfAccount>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();

        /// <summary>
        /// حساب الرصيد الحالي
        /// </summary>
        /// <param name="debitAmount">المبلغ المدين</param>
        /// <param name="creditAmount">المبلغ الدائن</param>
        public void UpdateBalance(decimal debitAmount, decimal creditAmount)
        {
            if (AccountNature == "مدين")
            {
                CurrentBalance = OpeningBalanceDebit + debitAmount - creditAmount;
            }
            else
            {
                CurrentBalance = OpeningBalanceCredit + creditAmount - debitAmount;
            }
        }

        /// <summary>
        /// التحقق من صحة الرصيد
        /// </summary>
        /// <returns>صحيح إذا كان الرصيد صحيح</returns>
        public bool IsBalanceValid()
        {
            if (AccountNature == "مدين")
            {
                return CurrentBalance >= 0;
            }
            else
            {
                return CurrentBalance >= 0;
            }
        }
    }

    /// <summary>
    /// الصناديق والمصارف
    /// </summary>
    [Table("Banks")]
    public class Bank : CodedEntity
    {
        [StringLength(100)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(100)]
        [Display(Name = "الموقع الإلكتروني")]
        public string? Website { get; set; }

        [StringLength(50)]
        [Display(Name = "رمز البنك")]
        public string? BankCode { get; set; }

        [StringLength(50)]
        [Display(Name = "رمز SWIFT")]
        public string? SwiftCode { get; set; }

        [Display(Name = "نوع البنك")]
        public string BankType { get; set; } = "تجاري"; // تجاري، حكومي، إسلامي، استثماري

        // العلاقات
        public virtual ICollection<BankBranch> Branches { get; set; } = new List<BankBranch>();
    }

    /// <summary>
    /// فروع المصارف
    /// </summary>
    [Table("BankBranches")]
    public class BankBranch : CodedEntity
    {
        [Required]
        [Display(Name = "البنك")]
        public int BankId { get; set; }

        [StringLength(100)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(100)]
        [Display(Name = "مدير الفرع")]
        public string? BranchManager { get; set; }

        [StringLength(50)]
        [Display(Name = "رقم الفرع")]
        public string? BranchNumber { get; set; }

        // العلاقات
        [ForeignKey("BankId")]
        public virtual Bank Bank { get; set; } = null!;

        public virtual ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();
    }

    /// <summary>
    /// الحسابات البنكية
    /// </summary>
    [Table("BankAccounts")]
    public class BankAccount : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الحساب")]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الحساب")]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "فرع البنك")]
        public int BankBranchId { get; set; }

        [Display(Name = "الحساب في دليل الحسابات")]
        public int? ChartOfAccountId { get; set; }

        [Display(Name = "العملة")]
        public int? CurrencyId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع الحساب")]
        public string AccountType { get; set; } = "جاري"; // جاري، توفير، ودائع، استثماري

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الرصيد الحالي")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الحد الأدنى للرصيد")]
        public decimal MinimumBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "الحد الأقصى للسحب اليومي")]
        public decimal DailyWithdrawalLimit { get; set; } = 0;

        [StringLength(30)]
        [Display(Name = "رقم الآيبان")]
        public string? IbanNumber { get; set; }

        [Display(Name = "تاريخ فتح الحساب")]
        public DateTime? OpenDate { get; set; }

        [Display(Name = "تاريخ إغلاق الحساب")]
        public DateTime? CloseDate { get; set; }

        [Display(Name = "حساب مقفل")]
        public bool IsClosed { get; set; } = false;

        [StringLength(500)]
        [Display(Name = "سبب الإغلاق")]
        public string? CloseReason { get; set; }

        // العلاقات
        [ForeignKey("BankBranchId")]
        public virtual BankBranch BankBranch { get; set; } = null!;

        [ForeignKey("ChartOfAccountId")]
        public virtual ChartOfAccount? ChartOfAccount { get; set; }

        [ForeignKey("CurrencyId")]
        public virtual Currency? Currency { get; set; }

        public virtual ICollection<JournalEntry> JournalEntries { get; set; } = new List<JournalEntry>();

        /// <summary>
        /// إيداع مبلغ في الحساب
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void Deposit(decimal amount)
        {
            if (amount > 0 && !IsClosed)
            {
                CurrentBalance += amount;
            }
        }

        /// <summary>
        /// سحب مبلغ من الحساب
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>صحيح إذا تم السحب بنجاح</returns>
        public bool Withdraw(decimal amount)
        {
            if (amount > 0 && !IsClosed && CurrentBalance >= amount && CurrentBalance - amount >= MinimumBalance)
            {
                CurrentBalance -= amount;
                return true;
            }
            return false;
        }

        /// <summary>
        /// إغلاق الحساب
        /// </summary>
        /// <param name="reason">سبب الإغلاق</param>
        public void CloseAccount(string reason)
        {
            IsClosed = true;
            CloseDate = DateTime.Now;
            CloseReason = reason;
            IsActive = false;
        }

        /// <summary>
        /// إعادة فتح الحساب
        /// </summary>
        public void ReopenAccount()
        {
            IsClosed = false;
            CloseDate = null;
            CloseReason = null;
            IsActive = true;
        }
    }
}
