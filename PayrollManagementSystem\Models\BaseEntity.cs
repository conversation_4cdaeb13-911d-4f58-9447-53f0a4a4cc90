using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// الكلاس الأساسي لجميع الكيانات
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Display(Name = "المستخدم المنشئ")]
        public string CreatedBy { get; set; } = string.Empty;

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(100)]
        [Display(Name = "المستخدم المعدل")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "حالة النشاط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "محذوف")]
        public bool IsDeleted { get; set; } = false;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        /// <summary>
        /// تحديث معلومات التعديل
        /// </summary>
        /// <param name="modifiedBy">المستخدم المعدل</param>
        public virtual void UpdateModificationInfo(string modifiedBy)
        {
            ModifiedDate = DateTime.Now;
            ModifiedBy = modifiedBy;
        }

        /// <summary>
        /// حذف منطقي للكيان
        /// </summary>
        /// <param name="deletedBy">المستخدم الذي قام بالحذف</param>
        public virtual void SoftDelete(string deletedBy)
        {
            IsDeleted = true;
            IsActive = false;
            UpdateModificationInfo(deletedBy);
        }

        /// <summary>
        /// استعادة الكيان المحذوف
        /// </summary>
        /// <param name="restoredBy">المستخدم الذي قام بالاستعادة</param>
        public virtual void Restore(string restoredBy)
        {
            IsDeleted = false;
            IsActive = true;
            UpdateModificationInfo(restoredBy);
        }
    }

    /// <summary>
    /// كلاس أساسي للكيانات التي تحتاج رقم ورمز
    /// </summary>
    public abstract class CodedEntity : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "الرقم")]
        public string Number { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        [Display(Name = "الرمز")]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "الاسم")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? NameEn { get; set; }
    }

    /// <summary>
    /// كلاس أساسي للكيانات الهرمية
    /// </summary>
    public abstract class HierarchicalEntity : CodedEntity
    {
        [Display(Name = "الكيان الأب")]
        public int? ParentId { get; set; }

        [Display(Name = "المستوى")]
        public int Level { get; set; } = 1;

        [StringLength(500)]
        [Display(Name = "المسار الهرمي")]
        public string? HierarchyPath { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 1;

        /// <summary>
        /// تحديث المسار الهرمي
        /// </summary>
        /// <param name="parentPath">مسار الأب</param>
        public virtual void UpdateHierarchyPath(string? parentPath = null)
        {
            if (string.IsNullOrEmpty(parentPath))
            {
                HierarchyPath = Id.ToString();
                Level = 1;
            }
            else
            {
                HierarchyPath = $"{parentPath}/{Id}";
                Level = parentPath.Split('/').Length + 1;
            }
        }
    }
}
