using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// الشهادات
    /// </summary>
    [Table("Qualifications")]
    public class Qualification : CodedEntity
    {
        [StringLength(50)]
        [Display(Name = "نوع الشهادة")]
        public string? QualificationType { get; set; } = "أكاديمية"; // أكاديمية، مهنية، تدريبية

        [Display(Name = "المستوى")]
        public int Level { get; set; } = 1; // 1=ابتدائية، 2=متوسطة، 3=إعدادية، 4=دبلوم، 5=بكالوريوس، 6=ماجستير، 7=دكتوراه

        [Display(Name = "عدد السنوات")]
        public int YearsOfStudy { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مخصص الشهادة")]
        public decimal QualificationAllowance { get; set; } = 0;

        // العلاقات
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// الدرجات الوظيفية
    /// </summary>
    [Table("JobGrades")]
    public class JobGrade : CodedEntity
    {
        [Display(Name = "المستوى")]
        public int Level { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الراتب الأساسي")]
        public decimal BasicSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحد الأدنى للراتب")]
        public decimal MinSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحد الأقصى للراتب")]
        public decimal MaxSalary { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة العلاوة السنوية")]
        public decimal AnnualIncreasePercentage { get; set; } = 0;

        // العلاقات
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// المراحل
    /// </summary>
    [Table("JobStages")]
    public class JobStage : CodedEntity
    {
        [Display(Name = "رقم المرحلة")]
        public int StageNumber { get; set; }

        [Display(Name = "الدرجة الوظيفية")]
        public int? JobGradeId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "راتب المرحلة")]
        public decimal StageSalary { get; set; } = 0;

        [Display(Name = "عدد سنوات المرحلة")]
        public int YearsInStage { get; set; } = 1;

        // العلاقات
        [ForeignKey("JobGradeId")]
        public virtual JobGrade? JobGrade { get; set; }

        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// العناوين الوظيفية
    /// </summary>
    [Table("JobTitles")]
    public class JobTitle : CodedEntity
    {
        [StringLength(50)]
        [Display(Name = "نوع الوظيفة")]
        public string? JobType { get; set; } = "إدارية"; // إدارية، فنية، خدمية

        [Display(Name = "المستوى الإداري")]
        public int ManagementLevel { get; set; } = 1; // 1=موظف، 2=رئيس قسم، 3=مدير، 4=مدير عام

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مخصص المنصب")]
        public decimal PositionAllowance { get; set; } = 0;

        [Display(Name = "يتطلب شهادة")]
        public int? RequiredQualificationId { get; set; }

        [Display(Name = "سنوات الخبرة المطلوبة")]
        public int RequiredExperienceYears { get; set; } = 0;

        // العلاقات
        [ForeignKey("RequiredQualificationId")]
        public virtual Qualification? RequiredQualification { get; set; }

        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// الموظفين
    /// </summary>
    [Table("Employees")]
    public class Employee : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "الرقم الوظيفي")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Display(Name = "الاسم الرباعي")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "الاسم الأول")]
        public string? FirstName { get; set; }

        [StringLength(50)]
        [Display(Name = "اسم الأب")]
        public string? FatherName { get; set; }

        [StringLength(50)]
        [Display(Name = "اسم الجد")]
        public string? GrandFatherName { get; set; }

        [StringLength(50)]
        [Display(Name = "اللقب")]
        public string? LastName { get; set; }

        [StringLength(30)]
        [Display(Name = "رقم الآيبان")]
        public string? IbanNumber { get; set; }

        [Display(Name = "تاريخ التعيين")]
        public DateTime? HireDate { get; set; }

        [Display(Name = "تاريخ الميلاد")]
        public DateTime? BirthDate { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "الجنس")]
        public string Gender { get; set; } = "ذكر"; // ذكر، أنثى

        [Required]
        [StringLength(20)]
        [Display(Name = "الحالة الزوجية")]
        public string MaritalStatus { get; set; } = "أعزب"; // أعزب، متزوج، مطلق، أرمل

        [Display(Name = "عدد الأطفال")]
        public int NumberOfChildren { get; set; } = 0;

        [Display(Name = "الدائرة")]
        public int? DepartmentId { get; set; }

        [Display(Name = "الدرجة الوظيفية")]
        public int? JobGradeId { get; set; }

        [Display(Name = "المرحلة")]
        public int? JobStageId { get; set; }

        [Display(Name = "العنوان الوظيفي")]
        public int? JobTitleId { get; set; }

        [Display(Name = "الشهادة")]
        public int? QualificationId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "حالة الموظف")]
        public string EmployeeStatus { get; set; } = "مستمر"; // مستمر، متقاعد، متوفي، نقل، متوقف

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الراتب الأساسي")]
        public decimal BasicSalary { get; set; } = 0;

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهوية")]
        public string? NationalId { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم جواز السفر")]
        public string? PassportNumber { get; set; }

        [Display(Name = "تاريخ انتهاء الخدمة")]
        public DateTime? ServiceEndDate { get; set; }

        [StringLength(500)]
        [Display(Name = "سبب انتهاء الخدمة")]
        public string? ServiceEndReason { get; set; }

        [StringLength(500)]
        [Display(Name = "مسار صورة الموظف")]
        public string? PhotoPath { get; set; }

        // العلاقات
        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        [ForeignKey("JobGradeId")]
        public virtual JobGrade? JobGrade { get; set; }

        [ForeignKey("JobStageId")]
        public virtual JobStage? JobStage { get; set; }

        [ForeignKey("JobTitleId")]
        public virtual JobTitle? JobTitle { get; set; }

        [ForeignKey("QualificationId")]
        public virtual Qualification? Qualification { get; set; }

        public virtual ICollection<EmployeeAllowance> Allowances { get; set; } = new List<EmployeeAllowance>();
        public virtual ICollection<EmployeeDeduction> Deductions { get; set; } = new List<EmployeeDeduction>();
        public virtual ICollection<PayrollRecord> PayrollRecords { get; set; } = new List<PayrollRecord>();

        /// <summary>
        /// حساب العمر
        /// </summary>
        /// <returns>العمر بالسنوات</returns>
        public int GetAge()
        {
            if (!BirthDate.HasValue) return 0;
            
            var today = DateTime.Today;
            var age = today.Year - BirthDate.Value.Year;
            
            if (BirthDate.Value.Date > today.AddYears(-age))
                age--;
                
            return age;
        }

        /// <summary>
        /// حساب سنوات الخدمة
        /// </summary>
        /// <returns>سنوات الخدمة</returns>
        public int GetServiceYears()
        {
            if (!HireDate.HasValue) return 0;
            
            var endDate = ServiceEndDate ?? DateTime.Today;
            var serviceYears = endDate.Year - HireDate.Value.Year;
            
            if (HireDate.Value.Date > endDate.AddYears(-serviceYears))
                serviceYears--;
                
            return serviceYears;
        }

        /// <summary>
        /// التحقق من أهلية التقاعد
        /// </summary>
        /// <returns>صحيح إذا كان مؤهل للتقاعد</returns>
        public bool IsEligibleForRetirement()
        {
            var age = GetAge();
            var serviceYears = GetServiceYears();
            
            // شروط التقاعد: 60 سنة أو 25 سنة خدمة
            return age >= 60 || serviceYears >= 25;
        }

        /// <summary>
        /// إنهاء خدمة الموظف
        /// </summary>
        /// <param name="endDate">تاريخ انتهاء الخدمة</param>
        /// <param name="reason">سبب انتهاء الخدمة</param>
        /// <param name="status">حالة الموظف الجديدة</param>
        public void EndService(DateTime endDate, string reason, string status)
        {
            ServiceEndDate = endDate;
            ServiceEndReason = reason;
            EmployeeStatus = status;
            IsActive = false;
        }

        /// <summary>
        /// إعادة تفعيل الموظف
        /// </summary>
        public void ReactivateEmployee()
        {
            ServiceEndDate = null;
            ServiceEndReason = null;
            EmployeeStatus = "مستمر";
            IsActive = true;
        }
    }
}
