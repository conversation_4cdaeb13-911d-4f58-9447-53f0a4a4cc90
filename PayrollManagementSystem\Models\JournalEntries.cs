using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// القيود اليومية
    /// </summary>
    [Table("JournalEntries")]
    public class JournalEntry : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "رقم القيد")]
        public string EntryNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ القيد")]
        public DateTime EntryDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع القيد")]
        public string EntryType { get; set; } = string.Empty; // قيد يومي، سند صرف، سند قبض، قيد راتب، قيد إقفال

        [StringLength(1000)]
        [Display(Name = "البيان")]
        public string? Description { get; set; }

        [StringLength(50)]
        [Display(Name = "المرجع")]
        public string? Reference { get; set; }

        [Display(Name = "الفترة المحاسبية")]
        public int? AccountingPeriodId { get; set; }

        [Display(Name = "الحساب البنكي")]
        public int? BankAccountId { get; set; }

        [Display(Name = "العملة")]
        public int? CurrencyId { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        [Display(Name = "سعر الصرف")]
        public decimal ExchangeRate { get; set; } = 1;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "إجمالي المبلغ المدين")]
        public decimal TotalDebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "إجمالي المبلغ الدائن")]
        public decimal TotalCreditAmount { get; set; } = 0;

        [Display(Name = "قيد معتمد")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "تاريخ الاعتماد")]
        public DateTime? ApprovedDate { get; set; }

        [StringLength(100)]
        [Display(Name = "المعتمد من")]
        public string? ApprovedBy { get; set; }

        [Display(Name = "قيد مرحل")]
        public bool IsPosted { get; set; } = false;

        [Display(Name = "تاريخ الترحيل")]
        public DateTime? PostedDate { get; set; }

        [StringLength(100)]
        [Display(Name = "المرحل من")]
        public string? PostedBy { get; set; }

        [Display(Name = "قيد مقفل")]
        public bool IsClosed { get; set; } = false;

        [StringLength(500)]
        [Display(Name = "ملاحظات الاعتماد")]
        public string? ApprovalNotes { get; set; }

        // العلاقات
        [ForeignKey("AccountingPeriodId")]
        public virtual AccountingPeriod? AccountingPeriod { get; set; }

        [ForeignKey("BankAccountId")]
        public virtual BankAccount? BankAccount { get; set; }

        [ForeignKey("CurrencyId")]
        public virtual Currency? Currency { get; set; }

        public virtual ICollection<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();

        /// <summary>
        /// حساب إجمالي المبالغ
        /// </summary>
        public void CalculateTotals()
        {
            TotalDebitAmount = Details.Sum(d => d.DebitAmount);
            TotalCreditAmount = Details.Sum(d => d.CreditAmount);
        }

        /// <summary>
        /// التحقق من توازن القيد
        /// </summary>
        /// <returns>صحيح إذا كان القيد متوازن</returns>
        public bool IsBalanced()
        {
            CalculateTotals();
            return Math.Abs(TotalDebitAmount - TotalCreditAmount) < 0.01m;
        }

        /// <summary>
        /// اعتماد القيد
        /// </summary>
        /// <param name="approvedBy">المعتمد من</param>
        /// <param name="notes">ملاحظات الاعتماد</param>
        public void ApproveEntry(string approvedBy, string? notes = null)
        {
            if (IsBalanced())
            {
                IsApproved = true;
                ApprovedDate = DateTime.Now;
                ApprovedBy = approvedBy;
                ApprovalNotes = notes;
            }
        }

        /// <summary>
        /// ترحيل القيد
        /// </summary>
        /// <param name="postedBy">المرحل من</param>
        public void PostEntry(string postedBy)
        {
            if (IsApproved && !IsPosted)
            {
                IsPosted = true;
                PostedDate = DateTime.Now;
                PostedBy = postedBy;
            }
        }

        /// <summary>
        /// إلغاء اعتماد القيد
        /// </summary>
        public void UnapproveEntry()
        {
            if (!IsPosted)
            {
                IsApproved = false;
                ApprovedDate = null;
                ApprovedBy = null;
                ApprovalNotes = null;
            }
        }

        /// <summary>
        /// إقفال القيد
        /// </summary>
        public void CloseEntry()
        {
            if (IsPosted)
            {
                IsClosed = true;
            }
        }
    }

    /// <summary>
    /// تفاصيل القيود اليومية
    /// </summary>
    [Table("JournalEntryDetails")]
    public class JournalEntryDetail : BaseEntity
    {
        [Required]
        [Display(Name = "القيد اليومي")]
        public int JournalEntryId { get; set; }

        [Required]
        [Display(Name = "الحساب")]
        public int AccountId { get; set; }

        [StringLength(500)]
        [Display(Name = "البيان")]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "المبلغ المدين")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "المبلغ الدائن")]
        public decimal CreditAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "المبلغ بالعملة الأجنبية")]
        public decimal ForeignAmount { get; set; } = 0;

        [Display(Name = "العملة")]
        public int? CurrencyId { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        [Display(Name = "سعر الصرف")]
        public decimal ExchangeRate { get; set; } = 1;

        [StringLength(100)]
        [Display(Name = "مركز التكلفة")]
        public string? CostCenter { get; set; }

        [StringLength(100)]
        [Display(Name = "المشروع")]
        public string? Project { get; set; }

        [Display(Name = "ترتيب السطر")]
        public int LineOrder { get; set; } = 1;

        // العلاقات
        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; } = null!;

        [ForeignKey("AccountId")]
        public virtual ChartOfAccount Account { get; set; } = null!;

        [ForeignKey("CurrencyId")]
        public virtual Currency? Currency { get; set; }

        /// <summary>
        /// حساب المبلغ بالعملة المحلية
        /// </summary>
        /// <returns>المبلغ بالعملة المحلية</returns>
        public decimal GetLocalAmount()
        {
            if (ForeignAmount > 0 && ExchangeRate > 0)
            {
                return ForeignAmount * ExchangeRate;
            }
            return DebitAmount > 0 ? DebitAmount : CreditAmount;
        }

        /// <summary>
        /// التحقق من صحة السطر
        /// </summary>
        /// <returns>صحيح إذا كان السطر صحيح</returns>
        public bool IsValid()
        {
            // يجب أن يكون هناك مبلغ مدين أو دائن وليس كلاهما
            return (DebitAmount > 0 && CreditAmount == 0) || (CreditAmount > 0 && DebitAmount == 0);
        }
    }

    /// <summary>
    /// سندات الصرف
    /// </summary>
    [Table("PaymentVouchers")]
    public class PaymentVoucher : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "رقم السند")]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ السند")]
        public DateTime VoucherDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(200)]
        [Display(Name = "المستفيد")]
        public string Beneficiary { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; } = 0;

        [StringLength(1000)]
        [Display(Name = "البيان")]
        public string? Description { get; set; }

        [Display(Name = "الحساب البنكي")]
        public int? BankAccountId { get; set; }

        [Display(Name = "القيد اليومي")]
        public int? JournalEntryId { get; set; }

        [StringLength(50)]
        [Display(Name = "طريقة الدفع")]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، شيك، تحويل بنكي

        [StringLength(50)]
        [Display(Name = "رقم الشيك/التحويل")]
        public string? CheckNumber { get; set; }

        [Display(Name = "تاريخ الشيك")]
        public DateTime? CheckDate { get; set; }

        [StringLength(100)]
        [Display(Name = "البنك المسحوب عليه")]
        public string? DraweeBank { get; set; }

        [Display(Name = "سند معتمد")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "سند مدفوع")]
        public bool IsPaid { get; set; } = false;

        [Display(Name = "تاريخ الدفع")]
        public DateTime? PaidDate { get; set; }

        // العلاقات
        [ForeignKey("BankAccountId")]
        public virtual BankAccount? BankAccount { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }
    }

    /// <summary>
    /// سندات القبض
    /// </summary>
    [Table("ReceiptVouchers")]
    public class ReceiptVoucher : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "رقم السند")]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ السند")]
        public DateTime VoucherDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(200)]
        [Display(Name = "المورد")]
        public string Payer { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; } = 0;

        [StringLength(1000)]
        [Display(Name = "البيان")]
        public string? Description { get; set; }

        [Display(Name = "الحساب البنكي")]
        public int? BankAccountId { get; set; }

        [Display(Name = "القيد اليومي")]
        public int? JournalEntryId { get; set; }

        [StringLength(50)]
        [Display(Name = "طريقة الاستلام")]
        public string ReceiptMethod { get; set; } = "نقدي"; // نقدي، شيك، تحويل بنكي

        [StringLength(50)]
        [Display(Name = "رقم الشيك/التحويل")]
        public string? CheckNumber { get; set; }

        [Display(Name = "تاريخ الشيك")]
        public DateTime? CheckDate { get; set; }

        [StringLength(100)]
        [Display(Name = "البنك المسحوب عليه")]
        public string? DraweeBank { get; set; }

        [Display(Name = "سند معتمد")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "سند مقبوض")]
        public bool IsReceived { get; set; } = false;

        [Display(Name = "تاريخ القبض")]
        public DateTime? ReceivedDate { get; set; }

        // العلاقات
        [ForeignKey("BankAccountId")]
        public virtual BankAccount? BankAccount { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }
    }
}
