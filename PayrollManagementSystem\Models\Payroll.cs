using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// أنواع المخصصات
    /// </summary>
    [Table("AllowanceTypes")]
    public class AllowanceType : CodedEntity
    {
        [StringLength(50)]
        [Display(Name = "نوع المخصص")]
        public string? AllowanceCategory { get; set; } = "ثابت"; // ثابت، متغير، نسبي

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الافتراضي")]
        public decimal DefaultAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة الافتراضية")]
        public decimal DefaultPercentage { get; set; } = 0;

        [Display(Name = "خاضع للضريبة")]
        public bool IsTaxable { get; set; } = true;

        [Display(Name = "خاضع للتقاعد")]
        public bool IsRetirementSubject { get; set; } = true;

        [Display(Name = "يظهر في كشف الراتب")]
        public bool ShowInPayslip { get; set; } = true;

        [Display(Name = "إجباري")]
        public bool IsMandatory { get; set; } = false;

        [StringLength(100)]
        [Display(Name = "الحساب المحاسبي")]
        public string? AccountCode { get; set; }

        // العلاقات
        public virtual ICollection<EmployeeAllowance> EmployeeAllowances { get; set; } = new List<EmployeeAllowance>();
    }

    /// <summary>
    /// أنواع الاستقطاعات
    /// </summary>
    [Table("DeductionTypes")]
    public class DeductionType : CodedEntity
    {
        [StringLength(50)]
        [Display(Name = "نوع الاستقطاع")]
        public string? DeductionCategory { get; set; } = "ثابت"; // ثابت، متغير، نسبي

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الافتراضي")]
        public decimal DefaultAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة الافتراضية")]
        public decimal DefaultPercentage { get; set; } = 0;

        [Display(Name = "إجباري")]
        public bool IsMandatory { get; set; } = false;

        [Display(Name = "يظهر في كشف الراتب")]
        public bool ShowInPayslip { get; set; } = true;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحد الأقصى للاستقطاع")]
        public decimal MaxDeductionAmount { get; set; } = 0;

        [StringLength(100)]
        [Display(Name = "الحساب المحاسبي")]
        public string? AccountCode { get; set; }

        // العلاقات
        public virtual ICollection<EmployeeDeduction> EmployeeDeductions { get; set; } = new List<EmployeeDeduction>();
    }

    /// <summary>
    /// مخصصات الموظفين
    /// </summary>
    [Table("EmployeeAllowances")]
    public class EmployeeAllowance : BaseEntity
    {
        [Required]
        [Display(Name = "الموظف")]
        public int EmployeeId { get; set; }

        [Required]
        [Display(Name = "نوع المخصص")]
        public int AllowanceTypeId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة")]
        public decimal Percentage { get; set; } = 0;

        [Display(Name = "تاريخ البداية")]
        public DateTime? StartDate { get; set; }

        [Display(Name = "تاريخ النهاية")]
        public DateTime? EndDate { get; set; }

        [Display(Name = "دائم")]
        public bool IsPermanent { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Remarks { get; set; }

        // العلاقات
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("AllowanceTypeId")]
        public virtual AllowanceType AllowanceType { get; set; } = null!;

        /// <summary>
        /// حساب مبلغ المخصص
        /// </summary>
        /// <param name="basicSalary">الراتب الأساسي</param>
        /// <returns>مبلغ المخصص</returns>
        public decimal CalculateAmount(decimal basicSalary)
        {
            if (Percentage > 0)
            {
                return basicSalary * (Percentage / 100);
            }
            return Amount;
        }

        /// <summary>
        /// التحقق من صحة الفترة
        /// </summary>
        /// <param name="payrollDate">تاريخ الراتب</param>
        /// <returns>صحيح إذا كان المخصص ساري</returns>
        public bool IsValidForDate(DateTime payrollDate)
        {
            if (!IsActive) return false;
            
            if (StartDate.HasValue && payrollDate < StartDate.Value) return false;
            
            if (EndDate.HasValue && payrollDate > EndDate.Value) return false;
            
            return true;
        }
    }

    /// <summary>
    /// استقطاعات الموظفين
    /// </summary>
    [Table("EmployeeDeductions")]
    public class EmployeeDeduction : BaseEntity
    {
        [Required]
        [Display(Name = "الموظف")]
        public int EmployeeId { get; set; }

        [Required]
        [Display(Name = "نوع الاستقطاع")]
        public int DeductionTypeId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة")]
        public decimal Percentage { get; set; } = 0;

        [Display(Name = "تاريخ البداية")]
        public DateTime? StartDate { get; set; }

        [Display(Name = "تاريخ النهاية")]
        public DateTime? EndDate { get; set; }

        [Display(Name = "دائم")]
        public bool IsPermanent { get; set; } = true;

        [Display(Name = "عدد الأقساط")]
        public int? NumberOfInstallments { get; set; }

        [Display(Name = "الأقساط المدفوعة")]
        public int PaidInstallments { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المتبقي")]
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Remarks { get; set; }

        // العلاقات
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("DeductionTypeId")]
        public virtual DeductionType DeductionType { get; set; } = null!;

        /// <summary>
        /// حساب مبلغ الاستقطاع
        /// </summary>
        /// <param name="basicSalary">الراتب الأساسي</param>
        /// <returns>مبلغ الاستقطاع</returns>
        public decimal CalculateAmount(decimal basicSalary)
        {
            if (Percentage > 0)
            {
                return basicSalary * (Percentage / 100);
            }
            
            if (NumberOfInstallments.HasValue && NumberOfInstallments > 0)
            {
                return Math.Min(Amount, RemainingAmount);
            }
            
            return Amount;
        }

        /// <summary>
        /// تسجيل دفع قسط
        /// </summary>
        /// <param name="paidAmount">المبلغ المدفوع</param>
        public void RecordPayment(decimal paidAmount)
        {
            PaidInstallments++;
            RemainingAmount -= paidAmount;
            
            if (RemainingAmount <= 0 || (NumberOfInstallments.HasValue && PaidInstallments >= NumberOfInstallments))
            {
                IsActive = false;
                EndDate = DateTime.Now;
            }
        }

        /// <summary>
        /// التحقق من صحة الفترة
        /// </summary>
        /// <param name="payrollDate">تاريخ الراتب</param>
        /// <returns>صحيح إذا كان الاستقطاع ساري</returns>
        public bool IsValidForDate(DateTime payrollDate)
        {
            if (!IsActive) return false;
            
            if (StartDate.HasValue && payrollDate < StartDate.Value) return false;
            
            if (EndDate.HasValue && payrollDate > EndDate.Value) return false;
            
            if (NumberOfInstallments.HasValue && PaidInstallments >= NumberOfInstallments) return false;
            
            if (RemainingAmount <= 0) return false;
            
            return true;
        }
    }

    /// <summary>
    /// سجلات الرواتب
    /// </summary>
    [Table("PayrollRecords")]
    public class PayrollRecord : BaseEntity
    {
        [Required]
        [Display(Name = "الموظف")]
        public int EmployeeId { get; set; }

        [Required]
        [Display(Name = "السنة")]
        public int PayrollYear { get; set; }

        [Required]
        [Display(Name = "الشهر")]
        public int PayrollMonth { get; set; }

        [Required]
        [Display(Name = "تاريخ الراتب")]
        public DateTime PayrollDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الراتب الأساسي")]
        public decimal BasicSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "فرق الراتب")]
        public decimal SalaryDifference { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "حصة الدائرة")]
        public decimal DepartmentShare { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "إجمالي المخصصات")]
        public decimal TotalAllowances { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "إجمالي الاستقطاعات")]
        public decimal TotalDeductions { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "إجمالي الراتب")]
        public decimal GrossSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "صافي الراتب")]
        public decimal NetSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "ضريبة الدخل")]
        public decimal IncomeTax { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "استقطاع التقاعد")]
        public decimal RetirementDeduction { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحماية الاجتماعية")]
        public decimal SocialSecurityDeduction { get; set; } = 0;

        [Display(Name = "عدد أيام العمل")]
        public int WorkingDays { get; set; } = 30;

        [Display(Name = "عدد أيام الغياب")]
        public int AbsentDays { get; set; } = 0;

        [Display(Name = "عدد ساعات الإضافي")]
        public decimal OvertimeHours { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الإضافي")]
        public decimal OvertimeAmount { get; set; } = 0;

        [Display(Name = "راتب معتمد")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "راتب مدفوع")]
        public bool IsPaid { get; set; } = false;

        [Display(Name = "تاريخ الدفع")]
        public DateTime? PaidDate { get; set; }

        [Display(Name = "القيد اليومي")]
        public int? JournalEntryId { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Remarks { get; set; }

        // العلاقات
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        public virtual ICollection<PayrollRecordDetail> Details { get; set; } = new List<PayrollRecordDetail>();

        /// <summary>
        /// حساب إجمالي الراتب
        /// </summary>
        public void CalculateGrossSalary()
        {
            GrossSalary = BasicSalary + SalaryDifference + DepartmentShare + TotalAllowances + OvertimeAmount;
        }

        /// <summary>
        /// حساب صافي الراتب
        /// </summary>
        public void CalculateNetSalary()
        {
            CalculateGrossSalary();
            NetSalary = GrossSalary - TotalDeductions - IncomeTax - RetirementDeduction - SocialSecurityDeduction;
        }

        /// <summary>
        /// حساب ضريبة الدخل
        /// </summary>
        /// <param name="taxRate">نسبة الضريبة</param>
        public void CalculateIncomeTax(decimal taxRate)
        {
            var taxableAmount = GrossSalary - RetirementDeduction;
            IncomeTax = taxableAmount * (taxRate / 100);
        }

        /// <summary>
        /// حساب استقطاع التقاعد
        /// </summary>
        /// <param name="retirementRate">نسبة التقاعد</param>
        public void CalculateRetirementDeduction(decimal retirementRate)
        {
            RetirementDeduction = (BasicSalary + SalaryDifference) * (retirementRate / 100);
        }

        /// <summary>
        /// اعتماد الراتب
        /// </summary>
        public void ApprovePayroll()
        {
            IsApproved = true;
        }

        /// <summary>
        /// دفع الراتب
        /// </summary>
        public void PaySalary()
        {
            if (IsApproved)
            {
                IsPaid = true;
                PaidDate = DateTime.Now;
            }
        }
    }

    /// <summary>
    /// تفاصيل سجلات الرواتب
    /// </summary>
    [Table("PayrollRecordDetails")]
    public class PayrollRecordDetail : BaseEntity
    {
        [Required]
        [Display(Name = "سجل الراتب")]
        public int PayrollRecordId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع البند")]
        public string ItemType { get; set; } = string.Empty; // مخصص، استقطاع

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم البند")]
        public string ItemName { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; } = 0;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Remarks { get; set; }

        // العلاقات
        [ForeignKey("PayrollRecordId")]
        public virtual PayrollRecord PayrollRecord { get; set; } = null!;
    }
}
