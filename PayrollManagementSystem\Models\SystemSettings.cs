using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// بيانات المؤسسة
    /// </summary>
    [Table("Organizations")]
    public class Organization : CodedEntity
    {
        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الفاكس")]
        public string? Fax { get; set; }

        [StringLength(200)]
        [Display(Name = "الموقع الإلكتروني")]
        public string? Website { get; set; }

        [StringLength(500)]
        [Display(Name = "مسار الشعار")]
        public string? LogoPath { get; set; }

        [StringLength(50)]
        [Display(Name = "رقم التسجيل")]
        public string? RegistrationNumber { get; set; }

        [StringLength(50)]
        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "المدير العام")]
        public string? GeneralManager { get; set; }

        [StringLength(100)]
        [Display(Name = "المدير المالي")]
        public string? FinancialManager { get; set; }

        [StringLength(100)]
        [Display(Name = "مدير الموارد البشرية")]
        public string? HRManager { get; set; }

        // العلاقات
        public virtual ICollection<Department> Departments { get; set; } = new List<Department>();
    }

    /// <summary>
    /// الدوائر
    /// </summary>
    [Table("Departments")]
    public class Department : HierarchicalEntity
    {
        [Display(Name = "المؤسسة")]
        public int? OrganizationId { get; set; }

        [StringLength(100)]
        [Display(Name = "المدير")]
        public string? Manager { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Display(Name = "نوع الدائرة")]
        public string DepartmentType { get; set; } = "إدارية"; // إدارية، فنية، مالية، خدمية

        // العلاقات
        [ForeignKey("OrganizationId")]
        public virtual Organization? Organization { get; set; }

        [ForeignKey("ParentId")]
        public virtual Department? ParentDepartment { get; set; }

        public virtual ICollection<Department> SubDepartments { get; set; } = new List<Department>();
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }

    /// <summary>
    /// العملات
    /// </summary>
    [Table("Currencies")]
    public class Currency : CodedEntity
    {
        [Required]
        [StringLength(10)]
        [Display(Name = "رمز العملة")]
        public string CurrencyCode { get; set; } = string.Empty; // IQD, USD, EUR

        [StringLength(50)]
        [Display(Name = "أجزاء العملة")]
        public string? CurrencySubunit { get; set; } = string.Empty; // فلس، سنت

        [Required]
        [StringLength(20)]
        [Display(Name = "نوع العملة")]
        public string CurrencyType { get; set; } = "محلية"; // محلية، أجنبية

        [Required]
        [Column(TypeName = "decimal(18,6)")]
        [Display(Name = "سعر الصرف")]
        public decimal ExchangeRate { get; set; } = 1;

        [Display(Name = "العملة الافتراضية")]
        public bool IsDefault { get; set; } = false;

        [StringLength(10)]
        [Display(Name = "رمز العملة للعرض")]
        public string? Symbol { get; set; }

        [Display(Name = "عدد الخانات العشرية")]
        public int DecimalPlaces { get; set; } = 2;

        [Display(Name = "تاريخ آخر تحديث للسعر")]
        public DateTime? LastRateUpdate { get; set; }

        // العلاقات
        public virtual ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();
    }

    /// <summary>
    /// الفترات المحاسبية
    /// </summary>
    [Table("AccountingPeriods")]
    public class AccountingPeriod : BaseEntity
    {
        [Required]
        [Display(Name = "السنة المالية")]
        public int FiscalYear { get; set; }

        [Required]
        [Display(Name = "من شهر")]
        public int FromMonth { get; set; }

        [Required]
        [Display(Name = "إلى شهر")]
        public int ToMonth { get; set; }

        [Required]
        [Display(Name = "عدد الأشهر")]
        public int NumberOfMonths { get; set; }

        [Required]
        [Display(Name = "تاريخ البداية")]
        public DateTime StartDate { get; set; }

        [Required]
        [Display(Name = "تاريخ النهاية")]
        public DateTime EndDate { get; set; }

        [Display(Name = "الفترة الحالية")]
        public bool IsCurrent { get; set; } = false;

        [Display(Name = "مقفلة")]
        public bool IsClosed { get; set; } = false;

        [Display(Name = "تاريخ الإقفال")]
        public DateTime? ClosedDate { get; set; }

        [StringLength(100)]
        [Display(Name = "المستخدم الذي أقفل")]
        public string? ClosedBy { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات الإقفال")]
        public string? ClosingNotes { get; set; }

        /// <summary>
        /// إقفال الفترة المحاسبية
        /// </summary>
        /// <param name="closedBy">المستخدم الذي قام بالإقفال</param>
        /// <param name="notes">ملاحظات الإقفال</param>
        public void ClosePeriod(string closedBy, string? notes = null)
        {
            IsClosed = true;
            ClosedDate = DateTime.Now;
            ClosedBy = closedBy;
            ClosingNotes = notes;
        }

        /// <summary>
        /// إعادة فتح الفترة المحاسبية
        /// </summary>
        public void ReopenPeriod()
        {
            IsClosed = false;
            ClosedDate = null;
            ClosedBy = null;
            ClosingNotes = null;
        }
    }

    /// <summary>
    /// إعدادات النظام
    /// </summary>
    [Table("SystemSettings")]
    public class SystemSetting : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الإعداد")]
        public string SettingName { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        [Display(Name = "قيمة الإعداد")]
        public string SettingValue { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "وصف الإعداد")]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع البيانات")]
        public string DataType { get; set; } = "نص"; // نص، رقم، تاريخ، منطقي، قائمة

        [StringLength(100)]
        [Display(Name = "المجموعة")]
        public string? Category { get; set; }

        [Display(Name = "قابل للتعديل")]
        public bool IsEditable { get; set; } = true;

        [Display(Name = "مطلوب")]
        public bool IsRequired { get; set; } = false;

        [StringLength(1000)]
        [Display(Name = "القيم المسموحة")]
        public string? AllowedValues { get; set; }

        [StringLength(1000)]
        [Display(Name = "القيمة الافتراضية")]
        public string? DefaultValue { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 1;

        /// <summary>
        /// الحصول على القيمة كرقم صحيح
        /// </summary>
        /// <returns>القيمة كرقم صحيح</returns>
        public int GetIntValue()
        {
            return int.TryParse(SettingValue, out int result) ? result : 0;
        }

        /// <summary>
        /// الحصول على القيمة كرقم عشري
        /// </summary>
        /// <returns>القيمة كرقم عشري</returns>
        public decimal GetDecimalValue()
        {
            return decimal.TryParse(SettingValue, out decimal result) ? result : 0;
        }

        /// <summary>
        /// الحصول على القيمة كقيمة منطقية
        /// </summary>
        /// <returns>القيمة كقيمة منطقية</returns>
        public bool GetBoolValue()
        {
            return bool.TryParse(SettingValue, out bool result) ? result : false;
        }

        /// <summary>
        /// الحصول على القيمة كتاريخ
        /// </summary>
        /// <returns>القيمة كتاريخ</returns>
        public DateTime? GetDateValue()
        {
            return DateTime.TryParse(SettingValue, out DateTime result) ? result : null;
        }
    }

    /// <summary>
    /// إعدادات الرواتب
    /// </summary>
    [Table("PayrollSettings")]
    public class PayrollSetting : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الإعداد")]
        public string SettingName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        [Display(Name = "قيمة الإعداد")]
        public string SettingValue { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع البيانات")]
        public string DataType { get; set; } = "رقم"; // نص، رقم، نسبة، مبلغ

        [Display(Name = "قابل للتعديل")]
        public bool IsEditable { get; set; } = true;

        [Display(Name = "يطبق على جميع الموظفين")]
        public bool ApplyToAllEmployees { get; set; } = true;

        [Display(Name = "تاريخ السريان")]
        public DateTime? EffectiveDate { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الحصول على القيمة كنسبة مئوية
        /// </summary>
        /// <returns>النسبة المئوية</returns>
        public decimal GetPercentageValue()
        {
            if (decimal.TryParse(SettingValue, out decimal result))
            {
                return DataType == "نسبة" ? result / 100 : result;
            }
            return 0;
        }

        /// <summary>
        /// الحصول على القيمة كمبلغ
        /// </summary>
        /// <returns>المبلغ</returns>
        public decimal GetAmountValue()
        {
            return decimal.TryParse(SettingValue, out decimal result) ? result : 0;
        }
    }
}
