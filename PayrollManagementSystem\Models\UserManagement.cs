using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace PayrollManagementSystem.Models
{
    /// <summary>
    /// مجموعة المستخدمين
    /// </summary>
    [Table("UserGroups")]
    public class UserGroup : CodedEntity
    {
        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "مستوى الصلاحية")]
        public int PermissionLevel { get; set; } = 1;

        // العلاقات
        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<UserGroupPermission> Permissions { get; set; } = new List<UserGroupPermission>();
    }

    /// <summary>
    /// المستخدمين
    /// </summary>
    [Table("Users")]
    public class User : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Display(Name = "رقم الحساب")]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الحساب")]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Display(Name = "كلمة المرور المشفرة")]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "نوع الحساب")]
        public string AccountType { get; set; } = "مستخدم"; // مدير، مستخدم، محاسب، موظف شؤون

        [Display(Name = "مجموعة المستخدم")]
        public int? UserGroupId { get; set; }

        [Display(Name = "الدائرة")]
        public int? DepartmentId { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }

        [Display(Name = "عدد محاولات الدخول الفاشلة")]
        public int FailedLoginAttempts { get; set; } = 0;

        [Display(Name = "مقفل")]
        public bool IsLocked { get; set; } = false;

        [Display(Name = "تاريخ انتهاء القفل")]
        public DateTime? LockoutEndDate { get; set; }

        [Display(Name = "يجب تغيير كلمة المرور")]
        public bool MustChangePassword { get; set; } = true;

        [Display(Name = "تاريخ انتهاء كلمة المرور")]
        public DateTime? PasswordExpiryDate { get; set; }

        // العلاقات
        [ForeignKey("UserGroupId")]
        public virtual UserGroup? UserGroup { get; set; }

        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();
        public virtual ICollection<UserActivity> Activities { get; set; } = new List<UserActivity>();

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "PayrollSystem2024"));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>صحيح إذا كانت كلمة المرور صحيحة</returns>
        public bool VerifyPassword(string password)
        {
            return PasswordHash == HashPassword(password);
        }

        /// <summary>
        /// تحديث كلمة المرور
        /// </summary>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        public void UpdatePassword(string newPassword)
        {
            PasswordHash = HashPassword(newPassword);
            MustChangePassword = false;
            PasswordExpiryDate = DateTime.Now.AddDays(90); // انتهاء كلمة المرور بعد 90 يوم
        }

        /// <summary>
        /// قفل الحساب
        /// </summary>
        /// <param name="lockoutDuration">مدة القفل</param>
        public void LockAccount(TimeSpan lockoutDuration)
        {
            IsLocked = true;
            LockoutEndDate = DateTime.Now.Add(lockoutDuration);
        }

        /// <summary>
        /// إلغاء قفل الحساب
        /// </summary>
        public void UnlockAccount()
        {
            IsLocked = false;
            LockoutEndDate = null;
            FailedLoginAttempts = 0;
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        public void RecordFailedLogin()
        {
            FailedLoginAttempts++;
            if (FailedLoginAttempts >= 5)
            {
                LockAccount(TimeSpan.FromMinutes(15));
            }
        }

        /// <summary>
        /// تسجيل دخول ناجح
        /// </summary>
        public void RecordSuccessfulLogin()
        {
            LastLoginDate = DateTime.Now;
            FailedLoginAttempts = 0;
            if (IsLocked && LockoutEndDate <= DateTime.Now)
            {
                UnlockAccount();
            }
        }
    }

    /// <summary>
    /// الصلاحيات
    /// </summary>
    [Table("Permissions")]
    public class Permission : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الصلاحية")]
        public string PermissionName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "رمز الصلاحية")]
        public string PermissionCode { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "اسم الوحدة")]
        public string ModuleName { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 1;

        // العلاقات
        public virtual ICollection<UserGroupPermission> UserGroups { get; set; } = new List<UserGroupPermission>();
    }

    /// <summary>
    /// صلاحيات مجموعة المستخدمين
    /// </summary>
    [Table("UserGroupPermissions")]
    public class UserGroupPermission : BaseEntity
    {
        [Required]
        [Display(Name = "مجموعة المستخدم")]
        public int UserGroupId { get; set; }

        [Required]
        [Display(Name = "الصلاحية")]
        public int PermissionId { get; set; }

        [Display(Name = "يمكن القراءة")]
        public bool CanRead { get; set; } = true;

        [Display(Name = "يمكن الكتابة")]
        public bool CanWrite { get; set; } = false;

        [Display(Name = "يمكن التعديل")]
        public bool CanUpdate { get; set; } = false;

        [Display(Name = "يمكن الحذف")]
        public bool CanDelete { get; set; } = false;

        [Display(Name = "يمكن الطباعة")]
        public bool CanPrint { get; set; } = true;

        [Display(Name = "يمكن التصدير")]
        public bool CanExport { get; set; } = false;

        // العلاقات
        [ForeignKey("UserGroupId")]
        public virtual UserGroup UserGroup { get; set; } = null!;

        [ForeignKey("PermissionId")]
        public virtual Permission Permission { get; set; } = null!;
    }

    /// <summary>
    /// جلسات المستخدمين
    /// </summary>
    [Table("UserSessions")]
    public class UserSession : BaseEntity
    {
        [Required]
        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        [Required]
        [StringLength(255)]
        [Display(Name = "معرف الجلسة")]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ بداية الجلسة")]
        public DateTime StartTime { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ انتهاء الجلسة")]
        public DateTime? EndTime { get; set; }

        [StringLength(50)]
        [Display(Name = "عنوان IP")]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        [Display(Name = "معلومات المتصفح")]
        public string? UserAgent { get; set; }

        [Display(Name = "جلسة نشطة")]
        public bool IsActive { get; set; } = true;

        // العلاقات
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// أنشطة المستخدمين
    /// </summary>
    [Table("UserActivities")]
    public class UserActivity : BaseEntity
    {
        [Required]
        [Display(Name = "المستخدم")]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "نوع النشاط")]
        public string ActivityType { get; set; } = string.Empty; // Login, Logout, Create, Update, Delete, View, Print, Export

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الجدول")]
        public string TableName { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "معرف السجل")]
        public string? RecordId { get; set; }

        [StringLength(1000)]
        [Display(Name = "تفاصيل النشاط")]
        public string? ActivityDetails { get; set; }

        [StringLength(50)]
        [Display(Name = "عنوان IP")]
        public string? IpAddress { get; set; }

        [Display(Name = "تاريخ النشاط")]
        public DateTime ActivityDate { get; set; } = DateTime.Now;

        // العلاقات
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
