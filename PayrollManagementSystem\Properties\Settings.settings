<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="PayrollManagementSystem.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="SavedUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RememberUser" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1200</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">800</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.String" Scope="User">
      <Value Profile="(Default)">Maximized</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar-IQ</Value>
    </Setting>
  </Settings>
</SettingsFile>
