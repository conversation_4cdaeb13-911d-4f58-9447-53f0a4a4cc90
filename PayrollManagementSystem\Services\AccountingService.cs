using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة المحاسبة
    /// </summary>
    public class AccountingService : IAccountingService
    {
        private readonly PayrollDbContext _context;
        private readonly ILogger<AccountingService> _logger;

        public AccountingService(PayrollDbContext context, ILogger<AccountingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// إنشاء قيد يومي
        /// </summary>
        public async Task<JournalEntry> CreateJournalEntryAsync(JournalEntry journalEntry)
        {
            try
            {
                // التحقق من توازن القيد
                if (!journalEntry.IsBalanced())
                {
                    throw new InvalidOperationException("القيد غير متوازن");
                }

                // توليد رقم القيد
                var lastEntry = await _context.JournalEntries
                    .OrderByDescending(je => je.Id)
                    .FirstOrDefaultAsync();

                var entryNumber = (lastEntry?.Id ?? 0) + 1;
                journalEntry.EntryNumber = $"JE{entryNumber:D6}";

                _context.JournalEntries.Add(journalEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء قيد يومي جديد: {EntryNumber}", journalEntry.EntryNumber);
                return journalEntry;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء القيد اليومي");
                throw;
            }
        }

        /// <summary>
        /// اعتماد قيد يومي
        /// </summary>
        public async Task<bool> ApproveJournalEntryAsync(int journalEntryId, string approvedBy)
        {
            try
            {
                var journalEntry = await _context.JournalEntries
                    .Include(je => je.Details)
                    .FirstOrDefaultAsync(je => je.Id == journalEntryId);

                if (journalEntry == null)
                {
                    return false;
                }

                journalEntry.ApproveEntry(approvedBy);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم اعتماد القيد اليومي: {EntryNumber}", journalEntry.EntryNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد القيد اليومي: {JournalEntryId}", journalEntryId);
                throw;
            }
        }

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        public async Task<bool> PostJournalEntryAsync(int journalEntryId, string postedBy)
        {
            try
            {
                var journalEntry = await _context.JournalEntries
                    .Include(je => je.Details)
                    .ThenInclude(jed => jed.Account)
                    .FirstOrDefaultAsync(je => je.Id == journalEntryId);

                if (journalEntry == null || !journalEntry.IsApproved)
                {
                    return false;
                }

                // ترحيل القيد
                journalEntry.PostEntry(postedBy);

                // تحديث أرصدة الحسابات
                foreach (var detail in journalEntry.Details)
                {
                    detail.Account.UpdateBalance(detail.DebitAmount, detail.CreditAmount);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم ترحيل القيد اليومي: {EntryNumber}", journalEntry.EntryNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في ترحيل القيد اليومي: {JournalEntryId}", journalEntryId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على دليل الحسابات
        /// </summary>
        public async Task<List<ChartOfAccount>> GetChartOfAccountsAsync()
        {
            try
            {
                return await _context.ChartOfAccounts
                    .Include(c => c.Currency)
                    .Include(c => c.ParentAccount)
                    .Where(c => !c.IsDeleted)
                    .OrderBy(c => c.Code)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على دليل الحسابات");
                throw;
            }
        }

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        public async Task<ChartOfAccount> CreateAccountAsync(ChartOfAccount account)
        {
            try
            {
                // التحقق من عدم وجود حساب بنفس الرمز
                var existingAccount = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(c => c.Code == account.Code);

                if (existingAccount != null)
                {
                    throw new InvalidOperationException("رمز الحساب موجود مسبقاً");
                }

                // تحديث المسار الهرمي
                if (account.ParentId.HasValue)
                {
                    var parentAccount = await _context.ChartOfAccounts
                        .FindAsync(account.ParentId.Value);

                    if (parentAccount != null)
                    {
                        account.UpdateHierarchyPath(parentAccount.HierarchyPath);
                    }
                }
                else
                {
                    account.UpdateHierarchyPath();
                }

                _context.ChartOfAccounts.Add(account);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء حساب جديد: {AccountCode}", account.Code);
                return account;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الحساب: {AccountCode}", account.Code);
                throw;
            }
        }
    }
}
