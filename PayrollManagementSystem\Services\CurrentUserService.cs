using PayrollManagementSystem.Models;
using System;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة المستخدم الحالي
    /// </summary>
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IUserService _userService;
        private User? _currentUser;

        public CurrentUserService(IUserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User? CurrentUser => _currentUser;

        /// <summary>
        /// معرف المستخدم الحالي
        /// </summary>
        public int? CurrentUserId => _currentUser?.Id;

        /// <summary>
        /// اسم المستخدم الحالي
        /// </summary>
        public string? CurrentUsername => _currentUser?.Username;

        /// <summary>
        /// تعيين المستخدم الحالي
        /// </summary>
        public async Task SetCurrentUserAsync(User user)
        {
            _currentUser = user;
            
            // إنشاء جلسة جديدة
            var sessionId = Guid.NewGuid().ToString();
            await _userService.CreateSessionAsync(user.Id, sessionId);
        }

        /// <summary>
        /// مسح المستخدم الحالي
        /// </summary>
        public void ClearCurrentUser()
        {
            _currentUser = null;
        }

        /// <summary>
        /// التحقق من تسجيل الدخول
        /// </summary>
        public bool IsLoggedIn()
        {
            return _currentUser != null && _currentUser.IsActive && !_currentUser.IsDeleted;
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        public async Task<bool> HasPermissionAsync(string permissionCode)
        {
            if (!IsLoggedIn() || CurrentUserId == null)
            {
                return false;
            }

            return await _userService.HasPermissionAsync(CurrentUserId.Value, permissionCode);
        }

        /// <summary>
        /// التحقق من نوع المستخدم
        /// </summary>
        public bool IsUserType(string accountType)
        {
            return IsLoggedIn() && 
                   string.Equals(_currentUser?.AccountType, accountType, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// التحقق من كون المستخدم مدير
        /// </summary>
        public bool IsAdmin()
        {
            return IsUserType("مدير");
        }

        /// <summary>
        /// الحصول على اسم المستخدم للعرض
        /// </summary>
        public string GetDisplayName()
        {
            if (!IsLoggedIn())
            {
                return "غير مسجل";
            }

            return _currentUser?.AccountName ?? _currentUser?.Username ?? "مستخدم";
        }
    }
}
