using Microsoft.Win32;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة الحوارات
    /// </summary>
    public class DialogService : IDialogService
    {
        /// <summary>
        /// إظهار رسالة معلومات
        /// </summary>
        public Task ShowInformationAsync(string message, string title = "معلومات")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            return Task.CompletedTask;
        }

        /// <summary>
        /// إظهار رسالة تحذير
        /// </summary>
        public Task ShowWarningAsync(string message, string title = "تحذير")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            return Task.CompletedTask;
        }

        /// <summary>
        /// إظهار رسالة خطأ
        /// </summary>
        public Task ShowErrorAsync(string message, string title = "خطأ")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            return Task.CompletedTask;
        }

        /// <summary>
        /// إظهار رسالة تأكيد
        /// </summary>
        public Task<bool> ShowConfirmationAsync(string message, string title = "تأكيد")
        {
            var result = System.Windows.MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return Task.FromResult(result == MessageBoxResult.Yes);
        }

        /// <summary>
        /// إظهار حوار اختيار ملف
        /// </summary>
        public Task<string?> ShowOpenFileDialogAsync(string filter = "جميع الملفات (*.*)|*.*")
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = filter,
                Title = "اختيار ملف"
            };

            var result = dialog.ShowDialog();
            return Task.FromResult(result == true ? dialog.FileName : null);
        }

        /// <summary>
        /// إظهار حوار حفظ ملف
        /// </summary>
        public Task<string?> ShowSaveFileDialogAsync(string filter = "جميع الملفات (*.*)|*.*")
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = filter,
                Title = "حفظ ملف"
            };

            var result = dialog.ShowDialog();
            return Task.FromResult(result == true ? dialog.FileName : null);
        }

        /// <summary>
        /// إظهار حوار اختيار مجلد
        /// </summary>
        public Task<string?> ShowFolderBrowserDialogAsync()
        {
            using var dialog = new FolderBrowserDialog
            {
                Description = "اختيار مجلد",
                UseDescriptionForTitle = true
            };

            var result = dialog.ShowDialog();
            return Task.FromResult(result == DialogResult.OK ? dialog.SelectedPath : null);
        }
    }
}
