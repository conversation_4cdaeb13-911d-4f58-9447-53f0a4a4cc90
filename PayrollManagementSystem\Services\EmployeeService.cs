using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الموظفين
    /// </summary>
    public class EmployeeService : IEmployeeService
    {
        private readonly PayrollDbContext _context;
        private readonly ILogger<EmployeeService> _logger;

        public EmployeeService(PayrollDbContext context, ILogger<EmployeeService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الموظفين
        /// </summary>
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            try
            {
                return await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.JobGrade)
                    .Include(e => e.JobStage)
                    .Include(e => e.JobTitle)
                    .Include(e => e.Qualification)
                    .Where(e => !e.IsDeleted)
                    .OrderBy(e => e.EmployeeNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الموظفين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على موظف بواسطة المعرف
        /// </summary>
        public async Task<Employee?> GetEmployeeByIdAsync(int employeeId)
        {
            try
            {
                return await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.JobGrade)
                    .Include(e => e.JobStage)
                    .Include(e => e.JobTitle)
                    .Include(e => e.Qualification)
                    .Include(e => e.Allowances)
                    .ThenInclude(a => a.AllowanceType)
                    .Include(e => e.Deductions)
                    .ThenInclude(d => d.DeductionType)
                    .FirstOrDefaultAsync(e => e.Id == employeeId && !e.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الموظف بالمعرف: {EmployeeId}", employeeId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على موظف بواسطة الرقم الوظيفي
        /// </summary>
        public async Task<Employee?> GetEmployeeByNumberAsync(string employeeNumber)
        {
            try
            {
                return await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.JobGrade)
                    .Include(e => e.JobStage)
                    .Include(e => e.JobTitle)
                    .Include(e => e.Qualification)
                    .FirstOrDefaultAsync(e => e.EmployeeNumber == employeeNumber && !e.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الموظف بالرقم الوظيفي: {EmployeeNumber}", employeeNumber);
                throw;
            }
        }

        /// <summary>
        /// إنشاء موظف جديد
        /// </summary>
        public async Task<Employee> CreateEmployeeAsync(Employee employee)
        {
            try
            {
                // التحقق من عدم وجود موظف بنفس الرقم الوظيفي
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.EmployeeNumber == employee.EmployeeNumber);

                if (existingEmployee != null)
                {
                    throw new InvalidOperationException("الرقم الوظيفي موجود مسبقاً");
                }

                // التحقق من رقم الآيبان إذا كان موجوداً
                if (!string.IsNullOrEmpty(employee.IbanNumber))
                {
                    existingEmployee = await _context.Employees
                        .FirstOrDefaultAsync(e => e.IbanNumber == employee.IbanNumber);

                    if (existingEmployee != null)
                    {
                        throw new InvalidOperationException("رقم الآيبان موجود مسبقاً");
                    }
                }

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء موظف جديد: {EmployeeNumber}", employee.EmployeeNumber);
                return employee;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الموظف: {EmployeeNumber}", employee.EmployeeNumber);
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الموظف
        /// </summary>
        public async Task<Employee> UpdateEmployeeAsync(Employee employee)
        {
            try
            {
                var existingEmployee = await _context.Employees.FindAsync(employee.Id);
                if (existingEmployee == null)
                {
                    throw new InvalidOperationException("الموظف غير موجود");
                }

                // تحديث البيانات
                existingEmployee.FullName = employee.FullName;
                existingEmployee.FirstName = employee.FirstName;
                existingEmployee.FatherName = employee.FatherName;
                existingEmployee.GrandFatherName = employee.GrandFatherName;
                existingEmployee.LastName = employee.LastName;
                existingEmployee.IbanNumber = employee.IbanNumber;
                existingEmployee.HireDate = employee.HireDate;
                existingEmployee.BirthDate = employee.BirthDate;
                existingEmployee.Gender = employee.Gender;
                existingEmployee.MaritalStatus = employee.MaritalStatus;
                existingEmployee.NumberOfChildren = employee.NumberOfChildren;
                existingEmployee.DepartmentId = employee.DepartmentId;
                existingEmployee.JobGradeId = employee.JobGradeId;
                existingEmployee.JobStageId = employee.JobStageId;
                existingEmployee.JobTitleId = employee.JobTitleId;
                existingEmployee.QualificationId = employee.QualificationId;
                existingEmployee.EmployeeStatus = employee.EmployeeStatus;
                existingEmployee.BasicSalary = employee.BasicSalary;
                existingEmployee.Phone = employee.Phone;
                existingEmployee.Email = employee.Email;
                existingEmployee.Address = employee.Address;
                existingEmployee.NationalId = employee.NationalId;
                existingEmployee.PassportNumber = employee.PassportNumber;
                existingEmployee.UpdateModificationInfo(employee.ModifiedBy ?? "System");

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث بيانات الموظف: {EmployeeNumber}", existingEmployee.EmployeeNumber);
                return existingEmployee;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الموظف: {EmployeeId}", employee.Id);
                throw;
            }
        }

        /// <summary>
        /// حذف موظف
        /// </summary>
        public async Task<bool> DeleteEmployeeAsync(int employeeId)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(employeeId);
                if (employee == null)
                {
                    return false;
                }

                // حذف منطقي
                employee.SoftDelete("System");
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف الموظف: {EmployeeId}", employeeId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الموظف: {EmployeeId}", employeeId);
                throw;
            }
        }

        /// <summary>
        /// البحث في الموظفين
        /// </summary>
        public async Task<List<Employee>> SearchEmployeesAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return await GetAllEmployeesAsync();
                }

                return await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.JobGrade)
                    .Include(e => e.JobStage)
                    .Include(e => e.JobTitle)
                    .Include(e => e.Qualification)
                    .Where(e => !e.IsDeleted &&
                               (e.FullName.Contains(searchTerm) ||
                                e.EmployeeNumber.Contains(searchTerm) ||
                                (e.IbanNumber != null && e.IbanNumber.Contains(searchTerm))))
                    .OrderBy(e => e.EmployeeNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في الموظفين: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// الحصول على موظفي دائرة معينة
        /// </summary>
        public async Task<List<Employee>> GetEmployeesByDepartmentAsync(int departmentId)
        {
            try
            {
                return await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.JobGrade)
                    .Include(e => e.JobStage)
                    .Include(e => e.JobTitle)
                    .Include(e => e.Qualification)
                    .Where(e => e.DepartmentId == departmentId && !e.IsDeleted)
                    .OrderBy(e => e.EmployeeNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على موظفي الدائرة: {DepartmentId}", departmentId);
                throw;
            }
        }
    }
}
