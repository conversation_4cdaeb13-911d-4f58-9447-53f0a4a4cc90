using PayrollManagementSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة المحاسبة
    /// </summary>
    public interface IAccountingService
    {
        /// <summary>
        /// إنشاء قيد يومي
        /// </summary>
        Task<JournalEntry> CreateJournalEntryAsync(JournalEntry journalEntry);

        /// <summary>
        /// اعتماد قيد يومي
        /// </summary>
        Task<bool> ApproveJournalEntryAsync(int journalEntryId, string approvedBy);

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        Task<bool> PostJournalEntryAsync(int journalEntryId, string postedBy);

        /// <summary>
        /// الحصول على دليل الحسابات
        /// </summary>
        Task<List<ChartOfAccount>> GetChartOfAccountsAsync();

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        Task<ChartOfAccount> CreateAccountAsync(ChartOfAccount account);
    }
}
