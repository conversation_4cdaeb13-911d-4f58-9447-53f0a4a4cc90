using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة النسخ الاحتياطي
    /// </summary>
    public interface IBackupService
    {
        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        Task<bool> CreateBackupAsync(string backupPath);

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        Task<bool> RestoreBackupAsync(string backupPath);
    }
}
