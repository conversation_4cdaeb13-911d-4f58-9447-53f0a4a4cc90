using PayrollManagementSystem.Models;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة المستخدم الحالي
    /// </summary>
    public interface ICurrentUserService
    {
        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        User? CurrentUser { get; }

        /// <summary>
        /// معرف المستخدم الحالي
        /// </summary>
        int? CurrentUserId { get; }

        /// <summary>
        /// اسم المستخدم الحالي
        /// </summary>
        string? CurrentUsername { get; }

        /// <summary>
        /// تعيين المستخدم الحالي
        /// </summary>
        /// <param name="user">المستخدم</param>
        Task SetCurrentUserAsync(User user);

        /// <summary>
        /// مسح المستخدم الحالي
        /// </summary>
        void ClearCurrentUser();

        /// <summary>
        /// التحقق من تسجيل الدخول
        /// </summary>
        /// <returns>true إذا كان المستخدم مسجل الدخول</returns>
        bool IsLoggedIn();

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        /// <param name="permissionCode">رمز الصلاحية</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        Task<bool> HasPermissionAsync(string permissionCode);

        /// <summary>
        /// التحقق من نوع المستخدم
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>true إذا كان المستخدم من النوع المحدد</returns>
        bool IsUserType(string accountType);

        /// <summary>
        /// التحقق من كون المستخدم مدير
        /// </summary>
        /// <returns>true إذا كان المستخدم مدير</returns>
        bool IsAdmin();

        /// <summary>
        /// الحصول على اسم المستخدم للعرض
        /// </summary>
        /// <returns>اسم المستخدم للعرض</returns>
        string GetDisplayName();
    }
}
