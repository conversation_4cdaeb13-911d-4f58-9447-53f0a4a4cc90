using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة الحوارات
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// إظهار رسالة معلومات
        /// </summary>
        Task ShowInformationAsync(string message, string title = "معلومات");

        /// <summary>
        /// إظهار رسالة تحذير
        /// </summary>
        Task ShowWarningAsync(string message, string title = "تحذير");

        /// <summary>
        /// إظهار رسالة خطأ
        /// </summary>
        Task ShowErrorAsync(string message, string title = "خطأ");

        /// <summary>
        /// إظهار رسالة تأكيد
        /// </summary>
        Task<bool> ShowConfirmationAsync(string message, string title = "تأكيد");

        /// <summary>
        /// إظهار حوار اختيار ملف
        /// </summary>
        Task<string?> ShowOpenFileDialogAsync(string filter = "جميع الملفات (*.*)|*.*");

        /// <summary>
        /// إظهار حوار حفظ ملف
        /// </summary>
        Task<string?> ShowSaveFileDialogAsync(string filter = "جميع الملفات (*.*)|*.*");

        /// <summary>
        /// إظهار حوار اختيار مجلد
        /// </summary>
        Task<string?> ShowFolderBrowserDialogAsync();
    }
}
