using PayrollManagementSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الموظفين
    /// </summary>
    public interface IEmployeeService
    {
        /// <summary>
        /// الحصول على جميع الموظفين
        /// </summary>
        Task<List<Employee>> GetAllEmployeesAsync();

        /// <summary>
        /// الحصول على موظف بواسطة المعرف
        /// </summary>
        Task<Employee?> GetEmployeeByIdAsync(int employeeId);

        /// <summary>
        /// الحصول على موظف بواسطة الرقم الوظيفي
        /// </summary>
        Task<Employee?> GetEmployeeByNumberAsync(string employeeNumber);

        /// <summary>
        /// إنشاء موظف جديد
        /// </summary>
        Task<Employee> CreateEmployeeAsync(Employee employee);

        /// <summary>
        /// تحديث بيانات الموظف
        /// </summary>
        Task<Employee> UpdateEmployeeAsync(Employee employee);

        /// <summary>
        /// حذف موظف
        /// </summary>
        Task<bool> DeleteEmployeeAsync(int employeeId);

        /// <summary>
        /// البحث في الموظفين
        /// </summary>
        Task<List<Employee>> SearchEmployeesAsync(string searchTerm);

        /// <summary>
        /// الحصول على موظفي دائرة معينة
        /// </summary>
        Task<List<Employee>> GetEmployeesByDepartmentAsync(int departmentId);
    }
}
