using System;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة التنقل
    /// </summary>
    public interface INavigationService
    {
        /// <summary>
        /// الانتقال إلى صفحة
        /// </summary>
        void NavigateTo(string pageName, object? parameter = null);

        /// <summary>
        /// الانتقال إلى صفحة بواسطة النوع
        /// </summary>
        void NavigateTo<T>(object? parameter = null) where T : class;

        /// <summary>
        /// العودة للصفحة السابقة
        /// </summary>
        void GoBack();

        /// <summary>
        /// التحقق من إمكانية العودة
        /// </summary>
        bool CanGoBack { get; }

        /// <summary>
        /// حدث تغيير الصفحة
        /// </summary>
        event EventHandler<NavigationEventArgs>? Navigated;
    }

    /// <summary>
    /// معاملات حدث التنقل
    /// </summary>
    public class NavigationEventArgs : EventArgs
    {
        public string PageName { get; set; } = string.Empty;
        public object? Parameter { get; set; }
    }
}
