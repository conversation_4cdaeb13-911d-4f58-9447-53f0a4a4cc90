using PayrollManagementSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الرواتب
    /// </summary>
    public interface IPayrollService
    {
        /// <summary>
        /// حساب راتب موظف لشهر معين
        /// </summary>
        Task<PayrollRecord> CalculatePayrollAsync(int employeeId, int year, int month);

        /// <summary>
        /// حساب رواتب جميع الموظفين لشهر معين
        /// </summary>
        Task<List<PayrollRecord>> CalculateAllPayrollsAsync(int year, int month);

        /// <summary>
        /// الحصول على سجل راتب
        /// </summary>
        Task<PayrollRecord?> GetPayrollRecordAsync(int employeeId, int year, int month);

        /// <summary>
        /// اعتماد راتب
        /// </summary>
        Task<bool> ApprovePayrollAsync(int payrollRecordId);

        /// <summary>
        /// دفع راتب
        /// </summary>
        Task<bool> PaySalaryAsync(int payrollRecordId);

        /// <summary>
        /// الحصول على رواتب شهر معين
        /// </summary>
        Task<List<PayrollRecord>> GetMonthlyPayrollsAsync(int year, int month);
    }
}
