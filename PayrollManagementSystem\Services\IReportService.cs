using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة التقارير
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// تقرير كشف الرواتب الشهري
        /// </summary>
        Task<byte[]> GenerateMonthlyPayrollReportAsync(int year, int month);

        /// <summary>
        /// تقرير الميزان التجريبي
        /// </summary>
        Task<byte[]> GenerateTrialBalanceReportAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تقرير بيانات الموظفين
        /// </summary>
        Task<byte[]> GenerateEmployeeReportAsync();
    }
}
