using PayrollManagementSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة الإعدادات
    /// </summary>
    public interface ISettingsService
    {
        /// <summary>
        /// الحصول على إعداد النظام
        /// </summary>
        Task<SystemSetting?> GetSystemSettingAsync(string settingName);

        /// <summary>
        /// تحديث إعداد النظام
        /// </summary>
        Task<bool> UpdateSystemSettingAsync(string settingName, string settingValue);

        /// <summary>
        /// الحصول على جميع إعدادات النظام
        /// </summary>
        Task<List<SystemSetting>> GetAllSystemSettingsAsync();

        /// <summary>
        /// الحصول على إعدادات الرواتب
        /// </summary>
        Task<List<PayrollSetting>> GetPayrollSettingsAsync();
    }
}
