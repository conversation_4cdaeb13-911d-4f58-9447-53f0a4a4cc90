using PayrollManagementSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة إدارة المستخدمين
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>المستخدم إذا كانت البيانات صحيحة، null إذا كانت خاطئة</returns>
        Task<User?> AuthenticateAsync(string username, string password);

        /// <summary>
        /// الحصول على مستخدم بواسطة المعرف
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المستخدم</returns>
        Task<User?> GetUserByIdAsync(int userId);

        /// <summary>
        /// الحصول على مستخدم بواسطة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم</returns>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        Task<List<User>> GetAllUsersAsync();

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>المستخدم المنشأ</returns>
        Task<User> CreateUserAsync(User user);

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <returns>المستخدم المحدث</returns>
        Task<User> UpdateUserAsync(User user);

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> DeleteUserAsync(int userId);

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword);

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        Task<bool> ResetPasswordAsync(int userId, string newPassword);

        /// <summary>
        /// قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="lockoutDuration">مدة القفل</param>
        /// <returns>true إذا تم القفل بنجاح</returns>
        Task<bool> LockUserAsync(int userId, TimeSpan lockoutDuration);

        /// <summary>
        /// إلغاء قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم إلغاء القفل بنجاح</returns>
        Task<bool> UnlockUserAsync(int userId);

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="tableName">اسم الجدول</param>
        /// <param name="recordId">معرف السجل</param>
        /// <param name="details">تفاصيل النشاط</param>
        /// <returns>true إذا تم التسجيل بنجاح</returns>
        Task<bool> LogUserActivityAsync(int userId, string activityType, string tableName, string? recordId = null, string? details = null);

        /// <summary>
        /// الحصول على أنشطة المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة الأنشطة</returns>
        Task<List<UserActivity>> GetUserActivitiesAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// إنشاء جلسة مستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="sessionId">معرف الجلسة</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <returns>الجلسة المنشأة</returns>
        Task<UserSession> CreateSessionAsync(int userId, string sessionId, string? ipAddress = null, string? userAgent = null);

        /// <summary>
        /// إنهاء جلسة المستخدم
        /// </summary>
        /// <param name="sessionId">معرف الجلسة</param>
        /// <returns>true إذا تم الإنهاء بنجاح</returns>
        Task<bool> EndSessionAsync(string sessionId);

        /// <summary>
        /// الحصول على الجلسات النشطة للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الجلسات النشطة</returns>
        Task<List<UserSession>> GetActiveSessionsAsync(int userId);

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="permissionCode">رمز الصلاحية</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        Task<bool> HasPermissionAsync(int userId, string permissionCode);

        /// <summary>
        /// الحصول على صلاحيات المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الصلاحيات</returns>
        Task<List<Permission>> GetUserPermissionsAsync(int userId);
    }
}
