using System;
using System.Collections.Generic;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة التنقل
    /// </summary>
    public class NavigationService : INavigationService
    {
        private readonly Stack<NavigationItem> _navigationStack = new();

        /// <summary>
        /// التحقق من إمكانية العودة
        /// </summary>
        public bool CanGoBack => _navigationStack.Count > 1;

        /// <summary>
        /// حدث تغيير الصفحة
        /// </summary>
        public event EventHandler<NavigationEventArgs>? Navigated;

        /// <summary>
        /// الانتقال إلى صفحة
        /// </summary>
        public void NavigateTo(string pageName, object? parameter = null)
        {
            var navigationItem = new NavigationItem
            {
                PageName = pageName,
                Parameter = parameter
            };

            _navigationStack.Push(navigationItem);

            var args = new NavigationEventArgs
            {
                PageName = pageName,
                Parameter = parameter
            };

            Navigated?.Invoke(this, args);
        }

        /// <summary>
        /// الانتقال إلى صفحة بواسطة النوع
        /// </summary>
        public void NavigateTo<T>(object? parameter = null) where T : class
        {
            NavigateTo(typeof(T).Name, parameter);
        }

        /// <summary>
        /// العودة للصفحة السابقة
        /// </summary>
        public void GoBack()
        {
            if (!CanGoBack) return;

            // إزالة الصفحة الحالية
            _navigationStack.Pop();

            // الحصول على الصفحة السابقة
            var previousPage = _navigationStack.Peek();

            var args = new NavigationEventArgs
            {
                PageName = previousPage.PageName,
                Parameter = previousPage.Parameter
            };

            Navigated?.Invoke(this, args);
        }

        /// <summary>
        /// عنصر التنقل
        /// </summary>
        private class NavigationItem
        {
            public string PageName { get; set; } = string.Empty;
            public object? Parameter { get; set; }
        }
    }
}
