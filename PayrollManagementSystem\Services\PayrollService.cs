using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الرواتب
    /// </summary>
    public class PayrollService : IPayrollService
    {
        private readonly PayrollDbContext _context;
        private readonly ILogger<PayrollService> _logger;

        public PayrollService(PayrollDbContext context, ILogger<PayrollService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// حساب راتب موظف لشهر معين
        /// </summary>
        public async Task<PayrollRecord> CalculatePayrollAsync(int employeeId, int year, int month)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Allowances.Where(a => a.IsActive))
                    .ThenInclude(a => a.AllowanceType)
                    .Include(e => e.Deductions.Where(d => d.IsActive))
                    .ThenInclude(d => d.DeductionType)
                    .FirstOrDefaultAsync(e => e.Id == employeeId);

                if (employee == null)
                {
                    throw new InvalidOperationException("الموظف غير موجود");
                }

                var payrollDate = new DateTime(year, month, 1);

                // التحقق من وجود راتب مسبق
                var existingPayroll = await _context.PayrollRecords
                    .FirstOrDefaultAsync(pr => pr.EmployeeId == employeeId && 
                                              pr.PayrollYear == year && 
                                              pr.PayrollMonth == month);

                if (existingPayroll != null)
                {
                    return existingPayroll;
                }

                // إنشاء سجل راتب جديد
                var payrollRecord = new PayrollRecord
                {
                    EmployeeId = employeeId,
                    PayrollYear = year,
                    PayrollMonth = month,
                    PayrollDate = payrollDate,
                    BasicSalary = employee.BasicSalary,
                    CreatedBy = "System"
                };

                // حساب المخصصات
                decimal totalAllowances = 0;
                foreach (var allowance in employee.Allowances.Where(a => a.IsValidForDate(payrollDate)))
                {
                    var amount = allowance.CalculateAmount(employee.BasicSalary);
                    totalAllowances += amount;

                    // إضافة تفاصيل المخصص
                    payrollRecord.Details.Add(new PayrollRecordDetail
                    {
                        ItemType = "مخصص",
                        ItemName = allowance.AllowanceType.Name,
                        Amount = amount,
                        CreatedBy = "System"
                    });
                }
                payrollRecord.TotalAllowances = totalAllowances;

                // حساب الاستقطاعات
                decimal totalDeductions = 0;
                foreach (var deduction in employee.Deductions.Where(d => d.IsValidForDate(payrollDate)))
                {
                    var amount = deduction.CalculateAmount(employee.BasicSalary);
                    totalDeductions += amount;

                    // تسجيل دفع قسط إذا كان استقطاع بأقساط
                    if (deduction.NumberOfInstallments.HasValue)
                    {
                        deduction.RecordPayment(amount);
                    }

                    // إضافة تفاصيل الاستقطاع
                    payrollRecord.Details.Add(new PayrollRecordDetail
                    {
                        ItemType = "استقطاع",
                        ItemName = deduction.DeductionType.Name,
                        Amount = amount,
                        CreatedBy = "System"
                    });
                }
                payrollRecord.TotalDeductions = totalDeductions;

                // حساب استقطاع التقاعد (5% من الراتب الأساسي)
                payrollRecord.CalculateRetirementDeduction(5);

                // حساب ضريبة الدخل (حسب الشرائح)
                payrollRecord.CalculateIncomeTax(GetTaxRate(payrollRecord.GrossSalary));

                // حساب صافي الراتب
                payrollRecord.CalculateNetSalary();

                _context.PayrollRecords.Add(payrollRecord);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حساب راتب الموظف {EmployeeId} للشهر {Month}/{Year}", 
                    employeeId, month, year);

                return payrollRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب راتب الموظف {EmployeeId} للشهر {Month}/{Year}", 
                    employeeId, month, year);
                throw;
            }
        }

        /// <summary>
        /// حساب رواتب جميع الموظفين لشهر معين
        /// </summary>
        public async Task<List<PayrollRecord>> CalculateAllPayrollsAsync(int year, int month)
        {
            try
            {
                var employees = await _context.Employees
                    .Where(e => e.IsActive && !e.IsDeleted && e.EmployeeStatus == "مستمر")
                    .ToListAsync();

                var payrollRecords = new List<PayrollRecord>();

                foreach (var employee in employees)
                {
                    try
                    {
                        var payrollRecord = await CalculatePayrollAsync(employee.Id, year, month);
                        payrollRecords.Add(payrollRecord);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حساب راتب الموظف {EmployeeId}", employee.Id);
                    }
                }

                _logger.LogInformation("تم حساب رواتب {Count} موظف للشهر {Month}/{Year}", 
                    payrollRecords.Count, month, year);

                return payrollRecords;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب رواتب جميع الموظفين للشهر {Month}/{Year}", month, year);
                throw;
            }
        }

        /// <summary>
        /// الحصول على سجل راتب
        /// </summary>
        public async Task<PayrollRecord?> GetPayrollRecordAsync(int employeeId, int year, int month)
        {
            try
            {
                return await _context.PayrollRecords
                    .Include(pr => pr.Employee)
                    .Include(pr => pr.Details)
                    .FirstOrDefaultAsync(pr => pr.EmployeeId == employeeId && 
                                              pr.PayrollYear == year && 
                                              pr.PayrollMonth == month);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على سجل راتب الموظف {EmployeeId} للشهر {Month}/{Year}", 
                    employeeId, month, year);
                throw;
            }
        }

        /// <summary>
        /// اعتماد راتب
        /// </summary>
        public async Task<bool> ApprovePayrollAsync(int payrollRecordId)
        {
            try
            {
                var payrollRecord = await _context.PayrollRecords.FindAsync(payrollRecordId);
                if (payrollRecord == null)
                {
                    return false;
                }

                payrollRecord.ApprovePayroll();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم اعتماد راتب السجل {PayrollRecordId}", payrollRecordId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد راتب السجل {PayrollRecordId}", payrollRecordId);
                throw;
            }
        }

        /// <summary>
        /// دفع راتب
        /// </summary>
        public async Task<bool> PaySalaryAsync(int payrollRecordId)
        {
            try
            {
                var payrollRecord = await _context.PayrollRecords.FindAsync(payrollRecordId);
                if (payrollRecord == null)
                {
                    return false;
                }

                payrollRecord.PaySalary();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم دفع راتب السجل {PayrollRecordId}", payrollRecordId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في دفع راتب السجل {PayrollRecordId}", payrollRecordId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على رواتب شهر معين
        /// </summary>
        public async Task<List<PayrollRecord>> GetMonthlyPayrollsAsync(int year, int month)
        {
            try
            {
                return await _context.PayrollRecords
                    .Include(pr => pr.Employee)
                    .ThenInclude(e => e.Department)
                    .Where(pr => pr.PayrollYear == year && pr.PayrollMonth == month)
                    .OrderBy(pr => pr.Employee.EmployeeNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رواتب الشهر {Month}/{Year}", month, year);
                throw;
            }
        }

        /// <summary>
        /// حساب نسبة الضريبة حسب الراتب
        /// </summary>
        private decimal GetTaxRate(decimal grossSalary)
        {
            // شرائح ضريبة الدخل العراقية (مبسطة)
            if (grossSalary <= 250000) return 0; // معفى من الضريبة
            if (grossSalary <= 500000) return 3;
            if (grossSalary <= 1000000) return 5;
            if (grossSalary <= 1500000) return 10;
            return 15; // أعلى شريحة
        }
    }
}
