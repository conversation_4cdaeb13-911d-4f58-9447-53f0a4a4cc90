using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة الإعدادات
    /// </summary>
    public class SettingsService : ISettingsService
    {
        private readonly PayrollDbContext _context;
        private readonly ILogger<SettingsService> _logger;

        public SettingsService(PayrollDbContext context, ILogger<SettingsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على إعداد النظام
        /// </summary>
        public async Task<SystemSetting?> GetSystemSettingAsync(string settingName)
        {
            try
            {
                return await _context.SystemSettings
                    .FirstOrDefaultAsync(s => s.SettingName == settingName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إعداد النظام: {SettingName}", settingName);
                throw;
            }
        }

        /// <summary>
        /// تحديث إعداد النظام
        /// </summary>
        public async Task<bool> UpdateSystemSettingAsync(string settingName, string settingValue)
        {
            try
            {
                var setting = await _context.SystemSettings
                    .FirstOrDefaultAsync(s => s.SettingName == settingName);

                if (setting == null)
                {
                    // إنشاء إعداد جديد
                    setting = new SystemSetting
                    {
                        SettingName = settingName,
                        SettingValue = settingValue,
                        DataType = "نص",
                        CreatedBy = "System"
                    };
                    _context.SystemSettings.Add(setting);
                }
                else
                {
                    // تحديث الإعداد الموجود
                    setting.SettingValue = settingValue;
                    setting.UpdateModificationInfo("System");
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث إعداد النظام: {SettingName}", settingName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعداد النظام: {SettingName}", settingName);
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع إعدادات النظام
        /// </summary>
        public async Task<List<SystemSetting>> GetAllSystemSettingsAsync()
        {
            try
            {
                return await _context.SystemSettings
                    .Where(s => !s.IsDeleted)
                    .OrderBy(s => s.Category)
                    .ThenBy(s => s.DisplayOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع إعدادات النظام");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إعدادات الرواتب
        /// </summary>
        public async Task<List<PayrollSetting>> GetPayrollSettingsAsync()
        {
            try
            {
                return await _context.PayrollSettings
                    .Where(s => !s.IsDeleted)
                    .OrderBy(s => s.SettingName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إعدادات الرواتب");
                throw;
            }
        }
    }
}
