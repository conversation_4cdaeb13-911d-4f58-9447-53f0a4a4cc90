using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Data;
using PayrollManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PayrollManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة المستخدمين
    /// </summary>
    public class UserService : IUserService
    {
        private readonly PayrollDbContext _context;
        private readonly ILogger<UserService> _logger;

        public UserService(PayrollDbContext context, ILogger<UserService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserGroup)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive && !u.IsDeleted);

                if (user == null)
                {
                    _logger.LogWarning("محاولة تسجيل دخول بمستخدم غير موجود: {Username}", username);
                    return null;
                }

                // التحقق من قفل الحساب
                if (user.IsLocked && user.LockoutEndDate > DateTime.Now)
                {
                    _logger.LogWarning("محاولة تسجيل دخول بحساب مقفل: {Username}", username);
                    return null;
                }

                // التحقق من كلمة المرور
                if (!user.VerifyPassword(password))
                {
                    // تسجيل محاولة دخول فاشلة
                    user.RecordFailedLogin();
                    await _context.SaveChangesAsync();
                    
                    _logger.LogWarning("كلمة مرور خاطئة للمستخدم: {Username}", username);
                    return null;
                }

                // تسجيل دخول ناجح
                user.RecordSuccessfulLogin();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تسجيل دخول ناجح للمستخدم: {Username}", username);
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من بيانات المستخدم: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بواسطة المعرف
        /// </summary>
        public async Task<User?> GetUserByIdAsync(int userId)
        {
            try
            {
                return await _context.Users
                    .Include(u => u.UserGroup)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم بالمعرف: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بواسطة اسم المستخدم
        /// </summary>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                return await _context.Users
                    .Include(u => u.UserGroup)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم باسم المستخدم: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                return await _context.Users
                    .Include(u => u.UserGroup)
                    .Include(u => u.Department)
                    .Where(u => !u.IsDeleted)
                    .OrderBy(u => u.AccountName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        public async Task<User> CreateUserAsync(User user)
        {
            try
            {
                // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == user.Username);

                if (existingUser != null)
                {
                    throw new InvalidOperationException("اسم المستخدم موجود مسبقاً");
                }

                // التحقق من عدم وجود مستخدم بنفس رقم الحساب
                existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.AccountNumber == user.AccountNumber);

                if (existingUser != null)
                {
                    throw new InvalidOperationException("رقم الحساب موجود مسبقاً");
                }

                // تشفير كلمة المرور
                if (!string.IsNullOrEmpty(user.PasswordHash))
                {
                    user.PasswordHash = User.HashPassword(user.PasswordHash);
                }

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء مستخدم جديد: {Username}", user.Username);
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المستخدم: {Username}", user.Username);
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        public async Task<User> UpdateUserAsync(User user)
        {
            try
            {
                var existingUser = await _context.Users.FindAsync(user.Id);
                if (existingUser == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                // تحديث البيانات
                existingUser.AccountName = user.AccountName;
                existingUser.Email = user.Email;
                existingUser.Phone = user.Phone;
                existingUser.AccountType = user.AccountType;
                existingUser.UserGroupId = user.UserGroupId;
                existingUser.DepartmentId = user.DepartmentId;
                existingUser.IsActive = user.IsActive;
                existingUser.UpdateModificationInfo(user.ModifiedBy ?? "System");

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث بيانات المستخدم: {Username}", existingUser.Username);
                return existingUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المستخدم: {UserId}", user.Id);
                throw;
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                // حذف منطقي
                user.SoftDelete("System");
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف المستخدم: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        public async Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null || !user.VerifyPassword(oldPassword))
                {
                    return false;
                }

                user.UpdatePassword(newPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تغيير كلمة مرور المستخدم: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة مرور المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم
        /// </summary>
        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                user.UpdatePassword(newPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إعادة تعيين كلمة مرور المستخدم: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين كلمة مرور المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// قفل حساب المستخدم
        /// </summary>
        public async Task<bool> LockUserAsync(int userId, TimeSpan lockoutDuration)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                user.LockAccount(lockoutDuration);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم قفل حساب المستخدم: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في قفل حساب المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// إلغاء قفل حساب المستخدم
        /// </summary>
        public async Task<bool> UnlockUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                user.UnlockAccount();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إلغاء قفل حساب المستخدم: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء قفل حساب المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        public async Task<bool> LogUserActivityAsync(int userId, string activityType, string tableName, string? recordId = null, string? details = null)
        {
            try
            {
                var activity = new UserActivity
                {
                    UserId = userId,
                    ActivityType = activityType,
                    TableName = tableName,
                    RecordId = recordId,
                    ActivityDetails = details,
                    ActivityDate = DateTime.Now,
                    CreatedBy = "System"
                };

                _context.UserActivities.Add(activity);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل نشاط المستخدم: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// الحصول على أنشطة المستخدم
        /// </summary>
        public async Task<List<UserActivity>> GetUserActivitiesAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.UserActivities
                    .Where(ua => ua.UserId == userId);

                if (fromDate.HasValue)
                {
                    query = query.Where(ua => ua.ActivityDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(ua => ua.ActivityDate <= toDate.Value);
                }

                return await query
                    .OrderByDescending(ua => ua.ActivityDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أنشطة المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// إنشاء جلسة مستخدم
        /// </summary>
        public async Task<UserSession> CreateSessionAsync(int userId, string sessionId, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                var session = new UserSession
                {
                    UserId = userId,
                    SessionId = sessionId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    StartTime = DateTime.Now,
                    IsActive = true,
                    CreatedBy = "System"
                };

                _context.UserSessions.Add(session);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء جلسة جديدة للمستخدم: {UserId}", userId);
                return session;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء جلسة المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// إنهاء جلسة المستخدم
        /// </summary>
        public async Task<bool> EndSessionAsync(string sessionId)
        {
            try
            {
                var session = await _context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.IsActive);

                if (session == null)
                {
                    return false;
                }

                session.EndTime = DateTime.Now;
                session.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنهاء الجلسة: {SessionId}", sessionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنهاء الجلسة: {SessionId}", sessionId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على الجلسات النشطة للمستخدم
        /// </summary>
        public async Task<List<UserSession>> GetActiveSessionsAsync(int userId)
        {
            try
            {
                return await _context.UserSessions
                    .Where(s => s.UserId == userId && s.IsActive)
                    .OrderByDescending(s => s.StartTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الجلسات النشطة للمستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        public async Task<bool> HasPermissionAsync(int userId, string permissionCode)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserGroup)
                    .ThenInclude(ug => ug.Permissions)
                    .ThenInclude(ugp => ugp.Permission)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user?.UserGroup == null)
                {
                    return false;
                }

                return user.UserGroup.Permissions
                    .Any(ugp => ugp.Permission.PermissionCode == permissionCode && ugp.CanRead);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من صلاحية المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على صلاحيات المستخدم
        /// </summary>
        public async Task<List<Permission>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserGroup)
                    .ThenInclude(ug => ug.Permissions)
                    .ThenInclude(ugp => ugp.Permission)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user?.UserGroup == null)
                {
                    return new List<Permission>();
                }

                return user.UserGroup.Permissions
                    .Where(ugp => ugp.CanRead)
                    .Select(ugp => ugp.Permission)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على صلاحيات المستخدم: {UserId}", userId);
                throw;
            }
        }
    }
}
