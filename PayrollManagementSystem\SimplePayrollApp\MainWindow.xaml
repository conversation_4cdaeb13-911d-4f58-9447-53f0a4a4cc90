<Window x:Class="SimplePayrollApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة الرواتب الموحد - تسجيل الدخول"
        Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Sakkal Majalla"
        FontSize="14">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="#FAFAFA">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الجانب الأيسر - معلومات النظام -->
        <StackPanel Grid.Column="0"
                   VerticalAlignment="Center"
                   HorizontalAlignment="Center"
                   Margin="50">

            <!-- شعار النظام -->
            <materialDesign:PackIcon Kind="AccountBalance"
                                   Width="120" Height="120"
                                   Foreground="#1976D2"
                                   Margin="0,0,0,30"/>

            <!-- اسم النظام -->
            <TextBlock Text="نظام إدارة الرواتب الموحد"
                      FontSize="24"
                      FontWeight="Bold"
                      TextAlignment="Center"
                      Foreground="#1976D2"
                      Margin="0,0,0,15"/>

            <!-- وصف النظام -->
            <TextBlock Text="نظام محاسبي شامل لإدارة رواتب الموظفين"
                      FontSize="16"
                      TextAlignment="Center"
                      Foreground="#757575"
                      Margin="0,0,0,30"/>

            <!-- معلومات الإصدار -->
            <TextBlock Text="الإصدار 1.0.0"
                      FontSize="12"
                      TextAlignment="Center"
                      Foreground="#757575"/>
        </StackPanel>

        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <materialDesign:Card Grid.Column="1"
                           Padding="40"
                           VerticalAlignment="Center"
                           Margin="20">

            <StackPanel>
                <!-- عنوان تسجيل الدخول -->
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,30">
                    <materialDesign:PackIcon Kind="Login"
                                           Width="24" Height="24"
                                           Foreground="#1976D2"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="تسجيل الدخول"
                              FontSize="22"
                              FontWeight="Bold"
                              Foreground="#1976D2"/>
                </StackPanel>

                <!-- رسالة الترحيب -->
                <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                          TextAlignment="Center"
                          Foreground="#757575"
                          Margin="0,0,0,30"/>

                <!-- حقل اسم المستخدم -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="اسم المستخدم"
                        materialDesign:HintAssist.IsFloating="True"
                        materialDesign:TextFieldAssist.HasLeadingIcon="True"
                        materialDesign:TextFieldAssist.LeadingIcon="Account"
                        Height="50"
                        FontSize="14"
                        Text="admin"
                        Margin="0,0,0,20"/>

                <!-- حقل كلمة المرور -->
                <PasswordBox x:Name="PasswordBox"
                            materialDesign:HintAssist.Hint="كلمة المرور"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.HasLeadingIcon="True"
                            materialDesign:TextFieldAssist.LeadingIcon="Lock"
                            Height="50"
                            FontSize="14"
                            Password="admin"
                            Margin="0,0,0,20"/>

                <!-- خيار تذكر المستخدم -->
                <CheckBox x:Name="RememberMeCheckBox"
                         Content="تذكرني"
                         FontSize="14"
                         Margin="0,0,0,25"/>

                <!-- زر تسجيل الدخول -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Height="45"
                       FontSize="16"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="#1976D2"
                       BorderBrush="#1976D2"
                       Click="LoginButton_Click"
                       IsDefault="True"
                       Margin="0,0,0,15"/>

                <!-- رسالة الحالة -->
                <TextBlock x:Name="StatusTextBlock"
                          Text="أدخل بيانات تسجيل الدخول"
                          TextAlignment="Center"
                          Foreground="#757575"
                          FontSize="12"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- شريط الحالة -->
        <StatusBar Grid.Column="0" Grid.ColumnSpan="3"
                  VerticalAlignment="Bottom"
                  Background="#F5F5F5"
                  Height="25">
            <StatusBarItem>
                <TextBlock Text="© 2024 شركة التطوير المتقدم - جميع الحقوق محفوظة"
                          FontSize="11"/>
            </StatusBarItem>

            <StatusBarItem HorizontalAlignment="Left">
                <TextBlock x:Name="CurrentTimeTextBlock"
                          FontSize="11"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
