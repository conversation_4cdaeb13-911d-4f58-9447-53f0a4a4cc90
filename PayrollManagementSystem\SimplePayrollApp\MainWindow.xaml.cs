﻿using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Threading;

namespace SimplePayrollApp;

/// <summary>
/// نافذة تسجيل الدخول الرئيسية
/// </summary>
public partial class MainWindow : Window
{
    private readonly DispatcherTimer _timeTimer;

    public MainWindow()
    {
        InitializeComponent();

        // تكوين الثقافة العربية
        ConfigureArabicCulture();

        // تهيئة مؤقت الوقت
        _timeTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timeTimer.Tick += TimeTimer_Tick;
        _timeTimer.Start();

        // تحديث الوقت الحالي
        UpdateCurrentTime();

        // التركيز على حقل اسم المستخدم
        Loaded += (s, e) => UsernameTextBox.Focus();
    }

    /// <summary>
    /// تكوين الثقافة العربية
    /// </summary>
    private void ConfigureArabicCulture()
    {
        var culture = new CultureInfo("ar-IQ");
        culture.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
        culture.DateTimeFormat.LongDatePattern = "dddd، dd MMMM yyyy";
        culture.DateTimeFormat.ShortTimePattern = "HH:mm";
        culture.DateTimeFormat.LongTimePattern = "HH:mm:ss";
        culture.NumberFormat.CurrencySymbol = "د.ع";
        culture.NumberFormat.CurrencyDecimalDigits = 3;

        Thread.CurrentThread.CurrentCulture = culture;
        Thread.CurrentThread.CurrentUICulture = culture;
    }

    /// <summary>
    /// معالج النقر على زر تسجيل الدخول
    /// </summary>
    private void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        var username = UsernameTextBox.Text.Trim();
        var password = PasswordBox.Password;

        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            StatusTextBlock.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
            StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }

        // التحقق من بيانات تسجيل الدخول (مبسط للعرض التوضيحي)
        if (username == "admin" && password == "admin")
        {
            StatusTextBlock.Text = "تم تسجيل الدخول بنجاح!";
            StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;

            // إنشاء النافذة الرئيسية
            var mainWindow = new PayrollMainWindow();
            mainWindow.Show();

            // إغلاق نافذة تسجيل الدخول
            Close();
        }
        else
        {
            StatusTextBlock.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
            StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;

            // مسح كلمة المرور
            PasswordBox.Clear();
            PasswordBox.Focus();
        }
    }

    /// <summary>
    /// معالج تحديث الوقت
    /// </summary>
    private void TimeTimer_Tick(object? sender, EventArgs e)
    {
        UpdateCurrentTime();
    }

    /// <summary>
    /// تحديث عرض الوقت الحالي
    /// </summary>
    private void UpdateCurrentTime()
    {
        CurrentTimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
    }

    /// <summary>
    /// معالج إغلاق النافذة
    /// </summary>
    protected override void OnClosed(EventArgs e)
    {
        _timeTimer?.Stop();
        base.OnClosed(e);
    }
}