<Window x:Class="SimplePayrollApp.PayrollMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة الرواتب الموحد - النافذة الرئيسية" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Sakkal Majalla"
        FontSize="14">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <materialDesign:Card Grid.Row="0"
                           Background="#1976D2"
                           Margin="0,0,0,5">
            <Grid Height="60">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- شعار النظام -->
                <StackPanel Grid.Column="0" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <materialDesign:PackIcon Kind="AccountBalance" 
                                           Width="32" Height="32"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="نظام إدارة الرواتب الموحد"
                              FontSize="20"
                              FontWeight="Bold"
                              Foreground="White"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- عنوان الصفحة الحالية -->
                <TextBlock Grid.Column="1"
                          x:Name="CurrentPageTitle"
                          Text="الصفحة الرئيسية"
                          FontSize="16"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"/>

                <!-- معلومات المستخدم -->
                <StackPanel Grid.Column="2" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <StackPanel Orientation="Vertical" 
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0">
                        <TextBlock Text="مدير النظام"
                                  Foreground="White"
                                  FontSize="13"
                                  HorizontalAlignment="Right"/>
                        <TextBlock Text="مدير"
                                  Foreground="#BBDEFB"
                                  FontSize="11"
                                  HorizontalAlignment="Right"/>
                    </StackPanel>
                    
                    <materialDesign:PackIcon Kind="Account" 
                                           Width="24" Height="24"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    
                    <Button Content="خروج"
                           Background="Transparent"
                           BorderBrush="White"
                           BorderThickness="1"
                           Foreground="White"
                           Click="LogoutButton_Click"
                           Padding="15,5"
                           Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي -->
            <materialDesign:Card Grid.Column="0"
                               Background="#1976D2"
                               Margin="5,0,5,5">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- قائمة إدارة الموظفين -->
                        <TextBlock Text="إدارة الموظفين" 
                                  FontWeight="Bold"
                                  FontSize="16"
                                  Foreground="White"
                                  Margin="20,15,20,10"/>
                        
                        <Button Content="قائمة الموظفين"
                               Background="Transparent"
                               BorderThickness="0"
                               Foreground="White"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Padding="20,10"
                               Margin="0,2"
                               Click="MenuButton_Click"
                               Tag="employees">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                           Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                                   Width="16" Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#0D47A1"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!-- قائمة إدارة الرواتب -->
                        <TextBlock Text="إدارة الرواتب" 
                                  FontWeight="Bold"
                                  FontSize="16"
                                  Foreground="White"
                                  Margin="20,15,20,10"/>
                        
                        <Button Content="حساب الرواتب"
                               Background="Transparent"
                               BorderThickness="0"
                               Foreground="White"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Padding="20,10"
                               Margin="0,2"
                               Click="MenuButton_Click"
                               Tag="payroll">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                           Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Calculator" 
                                                                   Width="16" Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#0D47A1"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!-- قائمة المحاسبة -->
                        <TextBlock Text="المحاسبة" 
                                  FontWeight="Bold"
                                  FontSize="16"
                                  Foreground="White"
                                  Margin="20,15,20,10"/>
                        
                        <Button Content="دليل الحسابات"
                               Background="Transparent"
                               BorderThickness="0"
                               Foreground="White"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Padding="20,10"
                               Margin="0,2"
                               Click="MenuButton_Click"
                               Tag="accounting">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                           Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="BookOpenPageVariant" 
                                                                   Width="16" Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#0D47A1"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!-- قائمة التقارير -->
                        <TextBlock Text="التقارير" 
                                  FontWeight="Bold"
                                  FontSize="16"
                                  Foreground="White"
                                  Margin="20,15,20,10"/>
                        
                        <Button Content="تقارير الرواتب"
                               Background="Transparent"
                               BorderThickness="0"
                               Foreground="White"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Padding="20,10"
                               Margin="0,2"
                               Click="MenuButton_Click"
                               Tag="reports">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                           Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileChart" 
                                                                   Width="16" Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#0D47A1"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- منطقة المحتوى -->
            <materialDesign:Card Grid.Column="1"
                               Padding="20"
                               Margin="0,0,5,5">
                <Grid>
                    <!-- محتوى الصفحة -->
                    <ContentControl x:Name="ContentArea"/>
                    
                    <!-- صفحة ترحيبية افتراضية -->
                    <StackPanel x:Name="WelcomePanel"
                               VerticalAlignment="Center" 
                               HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Home" 
                                               Width="100" Height="100"
                                               Foreground="#BBDEFB"
                                               Margin="0,0,0,20"/>
                        <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                                  FontSize="24"
                                  FontWeight="Bold"
                                  Foreground="#1976D2"
                                  TextAlignment="Center"
                                  Margin="0,0,0,10"/>
                        <TextBlock Text="اختر من القائمة الجانبية للبدء"
                                  FontSize="16"
                                  Foreground="#757575"
                                  TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" 
                  Background="#F5F5F5"
                  BorderBrush="#E0E0E0"
                  BorderThickness="0,1,0,0">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Information" 
                                           Width="14" Height="14"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="جاهز" FontSize="12"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Center">
                <TextBlock x:Name="CurrentDateTextBlock"
                          FontSize="12"/>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Clock" 
                                           Width="14" Height="14"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock x:Name="CurrentTimeTextBlock" FontSize="12"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
