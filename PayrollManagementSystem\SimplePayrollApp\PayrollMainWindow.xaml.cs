using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace SimplePayrollApp;

/// <summary>
/// النافذة الرئيسية لنظام إدارة الرواتب
/// </summary>
public partial class PayrollMainWindow : Window
{
    private readonly DispatcherTimer _timeTimer;

    public PayrollMainWindow()
    {
        InitializeComponent();
        
        // تهيئة مؤقت الوقت
        _timeTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timeTimer.Tick += TimeTimer_Tick;
        _timeTimer.Start();

        // تحديث التاريخ والوقت
        UpdateDateTime();
    }

    /// <summary>
    /// معالج النقر على أزرار القائمة
    /// </summary>
    private void MenuButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string tag)
        {
            // إخفاء الصفحة الترحيبية
            WelcomePanel.Visibility = Visibility.Collapsed;
            
            // تحديث عنوان الصفحة والمحتوى حسب الاختيار
            switch (tag)
            {
                case "employees":
                    CurrentPageTitle.Text = "إدارة الموظفين";
                    ShowEmployeesPage();
                    break;
                    
                case "payroll":
                    CurrentPageTitle.Text = "إدارة الرواتب";
                    ShowPayrollPage();
                    break;
                    
                case "accounting":
                    CurrentPageTitle.Text = "المحاسبة";
                    ShowAccountingPage();
                    break;
                    
                case "reports":
                    CurrentPageTitle.Text = "التقارير";
                    ShowReportsPage();
                    break;
                    
                default:
                    CurrentPageTitle.Text = "الصفحة الرئيسية";
                    ShowWelcomePage();
                    break;
            }
        }
    }

    /// <summary>
    /// عرض صفحة إدارة الموظفين
    /// </summary>
    private void ShowEmployeesPage()
    {
        var content = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        content.Children.Add(new TextBlock
        {
            Text = "إدارة الموظفين",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إدارة بيانات الموظفين وإضافة موظفين جدد",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        });

        var addButton = new Button
        {
            Content = "إضافة موظف جديد",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        addButton.Click += (s, e) => MessageBox.Show("سيتم إضافة موظف جديد", "إضافة موظف");

        var listButton = new Button
        {
            Content = "عرض قائمة الموظفين",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        listButton.Click += (s, e) => MessageBox.Show("سيتم عرض قائمة الموظفين", "قائمة الموظفين");

        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };
        buttonPanel.Children.Add(addButton);
        buttonPanel.Children.Add(listButton);

        content.Children.Add(buttonPanel);
        ContentArea.Content = content;
    }

    /// <summary>
    /// عرض صفحة إدارة الرواتب
    /// </summary>
    private void ShowPayrollPage()
    {
        var content = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        content.Children.Add(new TextBlock
        {
            Text = "إدارة الرواتب",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك حساب واعتماد رواتب الموظفين",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        });

        var calculateButton = new Button
        {
            Content = "حساب الرواتب",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        calculateButton.Click += (s, e) => MessageBox.Show("سيتم حساب رواتب جميع الموظفين", "حساب الرواتب");

        var approveButton = new Button
        {
            Content = "اعتماد الرواتب",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        approveButton.Click += (s, e) => MessageBox.Show("سيتم اعتماد الرواتب المحسوبة", "اعتماد الرواتب");

        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };
        buttonPanel.Children.Add(calculateButton);
        buttonPanel.Children.Add(approveButton);

        content.Children.Add(buttonPanel);
        ContentArea.Content = content;
    }

    /// <summary>
    /// عرض صفحة المحاسبة
    /// </summary>
    private void ShowAccountingPage()
    {
        var content = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        content.Children.Add(new TextBlock
        {
            Text = "النظام المحاسبي",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إدارة دليل الحسابات والقيود المحاسبية",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        });

        var accountsButton = new Button
        {
            Content = "دليل الحسابات",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        accountsButton.Click += (s, e) => MessageBox.Show("سيتم عرض دليل الحسابات", "دليل الحسابات");

        var journalButton = new Button
        {
            Content = "القيود اليومية",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        journalButton.Click += (s, e) => MessageBox.Show("سيتم عرض القيود اليومية", "القيود اليومية");

        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };
        buttonPanel.Children.Add(accountsButton);
        buttonPanel.Children.Add(journalButton);

        content.Children.Add(buttonPanel);
        ContentArea.Content = content;
    }

    /// <summary>
    /// عرض صفحة التقارير
    /// </summary>
    private void ShowReportsPage()
    {
        var content = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        content.Children.Add(new TextBlock
        {
            Text = "التقارير",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إنشاء وطباعة التقارير المختلفة",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        });

        var payrollReportButton = new Button
        {
            Content = "تقرير الرواتب",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        payrollReportButton.Click += (s, e) => MessageBox.Show("سيتم إنشاء تقرير الرواتب", "تقرير الرواتب");

        var trialBalanceButton = new Button
        {
            Content = "الميزان التجريبي",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10)
        };
        trialBalanceButton.Click += (s, e) => MessageBox.Show("سيتم إنشاء الميزان التجريبي", "الميزان التجريبي");

        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };
        buttonPanel.Children.Add(payrollReportButton);
        buttonPanel.Children.Add(trialBalanceButton);

        content.Children.Add(buttonPanel);
        ContentArea.Content = content;
    }

    /// <summary>
    /// عرض الصفحة الترحيبية
    /// </summary>
    private void ShowWelcomePage()
    {
        ContentArea.Content = null;
        WelcomePanel.Visibility = Visibility.Visible;
    }

    /// <summary>
    /// معالج النقر على زر الخروج
    /// </summary>
    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد تسجيل الخروج من النظام؟", 
            "تسجيل الخروج", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            // إنشاء نافذة تسجيل الدخول
            var loginWindow = new MainWindow();
            loginWindow.Show();
            
            // إغلاق النافذة الحالية
            Close();
        }
    }

    /// <summary>
    /// معالج تحديث الوقت
    /// </summary>
    private void TimeTimer_Tick(object? sender, EventArgs e)
    {
        UpdateDateTime();
    }

    /// <summary>
    /// تحديث عرض التاريخ والوقت
    /// </summary>
    private void UpdateDateTime()
    {
        var now = DateTime.Now;
        CurrentTimeTextBlock.Text = now.ToString("HH:mm:ss");
        CurrentDateTextBlock.Text = now.ToString("dddd، dd MMMM yyyy");
    }

    /// <summary>
    /// معالج إغلاق النافذة
    /// </summary>
    protected override void OnClosed(EventArgs e)
    {
        _timeTimer?.Stop();
        base.OnClosed(e);
    }
}
