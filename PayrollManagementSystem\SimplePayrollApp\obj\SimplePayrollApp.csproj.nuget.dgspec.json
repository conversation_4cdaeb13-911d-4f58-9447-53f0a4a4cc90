{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Emp\\PayrollManagementSystem\\SimplePayrollApp\\SimplePayrollApp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Emp\\PayrollManagementSystem\\SimplePayrollApp\\SimplePayrollApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Emp\\PayrollManagementSystem\\SimplePayrollApp\\SimplePayrollApp.csproj", "projectName": "SimplePayrollApp", "projectPath": "C:\\Users\\<USER>\\Desktop\\Emp\\PayrollManagementSystem\\SimplePayrollApp\\SimplePayrollApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Emp\\PayrollManagementSystem\\SimplePayrollApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.312/PortableRuntimeIdentifierGraph.json"}}}}}