@echo off
chcp 65001 > nul
title نظام إدارة الرواتب الموحد

echo ========================================
echo    نظام إدارة الرواتب الموحد
echo    Unified Payroll Management System
echo ========================================
echo.

echo التحقق من متطلبات النظام...
echo Checking system requirements...

:: التحقق من .NET 8
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8 غير مثبت أو غير متاح
    echo Error: .NET 8 is not installed or available
    echo.
    echo يرجى تحميل وتثبيت .NET 8 من:
    echo Please download and install .NET 8 from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo .NET 8 متاح ✓
echo .NET 8 available ✓

echo.
echo بناء التطبيق...
echo Building application...

dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق
    echo Build failed
    echo.
    echo محاولة بناء مع تفاصيل أكثر...
    echo Trying build with more details...
    dotnet build --configuration Release --verbosity normal
    pause
    exit /b 1
)

echo البناء مكتمل ✓
echo Build completed ✓

echo.
echo تشغيل نظام إدارة الرواتب الموحد...
echo Starting Unified Payroll Management System...
echo.
echo بيانات تسجيل الدخول:
echo Login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin
echo.

:: تشغيل التطبيق
dotnet run --configuration Release

echo.
echo تم إغلاق التطبيق
echo Application closed
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
