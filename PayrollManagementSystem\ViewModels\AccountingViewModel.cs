using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Models;
using PayrollManagementSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للمحاسبة
    /// </summary>
    public class AccountingViewModel : INotifyPropertyChanged
    {
        private readonly IAccountingService _accountingService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<AccountingViewModel> _logger;

        private ObservableCollection<ChartOfAccount> _accounts = new();
        private ChartOfAccount? _selectedAccount;
        private bool _isLoading = false;

        public AccountingViewModel(
            IAccountingService accountingService,
            IDialogService dialogService,
            ILogger<AccountingViewModel> logger)
        {
            _accountingService = accountingService;
            _dialogService = dialogService;
            _logger = logger;

            // تهيئة الأوامر
            LoadAccountsCommand = new RelayCommand(async () => await LoadAccountsAsync());
            AddAccountCommand = new RelayCommand(AddAccount);
            EditAccountCommand = new RelayCommand(EditAccount, () => SelectedAccount != null);

            // تحميل البيانات
            _ = LoadAccountsAsync();
        }

        #region Properties

        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        public ObservableCollection<ChartOfAccount> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public ChartOfAccount? SelectedAccount
        {
            get => _selectedAccount;
            set
            {
                if (SetProperty(ref _selectedAccount, value))
                {
                    ((RelayCommand)EditAccountCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحميل الحسابات
        /// </summary>
        public ICommand LoadAccountsCommand { get; }

        /// <summary>
        /// أمر إضافة حساب
        /// </summary>
        public ICommand AddAccountCommand { get; }

        /// <summary>
        /// أمر تعديل حساب
        /// </summary>
        public ICommand EditAccountCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل قائمة الحسابات
        /// </summary>
        private async Task LoadAccountsAsync()
        {
            try
            {
                IsLoading = true;
                var accounts = await _accountingService.GetChartOfAccountsAsync();
                
                Accounts.Clear();
                foreach (var account in accounts)
                {
                    Accounts.Add(account);
                }

                _logger.LogInformation("تم تحميل {Count} حساب", accounts.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الحسابات");
                await _dialogService.ShowErrorAsync("حدث خطأ في تحميل بيانات الحسابات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        private void AddAccount()
        {
            try
            {
                // فتح نافذة إضافة حساب
                _logger.LogInformation("طلب إضافة حساب جديد");
                // TODO: تنفيذ نافذة إضافة الحساب
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة حساب جديد");
            }
        }

        /// <summary>
        /// تعديل حساب
        /// </summary>
        private void EditAccount()
        {
            try
            {
                if (SelectedAccount == null) return;

                // فتح نافذة تعديل الحساب
                _logger.LogInformation("طلب تعديل الحساب: {AccountId}", SelectedAccount.Id);
                // TODO: تنفيذ نافذة تعديل الحساب
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تعديل الحساب");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
