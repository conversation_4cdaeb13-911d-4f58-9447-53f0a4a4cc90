using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Models;
using PayrollManagementSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel لإدارة الموظفين
    /// </summary>
    public class EmployeeManagementViewModel : INotifyPropertyChanged
    {
        private readonly IEmployeeService _employeeService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<EmployeeManagementViewModel> _logger;

        private ObservableCollection<Employee> _employees = new();
        private Employee? _selectedEmployee;
        private string _searchText = string.Empty;
        private bool _isLoading = false;

        public EmployeeManagementViewModel(
            IEmployeeService employeeService,
            IDialogService dialogService,
            ILogger<EmployeeManagementViewModel> logger)
        {
            _employeeService = employeeService;
            _dialogService = dialogService;
            _logger = logger;

            // تهيئة الأوامر
            LoadEmployeesCommand = new RelayCommand(async () => await LoadEmployeesAsync());
            AddEmployeeCommand = new RelayCommand(AddEmployee);
            EditEmployeeCommand = new RelayCommand(EditEmployee, () => SelectedEmployee != null);
            DeleteEmployeeCommand = new RelayCommand(async () => await DeleteEmployeeAsync(), () => SelectedEmployee != null);
            SearchCommand = new RelayCommand(async () => await SearchEmployeesAsync());

            // تحميل البيانات
            _ = LoadEmployeesAsync();
        }

        #region Properties

        /// <summary>
        /// قائمة الموظفين
        /// </summary>
        public ObservableCollection<Employee> Employees
        {
            get => _employees;
            set => SetProperty(ref _employees, value);
        }

        /// <summary>
        /// الموظف المحدد
        /// </summary>
        public Employee? SelectedEmployee
        {
            get => _selectedEmployee;
            set
            {
                if (SetProperty(ref _selectedEmployee, value))
                {
                    ((RelayCommand)EditEmployeeCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)DeleteEmployeeCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحميل الموظفين
        /// </summary>
        public ICommand LoadEmployeesCommand { get; }

        /// <summary>
        /// أمر إضافة موظف
        /// </summary>
        public ICommand AddEmployeeCommand { get; }

        /// <summary>
        /// أمر تعديل موظف
        /// </summary>
        public ICommand EditEmployeeCommand { get; }

        /// <summary>
        /// أمر حذف موظف
        /// </summary>
        public ICommand DeleteEmployeeCommand { get; }

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل قائمة الموظفين
        /// </summary>
        private async Task LoadEmployeesAsync()
        {
            try
            {
                IsLoading = true;
                var employees = await _employeeService.GetAllEmployeesAsync();
                
                Employees.Clear();
                foreach (var employee in employees)
                {
                    Employees.Add(employee);
                }

                _logger.LogInformation("تم تحميل {Count} موظف", employees.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الموظفين");
                await _dialogService.ShowErrorAsync("حدث خطأ في تحميل بيانات الموظفين");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إضافة موظف جديد
        /// </summary>
        private void AddEmployee()
        {
            try
            {
                // فتح نافذة إضافة موظف
                _logger.LogInformation("طلب إضافة موظف جديد");
                // TODO: تنفيذ نافذة إضافة الموظف
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة موظف جديد");
            }
        }

        /// <summary>
        /// تعديل موظف
        /// </summary>
        private void EditEmployee()
        {
            try
            {
                if (SelectedEmployee == null) return;

                // فتح نافذة تعديل الموظف
                _logger.LogInformation("طلب تعديل الموظف: {EmployeeId}", SelectedEmployee.Id);
                // TODO: تنفيذ نافذة تعديل الموظف
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تعديل الموظف");
            }
        }

        /// <summary>
        /// حذف موظف
        /// </summary>
        private async Task DeleteEmployeeAsync()
        {
            try
            {
                if (SelectedEmployee == null) return;

                var confirmed = await _dialogService.ShowConfirmationAsync(
                    $"هل أنت متأكد من حذف الموظف '{SelectedEmployee.FullName}'؟");

                if (!confirmed) return;

                var success = await _employeeService.DeleteEmployeeAsync(SelectedEmployee.Id);
                if (success)
                {
                    Employees.Remove(SelectedEmployee);
                    SelectedEmployee = null;
                    await _dialogService.ShowInformationAsync("تم حذف الموظف بنجاح");
                    _logger.LogInformation("تم حذف الموظف: {EmployeeId}", SelectedEmployee.Id);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في حذف الموظف");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الموظف");
                await _dialogService.ShowErrorAsync("حدث خطأ في حذف الموظف");
            }
        }

        /// <summary>
        /// البحث في الموظفين
        /// </summary>
        private async Task SearchEmployeesAsync()
        {
            try
            {
                IsLoading = true;
                var employees = await _employeeService.SearchEmployeesAsync(SearchText);
                
                Employees.Clear();
                foreach (var employee in employees)
                {
                    Employees.Add(employee);
                }

                _logger.LogInformation("تم البحث عن '{SearchText}' وإرجاع {Count} نتيجة", SearchText, employees.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الموظفين");
                await _dialogService.ShowErrorAsync("حدث خطأ في البحث");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
