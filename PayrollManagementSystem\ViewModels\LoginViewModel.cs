using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Models;
using PayrollManagementSystem.Services;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel لنافذة تسجيل الدخول
    /// </summary>
    public class LoginViewModel : INotifyPropertyChanged
    {
        private readonly IUserService _userService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<LoginViewModel> _logger;
        private readonly IConfiguration _configuration;

        private string _username = string.Empty;
        private string _password = string.Empty;
        private bool _rememberMe = false;
        private bool _isLoading = false;
        private bool _hasError = false;
        private string _errorMessage = string.Empty;

        public LoginViewModel(
            IUserService userService,
            ICurrentUserService currentUserService,
            ILogger<LoginViewModel> logger,
            IConfiguration configuration)
        {
            _userService = userService;
            _currentUserService = currentUserService;
            _logger = logger;
            _configuration = configuration;

            // تهيئة الأوامر
            LoginCommand = new RelayCommand(async () => await LoginAsync(), CanLogin);
            ForgotPasswordCommand = new RelayCommand(ShowForgotPasswordDialog);
        }

        #region Properties

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string Username
        {
            get => _username;
            set
            {
                if (SetProperty(ref _username, value))
                {
                    ClearError();
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// كلمة المرور
        /// </summary>
        public string Password
        {
            get => _password;
            set
            {
                if (SetProperty(ref _password, value))
                {
                    ClearError();
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// تذكر المستخدم
        /// </summary>
        public bool RememberMe
        {
            get => _rememberMe;
            set => SetProperty(ref _rememberMe, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// وجود خطأ
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// رسالة الخطأ
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تسجيل الدخول
        /// </summary>
        public ICommand LoginCommand { get; }

        /// <summary>
        /// أمر نسيان كلمة المرور
        /// </summary>
        public ICommand ForgotPasswordCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// حدث نجاح تسجيل الدخول
        /// </summary>
        public event EventHandler? LoginSuccessful;

        /// <summary>
        /// حدث فشل تسجيل الدخول
        /// </summary>
        public event EventHandler<string>? LoginFailed;

        #endregion

        #region Methods

        /// <summary>
        /// التحقق من إمكانية تسجيل الدخول
        /// </summary>
        private bool CanLogin()
        {
            return !IsLoading && 
                   !string.IsNullOrWhiteSpace(Username) && 
                   !string.IsNullOrWhiteSpace(Password);
        }

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                ClearError();

                _logger.LogInformation("محاولة تسجيل دخول للمستخدم: {Username}", Username);

                // التحقق من بيانات المستخدم
                var user = await _userService.AuthenticateAsync(Username, Password);

                if (user != null)
                {
                    // تسجيل المستخدم الحالي
                    await _currentUserService.SetCurrentUserAsync(user);

                    // حفظ بيانات المستخدم إذا كان مطلوباً
                    if (RememberMe)
                    {
                        SaveCredentials();
                    }
                    else
                    {
                        ClearSavedCredentials();
                    }

                    // تسجيل نشاط تسجيل الدخول
                    await _userService.LogUserActivityAsync(user.Id, "تسجيل دخول", "Users", user.Id.ToString());

                    _logger.LogInformation("تم تسجيل دخول المستخدم بنجاح: {Username}", Username);

                    // إثارة حدث نجاح تسجيل الدخول
                    LoginSuccessful?.Invoke(this, EventArgs.Empty);
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    _logger.LogWarning("فشل في تسجيل دخول المستخدم: {Username}", Username);
                    LoginFailed?.Invoke(this, "اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الدخول للمستخدم: {Username}", Username);
                ShowError("حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.");
                LoginFailed?.Invoke(this, ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إظهار حوار نسيان كلمة المرور
        /// </summary>
        private void ShowForgotPasswordDialog()
        {
            // يمكن تنفيذ نافذة منفصلة لإعادة تعيين كلمة المرور
            _logger.LogInformation("طلب إعادة تعيين كلمة المرور للمستخدم: {Username}", Username);
        }

        /// <summary>
        /// إظهار رسالة خطأ
        /// </summary>
        private void ShowError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        /// <summary>
        /// مسح رسالة الخطأ
        /// </summary>
        private void ClearError()
        {
            HasError = false;
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// حفظ بيانات المستخدم
        /// </summary>
        private void SaveCredentials()
        {
            try
            {
                // يمكن حفظ البيانات في Registry أو ملف مشفر
                Properties.Settings.Default.SavedUsername = Username;
                Properties.Settings.Default.RememberUser = RememberMe;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ بيانات المستخدم");
            }
        }

        /// <summary>
        /// تحميل بيانات المستخدم المحفوظة
        /// </summary>
        public void LoadSavedCredentials()
        {
            try
            {
                if (Properties.Settings.Default.RememberUser)
                {
                    Username = Properties.Settings.Default.SavedUsername ?? string.Empty;
                    RememberMe = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات المستخدم المحفوظة");
            }
        }

        /// <summary>
        /// مسح بيانات المستخدم المحفوظة
        /// </summary>
        private void ClearSavedCredentials()
        {
            try
            {
                Properties.Settings.Default.SavedUsername = string.Empty;
                Properties.Settings.Default.RememberUser = false;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مسح بيانات المستخدم المحفوظة");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// أمر بسيط لتنفيذ العمليات
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();

        public void RaiseCanExecuteChanged() => CanExecuteChanged?.Invoke(this, EventArgs.Empty);
    }
}
