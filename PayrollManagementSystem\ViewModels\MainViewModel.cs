using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Services;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للنافذة الرئيسية
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly INavigationService _navigationService;
        private readonly ILogger<MainViewModel> _logger;

        private string _currentPageTitle = "الصفحة الرئيسية";
        private object? _currentPageContent;

        public MainViewModel(
            ICurrentUserService currentUserService,
            INavigationService navigationService,
            ILogger<MainViewModel> logger)
        {
            _currentUserService = currentUserService;
            _navigationService = navigationService;
            _logger = logger;

            // تهيئة الأوامر
            NavigateToEmployeesCommand = new RelayCommand(() => NavigateTo("إدارة الموظفين"));
            NavigateToPayrollCommand = new RelayCommand(() => NavigateTo("إدارة الرواتب"));
            NavigateToAccountingCommand = new RelayCommand(() => NavigateTo("المحاسبة"));
            NavigateToReportsCommand = new RelayCommand(() => NavigateTo("التقارير"));
            NavigateToSettingsCommand = new RelayCommand(() => NavigateTo("الإعدادات"));
            LogoutCommand = new RelayCommand(Logout);

            // ربط حدث التنقل
            _navigationService.Navigated += OnNavigated;
        }

        #region Properties

        /// <summary>
        /// عنوان الصفحة الحالية
        /// </summary>
        public string CurrentPageTitle
        {
            get => _currentPageTitle;
            set => SetProperty(ref _currentPageTitle, value);
        }

        /// <summary>
        /// محتوى الصفحة الحالية
        /// </summary>
        public object? CurrentPageContent
        {
            get => _currentPageContent;
            set => SetProperty(ref _currentPageContent, value);
        }

        /// <summary>
        /// اسم المستخدم الحالي
        /// </summary>
        public string CurrentUserName => _currentUserService.GetDisplayName();

        /// <summary>
        /// نوع حساب المستخدم الحالي
        /// </summary>
        public string CurrentUserType => _currentUserService.CurrentUser?.AccountType ?? "غير محدد";

        /// <summary>
        /// التحقق من كون المستخدم مدير
        /// </summary>
        public bool IsAdmin => _currentUserService.IsAdmin();

        #endregion

        #region Commands

        /// <summary>
        /// أمر الانتقال إلى إدارة الموظفين
        /// </summary>
        public ICommand NavigateToEmployeesCommand { get; }

        /// <summary>
        /// أمر الانتقال إلى إدارة الرواتب
        /// </summary>
        public ICommand NavigateToPayrollCommand { get; }

        /// <summary>
        /// أمر الانتقال إلى المحاسبة
        /// </summary>
        public ICommand NavigateToAccountingCommand { get; }

        /// <summary>
        /// أمر الانتقال إلى التقارير
        /// </summary>
        public ICommand NavigateToReportsCommand { get; }

        /// <summary>
        /// أمر الانتقال إلى الإعدادات
        /// </summary>
        public ICommand NavigateToSettingsCommand { get; }

        /// <summary>
        /// أمر تسجيل الخروج
        /// </summary>
        public ICommand LogoutCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// الانتقال إلى صفحة
        /// </summary>
        private void NavigateTo(string pageName)
        {
            try
            {
                _navigationService.NavigateTo(pageName);
                _logger.LogInformation("تم الانتقال إلى صفحة: {PageName}", pageName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الانتقال إلى صفحة: {PageName}", pageName);
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        private void Logout()
        {
            try
            {
                _currentUserService.ClearCurrentUser();
                _logger.LogInformation("تم تسجيل خروج المستخدم");

                // إغلاق النافذة الحالية وفتح نافذة تسجيل الدخول
                var loginWindow = App.GetService<Views.LoginWindow>();
                loginWindow.Show();

                // إغلاق النافذة الرئيسية
                System.Windows.Application.Current.MainWindow?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الخروج");
            }
        }

        /// <summary>
        /// معالج حدث التنقل
        /// </summary>
        private void OnNavigated(object? sender, NavigationEventArgs e)
        {
            CurrentPageTitle = e.PageName;
            
            // هنا يمكن تحديد محتوى الصفحة بناءً على اسم الصفحة
            switch (e.PageName)
            {
                case "إدارة الموظفين":
                    // تحميل صفحة إدارة الموظفين
                    break;
                case "إدارة الرواتب":
                    // تحميل صفحة إدارة الرواتب
                    break;
                case "المحاسبة":
                    // تحميل صفحة المحاسبة
                    break;
                case "التقارير":
                    // تحميل صفحة التقارير
                    break;
                case "الإعدادات":
                    // تحميل صفحة الإعدادات
                    break;
                default:
                    CurrentPageTitle = "الصفحة الرئيسية";
                    break;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
