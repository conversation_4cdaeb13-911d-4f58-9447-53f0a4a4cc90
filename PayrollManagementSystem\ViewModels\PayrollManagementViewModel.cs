using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Models;
using PayrollManagementSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel لإدارة الرواتب
    /// </summary>
    public class PayrollManagementViewModel : INotifyPropertyChanged
    {
        private readonly IPayrollService _payrollService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<PayrollManagementViewModel> _logger;

        private ObservableCollection<PayrollRecord> _payrollRecords = new();
        private PayrollRecord? _selectedPayrollRecord;
        private int _selectedYear = DateTime.Now.Year;
        private int _selectedMonth = DateTime.Now.Month;
        private bool _isLoading = false;

        public PayrollManagementViewModel(
            IPayrollService payrollService,
            IDialogService dialogService,
            ILogger<PayrollManagementViewModel> logger)
        {
            _payrollService = payrollService;
            _dialogService = dialogService;
            _logger = logger;

            // تهيئة الأوامر
            LoadPayrollsCommand = new RelayCommand(async () => await LoadPayrollsAsync());
            CalculateAllPayrollsCommand = new RelayCommand(async () => await CalculateAllPayrollsAsync());
            ApprovePayrollCommand = new RelayCommand(async () => await ApprovePayrollAsync(), () => SelectedPayrollRecord != null);
            PaySalaryCommand = new RelayCommand(async () => await PaySalaryAsync(), () => SelectedPayrollRecord != null);

            // تحميل البيانات
            _ = LoadPayrollsAsync();
        }

        #region Properties

        /// <summary>
        /// قائمة سجلات الرواتب
        /// </summary>
        public ObservableCollection<PayrollRecord> PayrollRecords
        {
            get => _payrollRecords;
            set => SetProperty(ref _payrollRecords, value);
        }

        /// <summary>
        /// سجل الراتب المحدد
        /// </summary>
        public PayrollRecord? SelectedPayrollRecord
        {
            get => _selectedPayrollRecord;
            set
            {
                if (SetProperty(ref _selectedPayrollRecord, value))
                {
                    ((RelayCommand)ApprovePayrollCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)PaySalaryCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// السنة المحددة
        /// </summary>
        public int SelectedYear
        {
            get => _selectedYear;
            set => SetProperty(ref _selectedYear, value);
        }

        /// <summary>
        /// الشهر المحدد
        /// </summary>
        public int SelectedMonth
        {
            get => _selectedMonth;
            set => SetProperty(ref _selectedMonth, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحميل الرواتب
        /// </summary>
        public ICommand LoadPayrollsCommand { get; }

        /// <summary>
        /// أمر حساب جميع الرواتب
        /// </summary>
        public ICommand CalculateAllPayrollsCommand { get; }

        /// <summary>
        /// أمر اعتماد الراتب
        /// </summary>
        public ICommand ApprovePayrollCommand { get; }

        /// <summary>
        /// أمر دفع الراتب
        /// </summary>
        public ICommand PaySalaryCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل قائمة الرواتب
        /// </summary>
        private async Task LoadPayrollsAsync()
        {
            try
            {
                IsLoading = true;
                var payrolls = await _payrollService.GetMonthlyPayrollsAsync(SelectedYear, SelectedMonth);
                
                PayrollRecords.Clear();
                foreach (var payroll in payrolls)
                {
                    PayrollRecords.Add(payroll);
                }

                _logger.LogInformation("تم تحميل {Count} سجل راتب للشهر {Month}/{Year}", 
                    payrolls.Count, SelectedMonth, SelectedYear);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الرواتب");
                await _dialogService.ShowErrorAsync("حدث خطأ في تحميل بيانات الرواتب");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// حساب جميع الرواتب
        /// </summary>
        private async Task CalculateAllPayrollsAsync()
        {
            try
            {
                var confirmed = await _dialogService.ShowConfirmationAsync(
                    $"هل أنت متأكد من حساب رواتب جميع الموظفين للشهر {SelectedMonth}/{SelectedYear}؟");

                if (!confirmed) return;

                IsLoading = true;
                var payrolls = await _payrollService.CalculateAllPayrollsAsync(SelectedYear, SelectedMonth);
                
                PayrollRecords.Clear();
                foreach (var payroll in payrolls)
                {
                    PayrollRecords.Add(payroll);
                }

                await _dialogService.ShowInformationAsync($"تم حساب {payrolls.Count} راتب بنجاح");
                _logger.LogInformation("تم حساب {Count} راتب للشهر {Month}/{Year}", 
                    payrolls.Count, SelectedMonth, SelectedYear);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الرواتب");
                await _dialogService.ShowErrorAsync("حدث خطأ في حساب الرواتب");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// اعتماد الراتب
        /// </summary>
        private async Task ApprovePayrollAsync()
        {
            try
            {
                if (SelectedPayrollRecord == null) return;

                var confirmed = await _dialogService.ShowConfirmationAsync(
                    "هل أنت متأكد من اعتماد هذا الراتب؟");

                if (!confirmed) return;

                var success = await _payrollService.ApprovePayrollAsync(SelectedPayrollRecord.Id);
                if (success)
                {
                    SelectedPayrollRecord.IsApproved = true;
                    await _dialogService.ShowInformationAsync("تم اعتماد الراتب بنجاح");
                    _logger.LogInformation("تم اعتماد الراتب: {PayrollRecordId}", SelectedPayrollRecord.Id);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في اعتماد الراتب");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد الراتب");
                await _dialogService.ShowErrorAsync("حدث خطأ في اعتماد الراتب");
            }
        }

        /// <summary>
        /// دفع الراتب
        /// </summary>
        private async Task PaySalaryAsync()
        {
            try
            {
                if (SelectedPayrollRecord == null) return;

                if (!SelectedPayrollRecord.IsApproved)
                {
                    await _dialogService.ShowWarningAsync("يجب اعتماد الراتب أولاً قبل الدفع");
                    return;
                }

                var confirmed = await _dialogService.ShowConfirmationAsync(
                    "هل أنت متأكد من دفع هذا الراتب؟");

                if (!confirmed) return;

                var success = await _payrollService.PaySalaryAsync(SelectedPayrollRecord.Id);
                if (success)
                {
                    SelectedPayrollRecord.IsPaid = true;
                    SelectedPayrollRecord.PaidDate = DateTime.Now;
                    await _dialogService.ShowInformationAsync("تم دفع الراتب بنجاح");
                    _logger.LogInformation("تم دفع الراتب: {PayrollRecordId}", SelectedPayrollRecord.Id);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في دفع الراتب");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في دفع الراتب");
                await _dialogService.ShowErrorAsync("حدث خطأ في دفع الراتب");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
