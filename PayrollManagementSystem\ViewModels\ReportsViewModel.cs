using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Services;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للتقارير
    /// </summary>
    public class ReportsViewModel : INotifyPropertyChanged
    {
        private readonly IReportService _reportService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<ReportsViewModel> _logger;

        private int _selectedYear = DateTime.Now.Year;
        private int _selectedMonth = DateTime.Now.Month;
        private DateTime _fromDate = DateTime.Now.AddMonths(-1);
        private DateTime _toDate = DateTime.Now;
        private bool _isLoading = false;

        public ReportsViewModel(
            IReportService reportService,
            IDialogService dialogService,
            ILogger<ReportsViewModel> logger)
        {
            _reportService = reportService;
            _dialogService = dialogService;
            _logger = logger;

            // تهيئة الأوامر
            GeneratePayrollReportCommand = new RelayCommand(async () => await GeneratePayrollReportAsync());
            GenerateTrialBalanceReportCommand = new RelayCommand(async () => await GenerateTrialBalanceReportAsync());
            GenerateEmployeeReportCommand = new RelayCommand(async () => await GenerateEmployeeReportAsync());
        }

        #region Properties

        /// <summary>
        /// السنة المحددة
        /// </summary>
        public int SelectedYear
        {
            get => _selectedYear;
            set => SetProperty(ref _selectedYear, value);
        }

        /// <summary>
        /// الشهر المحدد
        /// </summary>
        public int SelectedMonth
        {
            get => _selectedMonth;
            set => SetProperty(ref _selectedMonth, value);
        }

        /// <summary>
        /// من تاريخ
        /// </summary>
        public DateTime FromDate
        {
            get => _fromDate;
            set => SetProperty(ref _fromDate, value);
        }

        /// <summary>
        /// إلى تاريخ
        /// </summary>
        public DateTime ToDate
        {
            get => _toDate;
            set => SetProperty(ref _toDate, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر إنشاء تقرير كشف الرواتب
        /// </summary>
        public ICommand GeneratePayrollReportCommand { get; }

        /// <summary>
        /// أمر إنشاء تقرير الميزان التجريبي
        /// </summary>
        public ICommand GenerateTrialBalanceReportCommand { get; }

        /// <summary>
        /// أمر إنشاء تقرير بيانات الموظفين
        /// </summary>
        public ICommand GenerateEmployeeReportCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// إنشاء تقرير كشف الرواتب
        /// </summary>
        private async Task GeneratePayrollReportAsync()
        {
            try
            {
                IsLoading = true;
                
                var reportData = await _reportService.GenerateMonthlyPayrollReportAsync(SelectedYear, SelectedMonth);
                
                // حفظ التقرير
                var fileName = await _dialogService.ShowSaveFileDialogAsync(
                    "ملفات PDF (*.pdf)|*.pdf|جميع الملفات (*.*)|*.*");

                if (!string.IsNullOrEmpty(fileName))
                {
                    await System.IO.File.WriteAllBytesAsync(fileName, reportData);
                    await _dialogService.ShowInformationAsync("تم إنشاء التقرير بنجاح");
                }

                _logger.LogInformation("تم إنشاء تقرير كشف الرواتب للشهر {Month}/{Year}", SelectedMonth, SelectedYear);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير كشف الرواتب");
                await _dialogService.ShowErrorAsync("حدث خطأ في إنشاء التقرير");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إنشاء تقرير الميزان التجريبي
        /// </summary>
        private async Task GenerateTrialBalanceReportAsync()
        {
            try
            {
                IsLoading = true;
                
                var reportData = await _reportService.GenerateTrialBalanceReportAsync(FromDate, ToDate);
                
                // حفظ التقرير
                var fileName = await _dialogService.ShowSaveFileDialogAsync(
                    "ملفات PDF (*.pdf)|*.pdf|جميع الملفات (*.*)|*.*");

                if (!string.IsNullOrEmpty(fileName))
                {
                    await System.IO.File.WriteAllBytesAsync(fileName, reportData);
                    await _dialogService.ShowInformationAsync("تم إنشاء التقرير بنجاح");
                }

                _logger.LogInformation("تم إنشاء تقرير الميزان التجريبي من {FromDate} إلى {ToDate}", FromDate, ToDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الميزان التجريبي");
                await _dialogService.ShowErrorAsync("حدث خطأ في إنشاء التقرير");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إنشاء تقرير بيانات الموظفين
        /// </summary>
        private async Task GenerateEmployeeReportAsync()
        {
            try
            {
                IsLoading = true;
                
                var reportData = await _reportService.GenerateEmployeeReportAsync();
                
                // حفظ التقرير
                var fileName = await _dialogService.ShowSaveFileDialogAsync(
                    "ملفات PDF (*.pdf)|*.pdf|جميع الملفات (*.*)|*.*");

                if (!string.IsNullOrEmpty(fileName))
                {
                    await System.IO.File.WriteAllBytesAsync(fileName, reportData);
                    await _dialogService.ShowInformationAsync("تم إنشاء التقرير بنجاح");
                }

                _logger.LogInformation("تم إنشاء تقرير بيانات الموظفين");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير بيانات الموظفين");
                await _dialogService.ShowErrorAsync("حدث خطأ في إنشاء التقرير");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
