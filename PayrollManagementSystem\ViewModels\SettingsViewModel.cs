using Microsoft.Extensions.Logging;
using PayrollManagementSystem.Models;
using PayrollManagementSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace PayrollManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للإعدادات
    /// </summary>
    public class SettingsViewModel : INotifyPropertyChanged
    {
        private readonly ISettingsService _settingsService;
        private readonly IBackupService _backupService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<SettingsViewModel> _logger;

        private ObservableCollection<SystemSetting> _systemSettings = new();
        private ObservableCollection<PayrollSetting> _payrollSettings = new();
        private SystemSetting? _selectedSystemSetting;
        private PayrollSetting? _selectedPayrollSetting;
        private bool _isLoading = false;

        public SettingsViewModel(
            ISettingsService settingsService,
            IBackupService backupService,
            IDialogService dialogService,
            ILogger<SettingsViewModel> logger)
        {
            _settingsService = settingsService;
            _backupService = backupService;
            _dialogService = dialogService;
            _logger = logger;

            // تهيئة الأوامر
            LoadSettingsCommand = new RelayCommand(async () => await LoadSettingsAsync());
            SaveSystemSettingCommand = new RelayCommand(async () => await SaveSystemSettingAsync(), () => SelectedSystemSetting != null);
            CreateBackupCommand = new RelayCommand(async () => await CreateBackupAsync());
            RestoreBackupCommand = new RelayCommand(async () => await RestoreBackupAsync());

            // تحميل البيانات
            _ = LoadSettingsAsync();
        }

        #region Properties

        /// <summary>
        /// قائمة إعدادات النظام
        /// </summary>
        public ObservableCollection<SystemSetting> SystemSettings
        {
            get => _systemSettings;
            set => SetProperty(ref _systemSettings, value);
        }

        /// <summary>
        /// قائمة إعدادات الرواتب
        /// </summary>
        public ObservableCollection<PayrollSetting> PayrollSettings
        {
            get => _payrollSettings;
            set => SetProperty(ref _payrollSettings, value);
        }

        /// <summary>
        /// إعداد النظام المحدد
        /// </summary>
        public SystemSetting? SelectedSystemSetting
        {
            get => _selectedSystemSetting;
            set
            {
                if (SetProperty(ref _selectedSystemSetting, value))
                {
                    ((RelayCommand)SaveSystemSettingCommand).RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// إعداد الراتب المحدد
        /// </summary>
        public PayrollSetting? SelectedPayrollSetting
        {
            get => _selectedPayrollSetting;
            set => SetProperty(ref _selectedPayrollSetting, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحميل الإعدادات
        /// </summary>
        public ICommand LoadSettingsCommand { get; }

        /// <summary>
        /// أمر حفظ إعداد النظام
        /// </summary>
        public ICommand SaveSystemSettingCommand { get; }

        /// <summary>
        /// أمر إنشاء نسخة احتياطية
        /// </summary>
        public ICommand CreateBackupCommand { get; }

        /// <summary>
        /// أمر استعادة نسخة احتياطية
        /// </summary>
        public ICommand RestoreBackupCommand { get; }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private async Task LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;
                
                // تحميل إعدادات النظام
                var systemSettings = await _settingsService.GetAllSystemSettingsAsync();
                SystemSettings.Clear();
                foreach (var setting in systemSettings)
                {
                    SystemSettings.Add(setting);
                }

                // تحميل إعدادات الرواتب
                var payrollSettings = await _settingsService.GetPayrollSettingsAsync();
                PayrollSettings.Clear();
                foreach (var setting in payrollSettings)
                {
                    PayrollSettings.Add(setting);
                }

                _logger.LogInformation("تم تحميل {SystemCount} إعداد نظام و {PayrollCount} إعداد راتب", 
                    systemSettings.Count, payrollSettings.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الإعدادات");
                await _dialogService.ShowErrorAsync("حدث خطأ في تحميل الإعدادات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// حفظ إعداد النظام
        /// </summary>
        private async Task SaveSystemSettingAsync()
        {
            try
            {
                if (SelectedSystemSetting == null) return;

                var success = await _settingsService.UpdateSystemSettingAsync(
                    SelectedSystemSetting.SettingName, 
                    SelectedSystemSetting.SettingValue);

                if (success)
                {
                    await _dialogService.ShowInformationAsync("تم حفظ الإعداد بنجاح");
                    _logger.LogInformation("تم حفظ إعداد النظام: {SettingName}", SelectedSystemSetting.SettingName);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في حفظ الإعداد");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعداد النظام");
                await _dialogService.ShowErrorAsync("حدث خطأ في حفظ الإعداد");
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private async Task CreateBackupAsync()
        {
            try
            {
                var backupPath = await _dialogService.ShowSaveFileDialogAsync(
                    "ملفات النسخ الاحتياطي (*.bak)|*.bak|جميع الملفات (*.*)|*.*");

                if (string.IsNullOrEmpty(backupPath)) return;

                IsLoading = true;
                var success = await _backupService.CreateBackupAsync(backupPath);

                if (success)
                {
                    await _dialogService.ShowInformationAsync("تم إنشاء النسخة الاحتياطية بنجاح");
                    _logger.LogInformation("تم إنشاء نسخة احتياطية في: {BackupPath}", backupPath);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في إنشاء النسخة الاحتياطية");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                await _dialogService.ShowErrorAsync("حدث خطأ في إنشاء النسخة الاحتياطية");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        private async Task RestoreBackupAsync()
        {
            try
            {
                var confirmed = await _dialogService.ShowConfirmationAsync(
                    "تحذير: ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية. هل أنت متأكد؟");

                if (!confirmed) return;

                var backupPath = await _dialogService.ShowOpenFileDialogAsync(
                    "ملفات النسخ الاحتياطي (*.bak)|*.bak|جميع الملفات (*.*)|*.*");

                if (string.IsNullOrEmpty(backupPath)) return;

                IsLoading = true;
                var success = await _backupService.RestoreBackupAsync(backupPath);

                if (success)
                {
                    await _dialogService.ShowInformationAsync("تم استعادة النسخة الاحتياطية بنجاح");
                    _logger.LogInformation("تم استعادة نسخة احتياطية من: {BackupPath}", backupPath);
                }
                else
                {
                    await _dialogService.ShowErrorAsync("فشل في استعادة النسخة الاحتياطية");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استعادة النسخة الاحتياطية");
                await _dialogService.ShowErrorAsync("حدث خطأ في استعادة النسخة الاحتياطية");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
