<Window x:Class="PayrollManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة الرواتب الموحد"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{StaticResource BackgroundBrush}"
        FontFamily="{StaticResource ArabicFont}"
        FontSize="14">

    <Window.Resources>
        <!-- أنماط مخصصة للنافذة -->
        <Style x:Key="LoginCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="40"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
        </Style>

        <Style x:Key="LoginButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="LoginTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="50"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="LoginPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Height" Value="50"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- خلفية متدرجة -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#E3F2FD" Offset="0"/>
                <GradientStop Color="#BBDEFB" Offset="0.5"/>
                <GradientStop Color="#90CAF9" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- المحتوى الرئيسي -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيسر - معلومات النظام -->
            <StackPanel Grid.Column="0" 
                       VerticalAlignment="Center" 
                       HorizontalAlignment="Center"
                       Margin="50">
                
                <!-- شعار النظام -->
                <materialDesign:PackIcon Kind="AccountBalance" 
                                       Width="120" Height="120"
                                       Foreground="{StaticResource PrimaryBrush}"
                                       Margin="0,0,0,30"/>

                <!-- اسم النظام -->
                <TextBlock Text="نظام إدارة الرواتب الموحد"
                          Style="{StaticResource HeaderTextStyle}"
                          FontSize="28"
                          TextAlignment="Center"
                          Margin="0,0,0,15"/>

                <!-- وصف النظام -->
                <TextBlock Text="نظام محاسبي شامل لإدارة رواتب الموظفين"
                          Style="{StaticResource ArabicTextStyle}"
                          FontSize="16"
                          TextAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          Margin="0,0,0,30"/>

                <!-- معلومات الإصدار -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" 
                                           Width="16" Height="16"
                                           Foreground="{StaticResource InfoBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="الإصدار 1.0.0"
                              Style="{StaticResource ArabicTextStyle}"
                              FontSize="12"
                              Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>
            </StackPanel>

            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <materialDesign:Card Grid.Column="1" 
                               Style="{StaticResource LoginCardStyle}"
                               VerticalAlignment="Center">
                
                <StackPanel>
                    <!-- عنوان تسجيل الدخول -->
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Center"
                               Margin="0,0,0,30">
                        <materialDesign:PackIcon Kind="Login" 
                                               Width="24" Height="24"
                                               Foreground="{StaticResource PrimaryBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="تسجيل الدخول"
                                  Style="{StaticResource HeaderTextStyle}"
                                  FontSize="22"/>
                    </StackPanel>

                    <!-- رسالة الترحيب -->
                    <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                              Style="{StaticResource ArabicTextStyle}"
                              TextAlignment="Center"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              Margin="0,0,0,30"/>

                    <!-- حقل اسم المستخدم -->
                    <TextBox x:Name="UsernameTextBox"
                            Style="{StaticResource LoginTextBoxStyle}"
                            materialDesign:HintAssist.Hint="اسم المستخدم"
                            materialDesign:TextFieldAssist.HasLeadingIcon="True"
                            materialDesign:TextFieldAssist.LeadingIcon="Account"
                            Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,0,20"/>

                    <!-- حقل كلمة المرور -->
                    <PasswordBox x:Name="PasswordBox"
                                Style="{StaticResource LoginPasswordBoxStyle}"
                                materialDesign:HintAssist.Hint="كلمة المرور"
                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                materialDesign:TextFieldAssist.LeadingIcon="Lock"
                                PasswordChanged="PasswordBox_PasswordChanged"
                                Margin="0,0,0,20"/>

                    <!-- خيار تذكر المستخدم -->
                    <CheckBox x:Name="RememberMeCheckBox"
                             Content="تذكرني"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="14"
                             IsChecked="{Binding RememberMe}"
                             Margin="0,0,0,25"/>

                    <!-- زر تسجيل الدخول -->
                    <Button x:Name="LoginButton"
                           Content="تسجيل الدخول"
                           Style="{StaticResource LoginButtonStyle}"
                           Command="{Binding LoginCommand}"
                           IsDefault="True"
                           Margin="0,0,0,15"/>

                    <!-- رابط نسيان كلمة المرور -->
                    <Button Content="نسيت كلمة المرور؟"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           FontFamily="{StaticResource ArabicFont}"
                           FontSize="13"
                           Foreground="{StaticResource PrimaryBrush}"
                           HorizontalAlignment="Center"
                           Command="{Binding ForgotPasswordCommand}"
                           Margin="0,10,0,0"/>

                    <!-- رسالة الخطأ -->
                    <Border x:Name="ErrorBorder"
                           Background="{StaticResource ErrorBrush}"
                           CornerRadius="5"
                           Padding="15,10"
                           Margin="0,20,0,0"
                           Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle" 
                                                   Width="16" Height="16"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                      Foreground="White"
                                      FontFamily="{StaticResource ArabicFont}"
                                      FontSize="13"
                                      TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- مؤشر التحميل -->
                    <ProgressBar x:Name="LoadingProgressBar"
                                Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Width="30" Height="30"
                                IsIndeterminate="True"
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Margin="0,20,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar VerticalAlignment="Bottom"
                  Style="{StaticResource StatusBarStyle}"
                  Height="25">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Copyright" 
                                           Width="12" Height="12"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="© 2024 شركة التطوير المتقدم - جميع الحقوق محفوظة"
                              FontSize="11"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Clock" 
                                           Width="12" Height="12"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock x:Name="CurrentTimeTextBlock"
                              FontSize="11"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
