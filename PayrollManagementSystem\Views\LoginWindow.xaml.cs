using PayrollManagementSystem.ViewModels;
using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace PayrollManagementSystem.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;
        private readonly DispatcherTimer _timeTimer;

        public LoginWindow()
        {
            InitializeComponent();
            
            // الحصول على ViewModel من حاوي الحقن
            _viewModel = App.GetService<LoginViewModel>();
            DataContext = _viewModel;

            // ربط أحداث ViewModel
            _viewModel.LoginSuccessful += OnLoginSuccessful;
            _viewModel.LoginFailed += OnLoginFailed;

            // تهيئة مؤقت الوقت
            _timeTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timeTimer.Tick += TimeTimer_Tick;
            _timeTimer.Start();

            // تحديث الوقت الحالي
            UpdateCurrentTime();

            // التركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        /// <summary>
        /// معالج تغيير كلمة المرور
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is LoginViewModel viewModel)
            {
                viewModel.Password = PasswordBox.Password;
            }
        }

        /// <summary>
        /// معالج نجاح تسجيل الدخول
        /// </summary>
        private void OnLoginSuccessful(object? sender, EventArgs e)
        {
            try
            {
                // إنشاء النافذة الرئيسية
                var mainWindow = App.GetService<MainWindow>();
                
                // إظهار النافذة الرئيسية
                mainWindow.Show();
                
                // إغلاق نافذة تسجيل الدخول
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في فتح النافذة الرئيسية:\n{ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج فشل تسجيل الدخول
        /// </summary>
        private void OnLoginFailed(object? sender, string errorMessage)
        {
            // التركيز على حقل كلمة المرور وتفريغه
            PasswordBox.Clear();
            PasswordBox.Focus();

            // اهتزاز النافذة للإشارة إلى الخطأ
            AnimateWindowShake();
        }

        /// <summary>
        /// تحديث الوقت الحالي
        /// </summary>
        private void TimeTimer_Tick(object? sender, EventArgs e)
        {
            UpdateCurrentTime();
        }

        /// <summary>
        /// تحديث عرض الوقت الحالي
        /// </summary>
        private void UpdateCurrentTime()
        {
            CurrentTimeTextBlock.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy - HH:mm:ss");
        }

        /// <summary>
        /// تأثير اهتزاز النافذة
        /// </summary>
        private void AnimateWindowShake()
        {
            var originalLeft = Left;
            var shakeTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(50)
            };
            
            var shakeCount = 0;
            var maxShakes = 6;
            var shakeDistance = 10;

            shakeTimer.Tick += (s, e) =>
            {
                if (shakeCount < maxShakes)
                {
                    Left = originalLeft + (shakeCount % 2 == 0 ? shakeDistance : -shakeDistance);
                    shakeCount++;
                }
                else
                {
                    Left = originalLeft;
                    shakeTimer.Stop();
                }
            };

            shakeTimer.Start();
        }

        /// <summary>
        /// معالج الضغط على مفاتيح النافذة
        /// </summary>
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);

            // تسجيل الدخول عند الضغط على Enter
            if (e.Key == Key.Enter && !_viewModel.IsLoading)
            {
                if (_viewModel.LoginCommand.CanExecute(null))
                {
                    _viewModel.LoginCommand.Execute(null);
                }
            }
            // إغلاق النافذة عند الضغط على Escape
            else if (e.Key == Key.Escape)
            {
                Close();
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // إيقاف المؤقت
            _timeTimer?.Stop();

            // إلغاء ربط الأحداث
            if (_viewModel != null)
            {
                _viewModel.LoginSuccessful -= OnLoginSuccessful;
                _viewModel.LoginFailed -= OnLoginFailed;
            }

            base.OnClosed(e);

            // إغلاق التطبيق إذا لم تكن النافذة الرئيسية مفتوحة
            if (Application.Current.MainWindow == this)
            {
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// معالج محاولة إغلاق النافذة
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // منع الإغلاق أثناء عملية تسجيل الدخول
            if (_viewModel?.IsLoading == true)
            {
                e.Cancel = true;
                MessageBox.Show("يرجى انتظار انتهاء عملية تسجيل الدخول", 
                    "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// معالج تحميل النافذة
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تطبيق تأثير الظهور التدريجي
            var fadeInAnimation = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(500)
            };

            BeginAnimation(OpacityProperty, fadeInAnimation);

            // التحقق من وجود بيانات مستخدم محفوظة
            _viewModel.LoadSavedCredentials();
        }

        /// <summary>
        /// معالج النقر على رابط نسيان كلمة المرور
        /// </summary>
        private void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور", 
                "نسيان كلمة المرور", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج تغيير حجم النافذة
        /// </summary>
        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);

            // تعطيل زر التكبير
            var hwnd = new System.Windows.Interop.WindowInteropHelper(this).Handle;
            var style = GetWindowLong(hwnd, GWL_STYLE);
            SetWindowLong(hwnd, GWL_STYLE, style & ~WS_MAXIMIZEBOX);
        }

        #region Win32 API للتحكم في النافذة
        private const int GWL_STYLE = -16;
        private const int WS_MAXIMIZEBOX = 0x10000;

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);
        #endregion
    }
}
