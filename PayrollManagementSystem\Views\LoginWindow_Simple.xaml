<Window x:Class="PayrollManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة الرواتب الموحد"
        Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#FAFAFA"
        FontFamily="Sakkal Majalla"
        FontSize="14">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الجانب الأيسر - معلومات النظام -->
        <StackPanel Grid.Column="0" 
                   VerticalAlignment="Center" 
                   HorizontalAlignment="Center"
                   Margin="50">
            
            <!-- شعار النظام -->
            <Ellipse Width="120" Height="120" Fill="#1976D2" Margin="0,0,0,30"/>

            <!-- اسم النظام -->
            <TextBlock Text="نظام إدارة الرواتب الموحد"
                      FontSize="24"
                      FontWeight="Bold"
                      TextAlignment="Center"
                      Foreground="#1976D2"
                      Margin="0,0,0,15"/>

            <!-- وصف النظام -->
            <TextBlock Text="نظام محاسبي شامل لإدارة رواتب الموظفين"
                      FontSize="16"
                      TextAlignment="Center"
                      Foreground="#757575"
                      Margin="0,0,0,30"/>

            <!-- معلومات الإصدار -->
            <TextBlock Text="الإصدار 1.0.0"
                      FontSize="12"
                      TextAlignment="Center"
                      Foreground="#757575"/>
        </StackPanel>

        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <Border Grid.Column="1" 
               Background="White"
               CornerRadius="10"
               Padding="40"
               VerticalAlignment="Center"
               Margin="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" BlurRadius="10" ShadowDepth="3" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel>
                <!-- عنوان تسجيل الدخول -->
                <TextBlock Text="تسجيل الدخول"
                          FontSize="22"
                          FontWeight="Bold"
                          Foreground="#1976D2"
                          TextAlignment="Center"
                          Margin="0,0,0,30"/>

                <!-- رسالة الترحيب -->
                <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                          TextAlignment="Center"
                          Foreground="#757575"
                          Margin="0,0,0,30"/>

                <!-- حقل اسم المستخدم -->
                <TextBlock Text="اسم المستخدم" Margin="0,0,0,5"/>
                <TextBox x:Name="UsernameTextBox"
                        Height="40"
                        Padding="10"
                        FontSize="14"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"/>

                <!-- حقل كلمة المرور -->
                <TextBlock Text="كلمة المرور" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                            Height="40"
                            Padding="10"
                            FontSize="14"
                            PasswordChanged="PasswordBox_PasswordChanged"
                            Margin="0,0,0,20"/>

                <!-- خيار تذكر المستخدم -->
                <CheckBox x:Name="RememberMeCheckBox"
                         Content="تذكرني"
                         FontSize="14"
                         IsChecked="{Binding RememberMe}"
                         Margin="0,0,0,25"/>

                <!-- زر تسجيل الدخول -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Height="45"
                       FontSize="16"
                       Background="#1976D2"
                       Foreground="White"
                       BorderThickness="0"
                       Command="{Binding LoginCommand}"
                       IsDefault="True"
                       Margin="0,0,0,15"/>

                <!-- رسالة الخطأ -->
                <Border x:Name="ErrorBorder"
                       Background="#F44336"
                       CornerRadius="5"
                       Padding="15,10"
                       Margin="0,20,0,0"
                       Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding ErrorMessage}"
                              Foreground="White"
                              FontSize="13"
                              TextWrapping="Wrap"/>
                </Border>

                <!-- مؤشر التحميل -->
                <ProgressBar x:Name="LoadingProgressBar"
                            Height="4"
                            IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,20,0,0"/>
            </StackPanel>
        </Border>

        <!-- شريط الحالة -->
        <StatusBar Grid.Column="0" Grid.ColumnSpan="3"
                  VerticalAlignment="Bottom"
                  Background="#F5F5F5"
                  Height="25">
            <StatusBarItem>
                <TextBlock Text="© 2024 شركة التطوير المتقدم - جميع الحقوق محفوظة"
                          FontSize="11"/>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Left">
                <TextBlock x:Name="CurrentTimeTextBlock"
                          FontSize="11"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
