<Window x:Class="PayrollManagementSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة الرواتب الموحد"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        Background="{StaticResource BackgroundBrush}"
        FontFamily="{StaticResource ArabicFont}"
        FontSize="14">

    <Window.Resources>
        <!-- أنماط مخصصة للنافذة -->
        <Style x:Key="SidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="MenuHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextStyle}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicBoldFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="20,15,20,10"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="0,0,0,5">
            <Grid Height="60">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- شعار النظام -->
                <StackPanel Grid.Column="0" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <materialDesign:PackIcon Kind="AccountBalance" 
                                           Width="32" Height="32"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="نظام إدارة الرواتب الموحد"
                              Style="{StaticResource HeaderTextStyle}"
                              Foreground="White"
                              FontSize="20"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- عنوان الصفحة الحالية -->
                <TextBlock Grid.Column="1"
                          Text="{Binding CurrentPageTitle}"
                          Style="{StaticResource SubHeaderTextStyle}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"/>

                <!-- معلومات المستخدم -->
                <StackPanel Grid.Column="2" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <StackPanel Orientation="Vertical" 
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0">
                        <TextBlock Text="{Binding CurrentUserName}"
                                  Style="{StaticResource ArabicTextStyle}"
                                  Foreground="White"
                                  FontSize="13"
                                  HorizontalAlignment="Right"/>
                        <TextBlock Text="{Binding CurrentUserType}"
                                  Style="{StaticResource ArabicTextStyle}"
                                  Foreground="{StaticResource AccentLightBrush}"
                                  FontSize="11"
                                  HorizontalAlignment="Right"/>
                    </StackPanel>
                    
                    <materialDesign:PackIcon Kind="Account" 
                                           Width="24" Height="24"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    
                    <Button Content="خروج"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Foreground="White"
                           BorderBrush="White"
                           Command="{Binding LogoutCommand}"
                           Padding="15,5"
                           Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي -->
            <materialDesign:Card Grid.Column="0" 
                               Background="{StaticResource PrimaryBrush}"
                               materialDesign:ShadowAssist.ShadowDepth="Depth2"
                               Margin="5,0,5,5">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- قائمة إدارة الموظفين -->
                        <TextBlock Text="إدارة الموظفين" Style="{StaticResource MenuHeaderStyle}"/>
                        <Button Content="قائمة الموظفين"
                               Style="{StaticResource SidebarButtonStyle}"
                               Command="{Binding NavigateToEmployeesCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountGroup" 
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- قائمة إدارة الرواتب -->
                        <TextBlock Text="إدارة الرواتب" Style="{StaticResource MenuHeaderStyle}"/>
                        <Button Content="حساب الرواتب"
                               Style="{StaticResource SidebarButtonStyle}"
                               Command="{Binding NavigateToPayrollCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Calculator" 
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- قائمة المحاسبة -->
                        <TextBlock Text="المحاسبة" Style="{StaticResource MenuHeaderStyle}"/>
                        <Button Content="دليل الحسابات"
                               Style="{StaticResource SidebarButtonStyle}"
                               Command="{Binding NavigateToAccountingCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="BookOpenPageVariant" 
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- قائمة التقارير -->
                        <TextBlock Text="التقارير" Style="{StaticResource MenuHeaderStyle}"/>
                        <Button Content="تقارير الرواتب"
                               Style="{StaticResource SidebarButtonStyle}"
                               Command="{Binding NavigateToReportsCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileChart" 
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- قائمة الإعدادات -->
                        <TextBlock Text="الإعدادات" Style="{StaticResource MenuHeaderStyle}"/>
                        <Button Content="إعدادات النظام"
                               Style="{StaticResource SidebarButtonStyle}"
                               Command="{Binding NavigateToSettingsCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Settings" 
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- منطقة المحتوى -->
            <materialDesign:Card Grid.Column="1" 
                               Style="{StaticResource CardStyle}"
                               Margin="0,0,5,5">
                <Grid>
                    <!-- محتوى الصفحة -->
                    <ContentPresenter Content="{Binding CurrentPageContent}"/>
                    
                    <!-- صفحة ترحيبية افتراضية -->
                    <StackPanel VerticalAlignment="Center" 
                               HorizontalAlignment="Center"
                               Visibility="{Binding CurrentPageContent, Converter={x:Static Converters:NullToVisibilityConverter.Instance}}">
                        <materialDesign:PackIcon Kind="Home" 
                                               Width="100" Height="100"
                                               Foreground="{StaticResource PrimaryLightBrush}"
                                               Margin="0,0,0,20"/>
                        <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                                  Style="{StaticResource HeaderTextStyle}"
                                  FontSize="24"
                                  TextAlignment="Center"
                                  Margin="0,0,0,10"/>
                        <TextBlock Text="اختر من القائمة الجانبية للبدء"
                                  Style="{StaticResource ArabicTextStyle}"
                                  FontSize="16"
                                  Foreground="{StaticResource TextSecondaryBrush}"
                                  TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Information" 
                                           Width="14" Height="14"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="جاهز" FontSize="12"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Center">
                <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='اليوم: {0:dddd، dd MMMM yyyy}'}"
                          FontSize="12"/>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Clock" 
                                           Width="14" Height="14"
                                           VerticalAlignment="Center"
                                           Margin="0,0,5,0"/>
                    <TextBlock x:Name="CurrentTimeTextBlock" FontSize="12"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
