using PayrollManagementSystem.ViewModels;
using System;
using System.Windows;
using System.Windows.Threading;

namespace PayrollManagementSystem.Views
{
    /// <summary>
    /// النافذة الرئيسية
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly MainViewModel _viewModel;
        private readonly DispatcherTimer _timeTimer;

        public MainWindow()
        {
            InitializeComponent();
            
            // الحصول على ViewModel من حاوي الحقن
            _viewModel = App.GetService<MainViewModel>();
            DataContext = _viewModel;

            // تهيئة مؤقت الوقت
            _timeTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timeTimer.Tick += TimeTimer_Tick;
            _timeTimer.Start();

            // تحديث الوقت الحالي
            UpdateCurrentTime();

            // تعيين النافذة الرئيسية
            Application.Current.MainWindow = this;
        }

        /// <summary>
        /// معالج تحديث الوقت
        /// </summary>
        private void TimeTimer_Tick(object? sender, EventArgs e)
        {
            UpdateCurrentTime();
        }

        /// <summary>
        /// تحديث عرض الوقت الحالي
        /// </summary>
        private void UpdateCurrentTime()
        {
            CurrentTimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // إيقاف المؤقت
            _timeTimer?.Stop();

            base.OnClosed(e);

            // إغلاق التطبيق
            Application.Current.Shutdown();
        }

        /// <summary>
        /// معالج تحميل النافذة
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تطبيق تأثير الظهور التدريجي
            var fadeInAnimation = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(500)
            };

            BeginAnimation(OpacityProperty, fadeInAnimation);
        }
    }
}
