﻿<Window x:Class="WorkingPayrollApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة الرواتب الموحد"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Tahoma"
        FontSize="14"
        Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="5,5,0,0">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <Ellipse Width="50" Height="50" Fill="White" Margin="0,0,15,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="نظام إدارة الرواتب الموحد"
                                  FontSize="20" FontWeight="Bold"
                                  Foreground="White"/>
                        <TextBlock Text="Unified Payroll Management System"
                                  FontSize="12"
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left"
                           VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="مرحباً: مدير النظام"
                              Foreground="White" FontSize="14"
                              VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button Content="خروج" Background="#1976D2" Foreground="White"
                           BorderBrush="White" Padding="15,5"
                           Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- القائمة الجانبية -->
            <Border Grid.Column="0" Background="White" CornerRadius="5"
                   BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,10,0">
                <StackPanel Margin="10">
                    <TextBlock Text="القوائم الرئيسية" FontWeight="Bold"
                              FontSize="16" Margin="0,0,0,15"
                              Foreground="#2196F3"/>

                    <Button Content="🏢 إدارة الموظفين"
                           Style="{StaticResource MenuButtonStyle}"
                           Click="MenuButton_Click" Tag="employees"/>

                    <Button Content="💰 إدارة الرواتب"
                           Style="{StaticResource MenuButtonStyle}"
                           Click="MenuButton_Click" Tag="payroll"/>

                    <Button Content="📊 المحاسبة"
                           Style="{StaticResource MenuButtonStyle}"
                           Click="MenuButton_Click" Tag="accounting"/>

                    <Button Content="📈 التقارير"
                           Style="{StaticResource MenuButtonStyle}"
                           Click="MenuButton_Click" Tag="reports"/>

                    <Button Content="⚙️ الإعدادات"
                           Style="{StaticResource MenuButtonStyle}"
                           Click="MenuButton_Click" Tag="settings"/>
                </StackPanel>
            </Border>

            <!-- منطقة المحتوى -->
            <Border Grid.Column="1" Background="White" CornerRadius="5"
                   BorderBrush="#E0E0E0" BorderThickness="1">
                <Grid>
                    <!-- محتوى ديناميكي -->
                    <ContentControl x:Name="ContentArea"/>

                    <!-- الصفحة الترحيبية -->
                    <StackPanel x:Name="WelcomePanel" VerticalAlignment="Center"
                               HorizontalAlignment="Center">
                        <TextBlock Text="🏠" FontSize="80" HorizontalAlignment="Center"
                                  Margin="0,0,0,20"/>
                        <TextBlock Text="مرحباً بك في نظام إدارة الرواتب الموحد"
                                  FontSize="24" FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Foreground="#2196F3" Margin="0,0,0,10"/>
                        <TextBlock Text="اختر من القائمة الجانبية للبدء"
                                  FontSize="16" HorizontalAlignment="Center"
                                  Foreground="#666"/>
                        <TextBlock Text="Choose from the sidebar to start"
                                  FontSize="14" HorizontalAlignment="Center"
                                  Foreground="#999" Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#EEEEEE" CornerRadius="0,0,5,5">
            <Grid>
                <TextBlock Text="جاهز | Ready" VerticalAlignment="Center"
                          Margin="10,0" FontSize="12"/>
                <TextBlock x:Name="TimeLabel" HorizontalAlignment="Left"
                          VerticalAlignment="Center" Margin="10,0"
                          FontSize="12"/>
            </Grid>
        </Border>
    </Grid>

    <Window.Resources>
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
</Window>
