﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace WorkingPayrollApp;

/// <summary>
/// نظام إدارة الرواتب الموحد - النافذة الرئيسية
/// </summary>
public partial class MainWindow : Window
{
    private readonly DispatcherTimer _timer;

    public MainWindow()
    {
        InitializeComponent();

        // تهيئة مؤقت الوقت
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timer.Tick += Timer_Tick;
        _timer.Start();

        UpdateTime();

        // رسالة ترحيب
        MessageBox.Show("مرحباً بك في نظام إدارة الرواتب الموحد!\n\nWelcome to the Unified Payroll Management System!",
            "مرحباً | Welcome", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        UpdateTime();
    }

    private void UpdateTime()
    {
        TimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
    }

    private void MenuButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string tag)
        {
            WelcomePanel.Visibility = Visibility.Collapsed;

            var content = new StackPanel
            {
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            switch (tag)
            {
                case "employees":
                    ShowEmployeesContent(content);
                    break;
                case "payroll":
                    ShowPayrollContent(content);
                    break;
                case "accounting":
                    ShowAccountingContent(content);
                    break;
                case "reports":
                    ShowReportsContent(content);
                    break;
                case "settings":
                    ShowSettingsContent(content);
                    break;
            }

            ContentArea.Content = content;
        }
    }

    private void ShowEmployeesContent(StackPanel content)
    {
        content.Children.Add(new TextBlock
        {
            Text = "🏢 إدارة الموظفين",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Foreground = System.Windows.Media.Brushes.DarkBlue
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إدارة بيانات الموظفين وإضافة موظفين جدد",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        });

        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var addButton = new Button
        {
            Content = "إضافة موظف جديد",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Green,
            Foreground = System.Windows.Media.Brushes.White
        };
        addButton.Click += (s, e) => MessageBox.Show("سيتم إضافة موظف جديد", "إضافة موظف");

        var listButton = new Button
        {
            Content = "عرض قائمة الموظفين",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Blue,
            Foreground = System.Windows.Media.Brushes.White
        };
        listButton.Click += (s, e) => MessageBox.Show("سيتم عرض قائمة الموظفين", "قائمة الموظفين");

        buttonPanel.Children.Add(addButton);
        buttonPanel.Children.Add(listButton);
        content.Children.Add(buttonPanel);
    }

    private void ShowPayrollContent(StackPanel content)
    {
        content.Children.Add(new TextBlock
        {
            Text = "💰 إدارة الرواتب",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Foreground = System.Windows.Media.Brushes.DarkGreen
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك حساب واعتماد رواتب الموظفين",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        });

        var calculateButton = new Button
        {
            Content = "حساب الرواتب",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Orange,
            Foreground = System.Windows.Media.Brushes.White
        };
        calculateButton.Click += (s, e) => MessageBox.Show("سيتم حساب رواتب جميع الموظفين", "حساب الرواتب");

        content.Children.Add(calculateButton);
    }

    private void ShowAccountingContent(StackPanel content)
    {
        content.Children.Add(new TextBlock
        {
            Text = "📊 النظام المحاسبي",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Foreground = System.Windows.Media.Brushes.Purple
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إدارة دليل الحسابات والقيود المحاسبية",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        });

        var accountsButton = new Button
        {
            Content = "دليل الحسابات",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Purple,
            Foreground = System.Windows.Media.Brushes.White
        };
        accountsButton.Click += (s, e) => MessageBox.Show("سيتم عرض دليل الحسابات", "دليل الحسابات");

        content.Children.Add(accountsButton);
    }

    private void ShowReportsContent(StackPanel content)
    {
        content.Children.Add(new TextBlock
        {
            Text = "📈 التقارير",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Foreground = System.Windows.Media.Brushes.Red
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك إنشاء وطباعة التقارير المختلفة",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        });

        var reportButton = new Button
        {
            Content = "تقرير الرواتب",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Red,
            Foreground = System.Windows.Media.Brushes.White
        };
        reportButton.Click += (s, e) => MessageBox.Show("سيتم إنشاء تقرير الرواتب", "تقرير الرواتب");

        content.Children.Add(reportButton);
    }

    private void ShowSettingsContent(StackPanel content)
    {
        content.Children.Add(new TextBlock
        {
            Text = "⚙️ الإعدادات",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Foreground = System.Windows.Media.Brushes.Gray
        });

        content.Children.Add(new TextBlock
        {
            Text = "هنا يمكنك تعديل إعدادات النظام",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        });

        var settingsButton = new Button
        {
            Content = "إعدادات النظام",
            Width = 150,
            Height = 40,
            Margin = new Thickness(10),
            Background = System.Windows.Media.Brushes.Gray,
            Foreground = System.Windows.Media.Brushes.White
        };
        settingsButton.Click += (s, e) => MessageBox.Show("سيتم فتح إعدادات النظام", "الإعدادات");

        content.Children.Add(settingsButton);
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق النظام؟\nDo you want to close the system?",
            "إغلاق النظام | Close System",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            Close();
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        base.OnClosed(e);
    }
}