<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>

  <connectionStrings>
    <!-- اتصال قاعدة البيانات المحلية -->
    <add name="PayrollConnectionString" 
         connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\PayrollSystem.mdf;Integrated Security=True;Connect Timeout=30" 
         providerName="System.Data.SqlClient" />
    
    <!-- اتصال قاعدة البيانات عبر الشبكة - يمكن تفعيله عند الحاجة -->
    <!--
    <add name="PayrollConnectionString" 
         connectionString="Data Source=SERVER_NAME;Initial Catalog=PayrollSystem;Integrated Security=True;Connect Timeout=30" 
         providerName="System.Data.SqlClient" />
    -->
    
    <!-- اتصال قاعدة البيانات مع اسم مستخدم وكلمة مرور -->
    <!--
    <add name="PayrollConnectionString" 
         connectionString="Data Source=SERVER_NAME;Initial Catalog=PayrollSystem;User ID=username;Password=password;Connect Timeout=30" 
         providerName="System.Data.SqlClient" />
    -->
  </connectionStrings>

  <appSettings>
    <!-- إعدادات التطبيق العامة -->
    <add key="CompanyName" value="وزارة الشباب والرياضة" />
    <add key="SystemName" value="نظام الرواتب" />
    <add key="SystemVersion" value="1.0.0" />
    <add key="DefaultLanguage" value="ar-IQ" />
    <add key="DefaultFont" value="Sakkal Majalla" />
    <add key="DefaultFontSize" value="14.25" />
    
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="BackupPath" value="C:\PayrollBackups\" />
    <add key="AutoBackupEnabled" value="true" />
    <add key="AutoBackupInterval" value="24" /> <!-- بالساعات -->
    
    <!-- إعدادات التقارير -->
    <add key="ReportsPath" value="C:\PayrollReports\" />
    <add key="TempPath" value="C:\Temp\" />
    
    <!-- إعدادات الأمان -->
    <add key="SessionTimeout" value="30" /> <!-- بالدقائق -->
    <add key="PasswordMinLength" value="6" />
    <add key="MaxLoginAttempts" value="3" />
    
    <!-- إعدادات الرواتب -->
    <add key="PayrollCalculationMethod" value="Monthly" />
    <add key="TaxRate" value="15" /> <!-- نسبة ضريبة الدخل -->
    <add key="RetirementRate" value="10" /> <!-- نسبة تقاعد الموظفين -->
    <add key="EmployerContributionRate" value="15" /> <!-- نسبة مساهمة الدائرة -->
    
    <!-- إعدادات استيراد البيانات -->
    <add key="ImportPath" value="C:\PayrollImports\" />
    <add key="AllowedFileTypes" value=".xlsx,.xls,.csv" />
    <add key="MaxFileSize" value="10485760" /> <!-- 10 MB -->
  </appSettings>

  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>

  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
