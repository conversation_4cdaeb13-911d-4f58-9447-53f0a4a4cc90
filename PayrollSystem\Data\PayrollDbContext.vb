Imports System
Imports System.Data.Entity
Imports System.Data.Entity.ModelConfiguration.Conventions
Imports System.Configuration

''' <summary>
''' سياق قاعدة البيانات لنظام الرواتب
''' </summary>
Public Class PayrollDbContext
    Inherits DbContext

    Public Sub New()
        MyBase.New("PayrollConnectionString")
        Database.SetInitializer(New PayrollDbInitializer())
    End Sub

    ' جداول إدارة المستخدمين
    Public Property Users As DbSet(Of User)
    Public Property Groups As DbSet(Of Group)
    Public Property UserGroups As DbSet(Of UserGroup)
    Public Property Permissions As DbSet(Of Permission)
    Public Property UserPermissions As DbSet(Of UserPermission)
    Public Property GroupPermissions As DbSet(Of GroupPermission)
    Public Property AuditLogs As DbSet(Of AuditLog)

    ' جداول الهيكل التنظيمي
    Public Property Departments As DbSet(Of Department)
    Public Property Sections As DbSet(Of Section)
    Public Property Divisions As DbSet(Of Division)
    Public Property JobTitles As DbSet(Of JobTitle)
    Public Property JobGrades As DbSet(Of JobGrade)
    Public Property Stages As DbSet(Of Stage)
    Public Property Educations As DbSet(Of Education)
    Public Property Positions As DbSet(Of Position)

    ' جداول المحاسبة
    Public Property ChartOfAccounts As DbSet(Of ChartOfAccount)
    Public Property AccountingPeriods As DbSet(Of AccountingPeriod)
    Public Property JournalEntries As DbSet(Of JournalEntry)
    Public Property JournalEntryDetails As DbSet(Of JournalEntryDetail)
    Public Property Banks As DbSet(Of Bank)
    Public Property BankBranches As DbSet(Of BankBranch)
    Public Property BankAccounts As DbSet(Of BankAccount)

    ' جداول الموظفين والرواتب
    Public Property Employees As DbSet(Of Employee)
    Public Property AllowanceTypes As DbSet(Of AllowanceType)
    Public Property EmployeeAllowances As DbSet(Of EmployeeAllowance)
    Public Property DeductionTypes As DbSet(Of DeductionType)
    Public Property EmployeeDeductions As DbSet(Of EmployeeDeduction)
    Public Property PayrollRecords As DbSet(Of PayrollRecord)
    Public Property PayrollDetails As DbSet(Of PayrollDetail)
    Public Property PayrollSettings As DbSet(Of PayrollSetting)

    Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
        ' إزالة اتفاقية تسمية الجداول بالجمع
        modelBuilder.Conventions.Remove(Of PluralizingTableNameConvention)()

        ' تكوين العلاقات والقيود
        ConfigureUserRelationships(modelBuilder)
        ConfigureOrganizationRelationships(modelBuilder)
        ConfigureAccountingRelationships(modelBuilder)
        ConfigurePayrollRelationships(modelBuilder)

        MyBase.OnModelCreating(modelBuilder)
    End Sub

    Private Sub ConfigureUserRelationships(modelBuilder As DbModelBuilder)
        ' تكوين علاقات المستخدمين
        modelBuilder.Entity(Of UserGroup)() _
            .HasRequired(Function(ug) ug.User) _
            .WithMany(Function(u) u.UserGroups) _
            .HasForeignKey(Function(ug) ug.UserId) _
            .WillCascadeOnDelete(True)

        modelBuilder.Entity(Of UserGroup)() _
            .HasRequired(Function(ug) ug.Group) _
            .WithMany(Function(g) g.UserGroups) _
            .HasForeignKey(Function(ug) ug.GroupId) _
            .WillCascadeOnDelete(True)

        modelBuilder.Entity(Of UserPermission)() _
            .HasRequired(Function(up) up.User) _
            .WithMany(Function(u) u.UserPermissions) _
            .HasForeignKey(Function(up) up.UserId) _
            .WillCascadeOnDelete(True)

        modelBuilder.Entity(Of GroupPermission)() _
            .HasRequired(Function(gp) gp.Group) _
            .WithMany(Function(g) g.GroupPermissions) _
            .HasForeignKey(Function(gp) gp.GroupId) _
            .WillCascadeOnDelete(True)
    End Sub

    Private Sub ConfigureOrganizationRelationships(modelBuilder As DbModelBuilder)
        ' تكوين علاقات الهيكل التنظيمي
        modelBuilder.Entity(Of Section)() _
            .HasRequired(Function(s) s.Department) _
            .WithMany(Function(d) d.Sections) _
            .HasForeignKey(Function(s) s.DepartmentId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of Division)() _
            .HasRequired(Function(d) d.Section) _
            .WithMany(Function(s) s.Divisions) _
            .HasForeignKey(Function(d) d.SectionId) _
            .WillCascadeOnDelete(False)
    End Sub

    Private Sub ConfigureAccountingRelationships(modelBuilder As DbModelBuilder)
        ' تكوين علاقات المحاسبة
        modelBuilder.Entity(Of ChartOfAccount)() _
            .HasOptional(Function(c) c.ParentAccount) _
            .WithMany(Function(c) c.ChildAccounts) _
            .HasForeignKey(Function(c) c.ParentAccountId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of BankBranch)() _
            .HasRequired(Function(bb) bb.Bank) _
            .WithMany(Function(b) b.BankBranches) _
            .HasForeignKey(Function(bb) bb.BankId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of BankAccount)() _
            .HasRequired(Function(ba) ba.BankBranch) _
            .WithMany(Function(bb) bb.BankAccounts) _
            .HasForeignKey(Function(ba) ba.BranchId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of JournalEntry)() _
            .HasRequired(Function(je) je.AccountingPeriod) _
            .WithMany(Function(ap) ap.JournalEntries) _
            .HasForeignKey(Function(je) je.PeriodId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of JournalEntryDetail)() _
            .HasRequired(Function(jed) jed.JournalEntry) _
            .WithMany(Function(je) je.JournalEntryDetails) _
            .HasForeignKey(Function(jed) jed.EntryId) _
            .WillCascadeOnDelete(True)
    End Sub

    Private Sub ConfigurePayrollRelationships(modelBuilder As DbModelBuilder)
        ' تكوين علاقات الرواتب
        modelBuilder.Entity(Of EmployeeAllowance)() _
            .HasRequired(Function(ea) ea.Employee) _
            .WithMany(Function(e) e.EmployeeAllowances) _
            .HasForeignKey(Function(ea) ea.EmployeeId) _
            .WillCascadeOnDelete(True)

        modelBuilder.Entity(Of EmployeeDeduction)() _
            .HasRequired(Function(ed) ed.Employee) _
            .WithMany(Function(e) e.EmployeeDeductions) _
            .HasForeignKey(Function(ed) ed.EmployeeId) _
            .WillCascadeOnDelete(True)

        modelBuilder.Entity(Of PayrollRecord)() _
            .HasRequired(Function(pr) pr.Employee) _
            .WithMany(Function(e) e.PayrollRecords) _
            .HasForeignKey(Function(pr) pr.EmployeeId) _
            .WillCascadeOnDelete(False)

        modelBuilder.Entity(Of PayrollDetail)() _
            .HasRequired(Function(pd) pd.PayrollRecord) _
            .WithMany(Function(pr) pr.PayrollDetails) _
            .HasForeignKey(Function(pd) pd.PayrollId) _
            .WillCascadeOnDelete(True)

        ' تكوين الفهارس الفريدة
        modelBuilder.Entity(Of User)() _
            .HasIndex(Function(u) u.Username) _
            .IsUnique()

        modelBuilder.Entity(Of Employee)() _
            .HasIndex(Function(e) e.EmployeeNumber) _
            .IsUnique()

        modelBuilder.Entity(Of ChartOfAccount)() _
            .HasIndex(Function(c) c.AccountCode) _
            .IsUnique()
    End Sub
End Class

''' <summary>
''' مُهيئ قاعدة البيانات
''' </summary>
Public Class PayrollDbInitializer
    Inherits CreateDatabaseIfNotExists(Of PayrollDbContext)

    Protected Overrides Sub Seed(context As PayrollDbContext)
        ' إضافة البيانات الأولية
        SeedUsers(context)
        SeedPermissions(context)
        SeedPayrollSettings(context)
        SeedDefaultAccounts(context)

        MyBase.Seed(context)
    End Sub

    Private Sub SeedUsers(context As PayrollDbContext)
        ' إضافة المستخدم الافتراضي
        Dim adminUser As New User() With {
            .Username = "admin",
            .FullName = "مدير النظام",
            .Email = "<EMAIL>",
            .PasswordHash = User.HashPassword("admin"),
            .IsActive = True,
            .CreatedBy = "System"
        }

        context.Users.Add(adminUser)
        context.SaveChanges()
    End Sub

    Private Sub SeedPermissions(context As PayrollDbContext)
        ' إضافة الصلاحيات الأساسية
        Dim permissions() As Permission = {
            New Permission() With {.PermissionName = "إدارة المستخدمين", .PermissionCode = "USERS_MANAGE", .ModuleName = "النظام"},
            New Permission() With {.PermissionName = "إدارة الموظفين", .PermissionCode = "EMPLOYEES_MANAGE", .ModuleName = "الرواتب"},
            New Permission() With {.PermissionName = "إدارة الحسابات", .PermissionCode = "ACCOUNTS_MANAGE", .ModuleName = "المحاسبة"},
            New Permission() With {.PermissionName = "احتساب الرواتب", .PermissionCode = "PAYROLL_CALCULATE", .ModuleName = "الرواتب"},
            New Permission() With {.PermissionName = "التقارير", .PermissionCode = "REPORTS_VIEW", .ModuleName = "التقارير"}
        }

        For Each permission In permissions
            context.Permissions.Add(permission)
        Next

        context.SaveChanges()
    End Sub

    Private Sub SeedPayrollSettings(context As PayrollDbContext)
        ' إضافة إعدادات الرواتب الافتراضية
        Dim settings() As PayrollSetting = {
            New PayrollSetting() With {.SettingName = "نسبة ضريبة الدخل", .SettingValue = "15", .Description = "نسبة ضريبة الدخل المئوية"},
            New PayrollSetting() With {.SettingName = "نسبة تقاعد الموظفين", .SettingValue = "10", .Description = "نسبة تقاعد الموظفين المئوية"},
            New PayrollSetting() With {.SettingName = "نسبة مساهمة الدائرة", .SettingValue = "15", .Description = "نسبة مساهمة الدائرة في التقاعد"}
        }

        For Each setting In settings
            context.PayrollSettings.Add(setting)
        Next

        context.SaveChanges()
    End Sub

    Private Sub SeedDefaultAccounts(context As PayrollDbContext)
        ' إضافة الحسابات الأساسية
        Dim accounts() As ChartOfAccount = {
            New ChartOfAccount() With {.AccountCode = "1000", .AccountName = "الأصول", .AccountType = "إجمالي", .AccountNature = "مدين"},
            New ChartOfAccount() With {.AccountCode = "2000", .AccountName = "الخصوم", .AccountType = "إجمالي", .AccountNature = "دائن"},
            New ChartOfAccount() With {.AccountCode = "3000", .AccountName = "حقوق الملكية", .AccountType = "إجمالي", .AccountNature = "دائن"},
            New ChartOfAccount() With {.AccountCode = "4000", .AccountName = "الإيرادات", .AccountType = "إجمالي", .AccountNature = "دائن"},
            New ChartOfAccount() With {.AccountCode = "5000", .AccountName = "المصروفات", .AccountType = "إجمالي", .AccountNature = "مدين"}
        }

        For Each account In accounts
            context.ChartOfAccounts.Add(account)
        Next

        context.SaveChanges()
    End Sub
End Class
