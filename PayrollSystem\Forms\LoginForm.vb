Imports System
Imports System.Windows.Forms
Imports System.Drawing
Imports MetroFramework.Forms
Imports MetroFramework.Controls
Imports MetroFramework

''' <summary>
''' نموذج تسجيل الدخول لنظام الرواتب
''' </summary>
Public Class LoginForm
    Inherits MetroForm

    Private lblTitle As MetroLabel
    Private lblUsername As MetroLabel
    Private lblPassword As MetroLabel
    Private txtUsername As MetroTextBox
    Private txtPassword As MetroTextBox
    Private btnLogin As MetroButton
    Private btnExit As MetroButton
    Private picLogo As PictureBox
    Private pnlMain As MetroPanel

    Public Sub New()
        InitializeComponent()
        SetupForm()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()

        ' إعداد النموذج الرئيسي
        Me.Text = "تسجيل الدخول - نظام الرواتب"
        Me.Size = New Size(500, 400)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Resizable = False
        Me.Style = MetroColorStyle.Blue
        Me.Theme = MetroThemeStyle.Light
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True

        ' اللوحة الرئيسية
        pnlMain = New MetroPanel()
        pnlMain.Size = New Size(450, 320)
        pnlMain.Location = New Point(25, 50)
        pnlMain.HorizontalScrollbarBarColor = True
        pnlMain.HorizontalScrollbarHighlightOnWheel = False
        pnlMain.HorizontalScrollbarSize = 10
        pnlMain.VerticalScrollbarBarColor = True
        pnlMain.VerticalScrollbarHighlightOnWheel = False
        pnlMain.VerticalScrollbarSize = 10

        ' شعار النظام
        picLogo = New PictureBox()
        picLogo.Size = New Size(80, 80)
        picLogo.Location = New Point(185, 20)
        picLogo.SizeMode = PictureBoxSizeMode.StretchImage
        picLogo.BackColor = Color.Transparent

        ' عنوان النظام
        lblTitle = New MetroLabel()
        lblTitle.Text = "نظام الرواتب - وزارة الشباب والرياضة"
        lblTitle.Font = New Font("Sakkal Majalla", 16.0F, FontStyle.Bold)
        lblTitle.Size = New Size(400, 30)
        lblTitle.Location = New Point(25, 110)
        lblTitle.TextAlign = ContentAlignment.MiddleCenter
        lblTitle.ForeColor = Color.FromArgb(0, 174, 219)

        ' تسمية اسم المستخدم
        lblUsername = New MetroLabel()
        lblUsername.Text = "اسم المستخدم:"
        lblUsername.Font = New Font("Sakkal Majalla", 12.0F, FontStyle.Bold)
        lblUsername.Size = New Size(100, 25)
        lblUsername.Location = New Point(320, 160)
        lblUsername.TextAlign = ContentAlignment.MiddleRight

        ' مربع نص اسم المستخدم
        txtUsername = New MetroTextBox()
        txtUsername.Size = New Size(250, 25)
        txtUsername.Location = New Point(50, 160)
        txtUsername.FontSize = MetroTextBoxSize.Medium
        txtUsername.FontWeight = MetroTextBoxWeight.Regular

        ' تسمية كلمة المرور
        lblPassword = New MetroLabel()
        lblPassword.Text = "كلمة المرور:"
        lblPassword.Font = New Font("Sakkal Majalla", 12.0F, FontStyle.Bold)
        lblPassword.Size = New Size(100, 25)
        lblPassword.Location = New Point(320, 200)
        lblPassword.TextAlign = ContentAlignment.MiddleRight

        ' مربع نص كلمة المرور
        txtPassword = New MetroTextBox()
        txtPassword.Size = New Size(250, 25)
        txtPassword.Location = New Point(50, 200)
        txtPassword.FontSize = MetroTextBoxSize.Medium
        txtPassword.FontWeight = MetroTextBoxWeight.Regular
        txtPassword.PasswordChar = "●"c
        txtPassword.UseSystemPasswordChar = True

        ' زر تسجيل الدخول
        btnLogin = New MetroButton()
        btnLogin.Text = "تسجيل الدخول"
        btnLogin.Size = New Size(120, 35)
        btnLogin.Location = New Point(200, 250)
        btnLogin.UseSelectable = True
        btnLogin.Style = MetroColorStyle.Blue

        ' زر الخروج
        btnExit = New MetroButton()
        btnExit.Text = "خروج"
        btnExit.Size = New Size(80, 35)
        btnExit.Location = New Point(100, 250)
        btnExit.UseSelectable = True
        btnExit.Style = MetroColorStyle.Red

        ' إضافة العناصر إلى اللوحة
        pnlMain.Controls.Add(picLogo)
        pnlMain.Controls.Add(lblTitle)
        pnlMain.Controls.Add(lblUsername)
        pnlMain.Controls.Add(txtUsername)
        pnlMain.Controls.Add(lblPassword)
        pnlMain.Controls.Add(txtPassword)
        pnlMain.Controls.Add(btnLogin)
        pnlMain.Controls.Add(btnExit)

        ' إضافة اللوحة إلى النموذج
        Me.Controls.Add(pnlMain)

        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupForm()
        ' ربط الأحداث
        AddHandler btnLogin.Click, AddressOf BtnLogin_Click
        AddHandler btnExit.Click, AddressOf BtnExit_Click
        AddHandler txtPassword.KeyPress, AddressOf TxtPassword_KeyPress
        AddHandler Me.Load, AddressOf LoginForm_Load
    End Sub

    Private Sub LoginForm_Load(sender As Object, e As EventArgs)
        ' تركيز على مربع اسم المستخدم
        txtUsername.Focus()
    End Sub

    Private Sub BtnLogin_Click(sender As Object, e As EventArgs)
        If ValidateLogin() Then
            ' إخفاء نموذج تسجيل الدخول وفتح النموذج الرئيسي
            Me.Hide()
            Dim mainForm As New MainForm()
            mainForm.ShowDialog()
            Me.Close()
        End If
    End Sub

    Private Sub BtnExit_Click(sender As Object, e As EventArgs)
        Application.Exit()
    End Sub

    Private Sub TxtPassword_KeyPress(sender As Object, e As KeyPressEventArgs)
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            BtnLogin_Click(sender, e)
        End If
    End Sub

    Private Function ValidateLogin() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        ' هنا يمكن إضافة التحقق من قاعدة البيانات
        ' مؤقتاً سنقبل أي اسم مستخدم وكلمة مرور
        If txtUsername.Text = "admin" AndAlso txtPassword.Text = "admin" Then
            Return True
        Else
            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtPassword.Clear()
            txtUsername.Focus()
            Return False
        End If
    End Function
End Class
