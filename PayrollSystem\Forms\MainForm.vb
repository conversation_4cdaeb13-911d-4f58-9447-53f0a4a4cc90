Imports System
Imports System.Windows.Forms
Imports System.Drawing
Imports MetroFramework.Forms
Imports MetroFramework.Controls
Imports MetroFramework

''' <summary>
''' النموذج الرئيسي لنظام الرواتب - وزارة الشباب والرياضة
''' </summary>
Public Class MainForm
    Inherits MetroForm

    Private menuStrip As MenuStrip
    Private statusStrip As StatusStrip
    Private lblStatus As ToolStripStatusLabel
    Private lblUser As ToolStripStatusLabel
    Private lblDate As ToolStripStatusLabel
    Private pnlMain As Panel
    Private timer As Timer

    ' القوائم الرئيسية
    Private mnuSystem As ToolStripMenuItem
    Private mnuAccounting As ToolStripMenuItem
    Private mnuPayroll As ToolStripMenuItem
    Private mnuReports As ToolStripMenuItem
    Private mnuHelp As ToolStripMenuItem

    Public Sub New()
        InitializeComponent()
        SetupMenus()
        SetupStatusBar()
        SetupTimer()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()

        ' إعداد النموذج الرئيسي
        Me.Text = "نظام الرواتب - وزارة الشباب والرياضة"
        Me.Size = New Size(1200, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.Style = MetroColorStyle.Blue
        Me.Theme = MetroThemeStyle.Light
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True

        ' شريط القوائم
        menuStrip = New MenuStrip()
        menuStrip.Font = New Font("Sakkal Majalla", 12.0F, FontStyle.Bold)
        menuStrip.RightToLeft = RightToLeft.Yes
        menuStrip.BackColor = Color.FromArgb(0, 174, 219)
        menuStrip.ForeColor = Color.White

        ' اللوحة الرئيسية
        pnlMain = New Panel()
        pnlMain.Dock = DockStyle.Fill
        pnlMain.BackColor = Color.WhiteSmoke

        ' شريط الحالة
        statusStrip = New StatusStrip()
        statusStrip.Font = New Font("Sakkal Majalla", 10.0F)
        statusStrip.RightToLeft = RightToLeft.Yes
        statusStrip.BackColor = Color.FromArgb(0, 174, 219)
        statusStrip.ForeColor = Color.White

        ' إضافة العناصر إلى النموذج
        Me.Controls.Add(pnlMain)
        Me.Controls.Add(menuStrip)
        Me.Controls.Add(statusStrip)
        Me.MainMenuStrip = menuStrip

        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub

    Private Sub SetupMenus()
        ' قائمة النظام
        mnuSystem = New ToolStripMenuItem("النظام")
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("إدارة المستخدمين", Nothing, AddressOf MnuUsers_Click))
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("الصلاحيات", Nothing, AddressOf MnuPermissions_Click))
        mnuSystem.DropDownItems.Add(New ToolStripSeparator())
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("إعدادات النظام", Nothing, AddressOf MnuSettings_Click))
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("النسخ الاحتياطي", Nothing, AddressOf MnuBackup_Click))
        mnuSystem.DropDownItems.Add(New ToolStripSeparator())
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("تسجيل الخروج", Nothing, AddressOf MnuLogout_Click))
        mnuSystem.DropDownItems.Add(New ToolStripMenuItem("إنهاء البرنامج", Nothing, AddressOf MnuExit_Click))

        ' قائمة المحاسبة
        mnuAccounting = New ToolStripMenuItem("المحاسبة")
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("شجرة الحسابات", Nothing, AddressOf MnuChartOfAccounts_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("الفترات المحاسبية", Nothing, AddressOf MnuAccountingPeriods_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripSeparator())
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("سندات القبض", Nothing, AddressOf MnuReceiptVouchers_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("سندات الصرف", Nothing, AddressOf MnuPaymentVouchers_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("القيود اليومية", Nothing, AddressOf MnuJournalEntries_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripSeparator())
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("المصارف", Nothing, AddressOf MnuBanks_Click))
        mnuAccounting.DropDownItems.Add(New ToolStripMenuItem("الحسابات البنكية", Nothing, AddressOf MnuBankAccounts_Click))

        ' قائمة الرواتب
        mnuPayroll = New ToolStripMenuItem("الرواتب")
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("بيانات الموظفين", Nothing, AddressOf MnuEmployees_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("الهيكل التنظيمي", Nothing, AddressOf MnuOrganization_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripSeparator())
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("المخصصات", Nothing, AddressOf MnuAllowances_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("الاستقطاعات", Nothing, AddressOf MnuDeductions_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripSeparator())
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("احتساب الرواتب", Nothing, AddressOf MnuCalculatePayroll_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("كشوفات الرواتب", Nothing, AddressOf MnuPayrollStatements_Click))
        mnuPayroll.DropDownItems.Add(New ToolStripMenuItem("استيراد من Excel", Nothing, AddressOf MnuImportExcel_Click))

        ' قائمة التقارير
        mnuReports = New ToolStripMenuItem("التقارير")
        mnuReports.DropDownItems.Add(New ToolStripMenuItem("التقارير المحاسبية", Nothing, AddressOf MnuAccountingReports_Click))
        mnuReports.DropDownItems.Add(New ToolStripMenuItem("تقارير الرواتب", Nothing, AddressOf MnuPayrollReports_Click))
        mnuReports.DropDownItems.Add(New ToolStripSeparator())
        mnuReports.DropDownItems.Add(New ToolStripMenuItem("ميزان المراجعة", Nothing, AddressOf MnuTrialBalance_Click))
        mnuReports.DropDownItems.Add(New ToolStripMenuItem("القوائم المالية", Nothing, AddressOf MnuFinancialStatements_Click))

        ' قائمة المساعدة
        mnuHelp = New ToolStripMenuItem("المساعدة")
        mnuHelp.DropDownItems.Add(New ToolStripMenuItem("دليل المستخدم", Nothing, AddressOf MnuUserGuide_Click))
        mnuHelp.DropDownItems.Add(New ToolStripMenuItem("حول البرنامج", Nothing, AddressOf MnuAbout_Click))

        ' إضافة القوائم إلى شريط القوائم
        menuStrip.Items.Add(mnuSystem)
        menuStrip.Items.Add(mnuAccounting)
        menuStrip.Items.Add(mnuPayroll)
        menuStrip.Items.Add(mnuReports)
        menuStrip.Items.Add(mnuHelp)
    End Sub

    Private Sub SetupStatusBar()
        lblStatus = New ToolStripStatusLabel("جاهز")
        lblStatus.Spring = True
        lblStatus.TextAlign = ContentAlignment.MiddleLeft

        lblUser = New ToolStripStatusLabel("المستخدم: admin")
        lblUser.BorderSides = ToolStripStatusLabelBorderSides.Left

        lblDate = New ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd - HH:mm"))
        lblDate.BorderSides = ToolStripStatusLabelBorderSides.Left

        statusStrip.Items.Add(lblStatus)
        statusStrip.Items.Add(lblUser)
        statusStrip.Items.Add(lblDate)
    End Sub

    Private Sub SetupTimer()
        timer = New Timer()
        timer.Interval = 1000 ' تحديث كل ثانية
        AddHandler timer.Tick, AddressOf Timer_Tick
        timer.Start()
    End Sub

    Private Sub Timer_Tick(sender As Object, e As EventArgs)
        lblDate.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss")
    End Sub

    ' معالجات الأحداث للقوائم
    Private Sub MnuUsers_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إدارة المستخدمين", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuPermissions_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إدارة الصلاحيات", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuSettings_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إعدادات النظام", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuBackup_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج النسخ الاحتياطي", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuLogout_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Me.Hide()
            Dim loginForm As New LoginForm()
            loginForm.ShowDialog()
            Me.Close()
        End If
    End Sub

    Private Sub MnuExit_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("هل تريد إنهاء البرنامج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Application.Exit()
        End If
    End Sub

    Private Sub MnuChartOfAccounts_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج شجرة الحسابات", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuAccountingPeriods_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج الفترات المحاسبية", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuReceiptVouchers_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج سندات القبض", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuPaymentVouchers_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج سندات الصرف", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuJournalEntries_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج القيود اليومية", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuBanks_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إدارة المصارف", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuBankAccounts_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج الحسابات البنكية", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuEmployees_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج بيانات الموظفين", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuOrganization_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج الهيكل التنظيمي", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuAllowances_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إدارة المخصصات", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuDeductions_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج إدارة الاستقطاعات", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuCalculatePayroll_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج احتساب الرواتب", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuPayrollStatements_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج كشوفات الرواتب", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuImportExcel_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح نموذج استيراد البيانات من Excel", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuAccountingReports_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح التقارير المحاسبية", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuPayrollReports_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح تقارير الرواتب", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuTrialBalance_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح تقرير ميزان المراجعة", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuFinancialStatements_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح القوائم المالية", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuUserGuide_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم فتح دليل المستخدم", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MnuAbout_Click(sender As Object, e As EventArgs)
        MessageBox.Show("نظام الرواتب - وزارة الشباب والرياضة" & vbCrLf & 
                       "الإصدار 1.0" & vbCrLf & 
                       "تم التطوير باستخدام VB.NET", 
                       "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If timer IsNot Nothing Then
            timer.Stop()
            timer.Dispose()
        End If
        MyBase.OnFormClosing(e)
    End Sub
End Class
