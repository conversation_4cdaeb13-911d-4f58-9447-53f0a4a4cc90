Imports System
Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج شجرة الحسابات
''' </summary>
<Table("ChartOfAccounts")>
Public Class ChartOfAccount
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property AccountId As Integer

    <Required>
    <StringLength(50)>
    <Display(Name:="رقم الحساب")>
    Public Property AccountCode As String

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الحساب")>
    Public Property AccountName As String

    <Display(Name:="الحساب الأب")>
    Public Property ParentAccountId As Integer?

    <Required>
    <StringLength(20)>
    <Display(Name:="نوع الحساب")>
    Public Property AccountType As String ' تحليلي / إجمالي

    <Required>
    <StringLength(20)>
    <Display(Name:="طبيعة الحساب")>
    Public Property AccountNature As String ' مدين / دائن

    <Display(Name:="الحساب الختامي")>
    Public Property FinalAccountId As Integer?

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="الرصيد الافتتاحي")>
    Public Property OpeningBalance As Decimal = 0

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    <ForeignKey("ParentAccountId")>
    Public Overridable Property ParentAccount As ChartOfAccount

    <ForeignKey("FinalAccountId")>
    Public Overridable Property FinalAccount As ChartOfAccount

    Public Overridable Property ChildAccounts As ICollection(Of ChartOfAccount)
    Public Overridable Property JournalEntryDetails As ICollection(Of JournalEntryDetail)

    Public Sub New()
        ChildAccounts = New HashSet(Of ChartOfAccount)()
        JournalEntryDetails = New HashSet(Of JournalEntryDetail)()
    End Sub
End Class

''' <summary>
''' نموذج الفترات المحاسبية
''' </summary>
<Table("AccountingPeriods")>
Public Class AccountingPeriod
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property PeriodId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم الفترة")>
    Public Property PeriodName As String

    <Required>
    <Display(Name:="تاريخ البداية")>
    Public Property StartDate As DateTime

    <Required>
    <Display(Name:="تاريخ النهاية")>
    Public Property EndDate As DateTime

    <Display(Name:="مغلقة")>
    Public Property IsClosed As Boolean = False

    <Display(Name:="تاريخ الإغلاق")>
    Public Property ClosedDate As DateTime?

    <StringLength(100)>
    <Display(Name:="المستخدم المغلق")>
    Public Property ClosedBy As String

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property JournalEntries As ICollection(Of JournalEntry)

    Public Sub New()
        JournalEntries = New HashSet(Of JournalEntry)()
    End Sub
End Class

''' <summary>
''' نموذج القيود اليومية
''' </summary>
<Table("JournalEntries")>
Public Class JournalEntry
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property EntryId As Integer

    <Required>
    <StringLength(50)>
    <Display(Name:="رقم القيد")>
    Public Property EntryNumber As String

    <Required>
    <Display(Name:="تاريخ القيد")>
    Public Property EntryDate As DateTime

    <Required>
    <StringLength(500)>
    <Display(Name:="البيان")>
    Public Property Description As String

    <StringLength(50)>
    <Display(Name:="نوع القيد")>
    Public Property EntryType As String

    <StringLength(100)>
    <Display(Name:="المرجع")>
    Public Property Reference As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="إجمالي المبلغ")>
    Public Property TotalAmount As Decimal

    <Display(Name:="معتمد")>
    Public Property IsApproved As Boolean = False

    <Display(Name:="تاريخ الاعتماد")>
    Public Property ApprovedDate As DateTime?

    <StringLength(100)>
    <Display(Name:="المعتمد من")>
    Public Property ApprovedBy As String

    <Display(Name:="الفترة المحاسبية")>
    Public Property PeriodId As Integer

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المنشئ")>
    Public Property CreatedBy As String

    ' خصائص التنقل
    <ForeignKey("PeriodId")>
    Public Overridable Property AccountingPeriod As AccountingPeriod

    Public Overridable Property JournalEntryDetails As ICollection(Of JournalEntryDetail)

    Public Sub New()
        JournalEntryDetails = New HashSet(Of JournalEntryDetail)()
    End Sub
End Class

''' <summary>
''' نموذج تفاصيل القيود اليومية
''' </summary>
<Table("JournalEntryDetails")>
Public Class JournalEntryDetail
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property DetailId As Integer

    <Required>
    <Display(Name:="القيد")>
    Public Property EntryId As Integer

    <Required>
    <Display(Name:="الحساب")>
    Public Property AccountId As Integer

    <Required>
    <StringLength(500)>
    <Display(Name:="البيان")>
    Public Property Description As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ المدين")>
    Public Property DebitAmount As Decimal = 0

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ الدائن")>
    Public Property CreditAmount As Decimal = 0

    ' خصائص التنقل
    <ForeignKey("EntryId")>
    Public Overridable Property JournalEntry As JournalEntry

    <ForeignKey("AccountId")>
    Public Overridable Property Account As ChartOfAccount
End Class

''' <summary>
''' نموذج المصارف
''' </summary>
<Table("Banks")>
Public Class Bank
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property BankId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم المصرف")>
    Public Property BankName As String

    <StringLength(100)>
    <Display(Name:="رمز المصرف")>
    Public Property BankCode As String

    <StringLength(500)>
    <Display(Name:="العنوان")>
    Public Property Address As String

    <StringLength(50)>
    <Display(Name:="الهاتف")>
    Public Property Phone As String

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property BankBranches As ICollection(Of BankBranch)

    Public Sub New()
        BankBranches = New HashSet(Of BankBranch)()
    End Sub
End Class

''' <summary>
''' نموذج فروع المصارف
''' </summary>
<Table("BankBranches")>
Public Class BankBranch
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property BranchId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الفرع")>
    Public Property BranchName As String

    <StringLength(100)>
    <Display(Name:="رقم الفرع")>
    Public Property BranchNumber As String

    <StringLength(500)>
    <Display(Name:="العنوان")>
    Public Property Address As String

    <StringLength(50)>
    <Display(Name:="الهاتف")>
    Public Property Phone As String

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Required>
    <Display(Name:="المصرف")>
    Public Property BankId As Integer

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    <ForeignKey("BankId")>
    Public Overridable Property Bank As Bank

    Public Overridable Property BankAccounts As ICollection(Of BankAccount)

    Public Sub New()
        BankAccounts = New HashSet(Of BankAccount)()
    End Sub
End Class

''' <summary>
''' نموذج الحسابات البنكية
''' </summary>
<Table("BankAccounts")>
Public Class BankAccount
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property AccountId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الحساب")>
    Public Property AccountName As String

    <Required>
    <StringLength(50)>
    <Display(Name:="رقم الحساب")>
    Public Property AccountNumber As String

    <StringLength(50)>
    <Display(Name:="رقم الـ IBAN")>
    Public Property IBANNumber As String

    <Required>
    <StringLength(50)>
    <Display(Name:="نوع الحساب")>
    Public Property AccountType As String ' تشغيلي / رواتب

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="الرصيد الحالي")>
    Public Property CurrentBalance As Decimal = 0

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Required>
    <Display(Name:="الفرع")>
    Public Property BranchId As Integer

    <Display(Name:="الحساب المحاسبي")>
    Public Property ChartAccountId As Integer?

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    <ForeignKey("BranchId")>
    Public Overridable Property BankBranch As BankBranch

    <ForeignKey("ChartAccountId")>
    Public Overridable Property ChartAccount As ChartOfAccount

    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class
