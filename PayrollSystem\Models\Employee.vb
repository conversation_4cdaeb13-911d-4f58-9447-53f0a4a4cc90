Imports System
Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج بيانات الموظف
''' </summary>
<Table("Employees")>
Public Class Employee
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property EmployeeId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="الرقم الوظيفي")>
    Public Property EmployeeNumber As String

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الموظف")>
    Public Property FullName As String

    <StringLength(100)>
    <Display(Name:="اسم الأم")>
    Public Property MotherName As String

    <StringLength(50)>
    <Display(Name:="رقم الـ IBAN")>
    Public Property IBANNumber As String

    <Required>
    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="الراتب الأساسي")>
    Public Property BasicSalary As Decimal

    <Display(Name:="تاريخ التعيين")>
    Public Property HireDate As DateTime?

    <Display(Name:="تاريخ انتهاء الخدمة")>
    Public Property EndOfServiceDate As DateTime?

    <Display(Name:="حالة الموظف")>
    Public Property IsActive As Boolean = True

    ' المفاتيح الخارجية
    <Display(Name:="الدائرة")>
    Public Property DepartmentId As Integer?

    <Display(Name:="القسم")>
    Public Property SectionId As Integer?

    <Display(Name:="الشعبة")>
    Public Property DivisionId As Integer?

    <Display(Name:="العنوان الوظيفي")>
    Public Property JobTitleId As Integer?

    <Display(Name:="الدرجة الوظيفية")>
    Public Property JobGradeId As Integer?

    <Display(Name:="المرحلة")>
    Public Property StageId As Integer?

    <Display(Name:="الشهادة العلمية")>
    Public Property EducationId As Integer?

    <Display(Name:="المنصب")>
    Public Property PositionId As Integer?

    <Display(Name:="الحساب البنكي")>
    Public Property BankAccountId As Integer?

    ' خصائص التنقل
    <ForeignKey("DepartmentId")>
    Public Overridable Property Department As Department

    <ForeignKey("SectionId")>
    Public Overridable Property Section As Section

    <ForeignKey("DivisionId")>
    Public Overridable Property Division As Division

    <ForeignKey("JobTitleId")>
    Public Overridable Property JobTitle As JobTitle

    <ForeignKey("JobGradeId")>
    Public Overridable Property JobGrade As JobGrade

    <ForeignKey("StageId")>
    Public Overridable Property Stage As Stage

    <ForeignKey("EducationId")>
    Public Overridable Property Education As Education

    <ForeignKey("PositionId")>
    Public Overridable Property Position As Position

    <ForeignKey("BankAccountId")>
    Public Overridable Property BankAccount As BankAccount

    ' مجموعات المخصصات والاستقطاعات
    Public Overridable Property EmployeeAllowances As ICollection(Of EmployeeAllowance)
    Public Overridable Property EmployeeDeductions As ICollection(Of EmployeeDeduction)
    Public Overridable Property PayrollRecords As ICollection(Of PayrollRecord)

    ' خصائص محسوبة
    <NotMapped>
    <Display(Name:="إجمالي المخصصات")>
    Public ReadOnly Property TotalAllowances As Decimal
        Get
            If EmployeeAllowances IsNot Nothing Then
                Return EmployeeAllowances.Sum(Function(a) a.Amount)
            End If
            Return 0
        End Get
    End Property

    <NotMapped>
    <Display(Name:="إجمالي الاستقطاعات")>
    Public ReadOnly Property TotalDeductions As Decimal
        Get
            If EmployeeDeductions IsNot Nothing Then
                Return EmployeeDeductions.Sum(Function(d) d.Amount)
            End If
            Return 0
        End Get
    End Property

    <NotMapped>
    <Display(Name:="إجمالي الراتب")>
    Public ReadOnly Property GrossSalary As Decimal
        Get
            Return BasicSalary + TotalAllowances
        End Get
    End Property

    <NotMapped>
    <Display(Name:="صافي الراتب")>
    Public ReadOnly Property NetSalary As Decimal
        Get
            Return GrossSalary - TotalDeductions
        End Get
    End Property

    ' معلومات التدقيق
    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    <Display(Name:="تاريخ التحديث")>
    Public Property ModifiedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المنشئ")>
    Public Property CreatedBy As String

    <StringLength(100)>
    <Display(Name:="المستخدم المحدث")>
    Public Property ModifiedBy As String

    Public Sub New()
        EmployeeAllowances = New HashSet(Of EmployeeAllowance)()
        EmployeeDeductions = New HashSet(Of EmployeeDeduction)()
        PayrollRecords = New HashSet(Of PayrollRecord)()
    End Sub
End Class
