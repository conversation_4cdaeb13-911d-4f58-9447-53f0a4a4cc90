Imports System
Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج الدوائر
''' </summary>
<Table("Departments")>
Public Class Department
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property DepartmentId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الدائرة")>
    Public Property DepartmentName As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Sections As ICollection(Of Section)
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Sections = New HashSet(Of Section)()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج الأقسام
''' </summary>
<Table("Sections")>
Public Class Section
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property SectionId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم القسم")>
    Public Property SectionName As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' المفتاح الخارجي
    <Required>
    <Display(Name:="الدائرة")>
    Public Property DepartmentId As Integer

    ' خصائص التنقل
    <ForeignKey("DepartmentId")>
    Public Overridable Property Department As Department

    Public Overridable Property Divisions As ICollection(Of Division)
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Divisions = New HashSet(Of Division)()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج الشعب
''' </summary>
<Table("Divisions")>
Public Class Division
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property DivisionId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الشعبة")>
    Public Property DivisionName As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' المفتاح الخارجي
    <Required>
    <Display(Name:="القسم")>
    Public Property SectionId As Integer

    ' خصائص التنقل
    <ForeignKey("SectionId")>
    Public Overridable Property Section As Section

    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج العناوين الوظيفية
''' </summary>
<Table("JobTitles")>
Public Class JobTitle
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property JobTitleId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="العنوان الوظيفي")>
    Public Property TitleName As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج الدرجات الوظيفية
''' </summary>
<Table("JobGrades")>
Public Class JobGrade
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property JobGradeId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم الدرجة")>
    Public Property GradeName As String

    <Required>
    <Display(Name:="رقم الدرجة")>
    Public Property GradeNumber As Integer

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="الراتب الأساسي")>
    Public Property BasicSalary As Decimal?

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج المراحل
''' </summary>
<Table("Stages")>
Public Class Stage
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property StageId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم المرحلة")>
    Public Property StageName As String

    <Required>
    <Display(Name:="رقم المرحلة")>
    Public Property StageNumber As Integer

    <Column(TypeName:="decimal(5,2)")>
    <Display(Name:="نسبة الزيادة")>
    Public Property IncreasePercentage As Decimal?

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج الشهادات العلمية
''' </summary>
<Table("Educations")>
Public Class Education
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property EducationId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الشهادة")>
    Public Property EducationName As String

    <StringLength(100)>
    <Display(Name:="المستوى")>
    Public Property Level As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="مخصص الشهادة")>
    Public Property AllowanceAmount As Decimal?

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class

''' <summary>
''' نموذج المناصب
''' </summary>
<Table("Positions")>
Public Class Position
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property PositionId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم المنصب")>
    Public Property PositionName As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="مخصص المنصب")>
    Public Property AllowanceAmount As Decimal?

    <Column(TypeName:="decimal(5,2)")>
    <Display(Name:="نسبة المخصص")>
    Public Property AllowancePercentage As Decimal?

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property Employees As ICollection(Of Employee)

    Public Sub New()
        Employees = New HashSet(Of Employee)()
    End Sub
End Class
