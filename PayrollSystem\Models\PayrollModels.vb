Imports System
Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج أنواع المخصصات
''' </summary>
<Table("AllowanceTypes")>
Public Class AllowanceType
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property AllowanceTypeId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم المخصص")>
    Public Property AllowanceName As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ الافتراضي")>
    Public Property DefaultAmount As Decimal?

    <Column(TypeName:="decimal(5,2)")>
    <Display(Name:="النسبة الافتراضية")>
    Public Property DefaultPercentage As Decimal?

    <Display(Name:="نوع الحساب")>
    Public Property CalculationType As String ' ثابت / نسبة

    <Display(Name:="خاضع للضريبة")>
    Public Property IsTaxable As Boolean = True

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property EmployeeAllowances As ICollection(Of EmployeeAllowance)

    Public Sub New()
        EmployeeAllowances = New HashSet(Of EmployeeAllowance)()
    End Sub
End Class

''' <summary>
''' نموذج مخصصات الموظفين
''' </summary>
<Table("EmployeeAllowances")>
Public Class EmployeeAllowance
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer

    <Required>
    <Display(Name:="الموظف")>
    Public Property EmployeeId As Integer

    <Required>
    <Display(Name:="نوع المخصص")>
    Public Property AllowanceTypeId As Integer

    <Required>
    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ")>
    Public Property Amount As Decimal

    <Display(Name:="تاريخ البداية")>
    Public Property StartDate As DateTime

    <Display(Name:="تاريخ النهاية")>
    Public Property EndDate As DateTime?

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    <ForeignKey("EmployeeId")>
    Public Overridable Property Employee As Employee

    <ForeignKey("AllowanceTypeId")>
    Public Overridable Property AllowanceType As AllowanceType
End Class

''' <summary>
''' نموذج أنواع الاستقطاعات
''' </summary>
<Table("DeductionTypes")>
Public Class DeductionType
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property DeductionTypeId As Integer

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم الاستقطاع")>
    Public Property DeductionName As String

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ الافتراضي")>
    Public Property DefaultAmount As Decimal?

    <Column(TypeName:="decimal(5,2)")>
    <Display(Name:="النسبة الافتراضية")>
    Public Property DefaultPercentage As Decimal?

    <Display(Name:="نوع الحساب")>
    Public Property CalculationType As String ' ثابت / نسبة

    <Display(Name:="إجباري")>
    Public Property IsMandatory As Boolean = False

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property EmployeeDeductions As ICollection(Of EmployeeDeduction)

    Public Sub New()
        EmployeeDeductions = New HashSet(Of EmployeeDeduction)()
    End Sub
End Class

''' <summary>
''' نموذج استقطاعات الموظفين
''' </summary>
<Table("EmployeeDeductions")>
Public Class EmployeeDeduction
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer

    <Required>
    <Display(Name:="الموظف")>
    Public Property EmployeeId As Integer

    <Required>
    <Display(Name:="نوع الاستقطاع")>
    Public Property DeductionTypeId As Integer

    <Required>
    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ")>
    Public Property Amount As Decimal

    <Display(Name:="تاريخ البداية")>
    Public Property StartDate As DateTime

    <Display(Name:="تاريخ النهاية")>
    Public Property EndDate As DateTime?

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    <ForeignKey("EmployeeId")>
    Public Overridable Property Employee As Employee

    <ForeignKey("DeductionTypeId")>
    Public Overridable Property DeductionType As DeductionType
End Class

''' <summary>
''' نموذج سجلات الرواتب الشهرية
''' </summary>
<Table("PayrollRecords")>
Public Class PayrollRecord
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property PayrollId As Integer

    <Required>
    <Display(Name:="الموظف")>
    Public Property EmployeeId As Integer

    <Required>
    <Display(Name:="السنة")>
    Public Property PayrollYear As Integer

    <Required>
    <Display(Name:="الشهر")>
    Public Property PayrollMonth As Integer

    <Required>
    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="الراتب الأساسي")>
    Public Property BasicSalary As Decimal

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="إجمالي المخصصات")>
    Public Property TotalAllowances As Decimal = 0

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="إجمالي الاستقطاعات")>
    Public Property TotalDeductions As Decimal = 0

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="إجمالي الراتب")>
    Public Property GrossSalary As Decimal

    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="صافي الراتب")>
    Public Property NetSalary As Decimal

    <Display(Name:="تاريخ الاحتساب")>
    Public Property CalculatedDate As DateTime

    <Display(Name:="معتمد")>
    Public Property IsApproved As Boolean = False

    <Display(Name:="تاريخ الاعتماد")>
    Public Property ApprovedDate As DateTime?

    <StringLength(100)>
    <Display(Name:="المعتمد من")>
    Public Property ApprovedBy As String

    <Display(Name:="مدفوع")>
    Public Property IsPaid As Boolean = False

    <Display(Name:="تاريخ الدفع")>
    Public Property PaidDate As DateTime?

    <StringLength(500)>
    <Display(Name:="ملاحظات")>
    Public Property Notes As String

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المنشئ")>
    Public Property CreatedBy As String

    ' خصائص التنقل
    <ForeignKey("EmployeeId")>
    Public Overridable Property Employee As Employee

    Public Overridable Property PayrollDetails As ICollection(Of PayrollDetail)

    Public Sub New()
        PayrollDetails = New HashSet(Of PayrollDetail)()
    End Sub
End Class

''' <summary>
''' نموذج تفاصيل الرواتب
''' </summary>
<Table("PayrollDetails")>
Public Class PayrollDetail
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property DetailId As Integer

    <Required>
    <Display(Name:="سجل الراتب")>
    Public Property PayrollId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="نوع العنصر")>
    Public Property ItemType As String ' مخصص / استقطاع

    <Required>
    <StringLength(200)>
    <Display(Name:="اسم العنصر")>
    Public Property ItemName As String

    <Required>
    <Column(TypeName:="decimal(18,2)")>
    <Display(Name:="المبلغ")>
    Public Property Amount As Decimal

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    ' خصائص التنقل
    <ForeignKey("PayrollId")>
    Public Overridable Property PayrollRecord As PayrollRecord
End Class

''' <summary>
''' نموذج إعدادات الرواتب
''' </summary>
<Table("PayrollSettings")>
Public Class PayrollSetting
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property SettingId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم الإعداد")>
    Public Property SettingName As String

    <Required>
    <StringLength(500)>
    <Display(Name:="قيمة الإعداد")>
    Public Property SettingValue As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="تاريخ التحديث")>
    Public Property ModifiedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المحدث")>
    Public Property ModifiedBy As String
End Class
