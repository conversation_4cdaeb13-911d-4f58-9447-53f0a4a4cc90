Imports System
Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema
Imports System.Security.Cryptography
Imports System.Text

''' <summary>
''' نموذج المستخدمين
''' </summary>
<Table("Users")>
Public Class User
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property UserId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم المستخدم")>
    Public Property Username As String

    <Required>
    <StringLength(200)>
    <Display(Name:="الاسم الكامل")>
    Public Property FullName As String

    <StringLength(200)>
    <Display(Name:="البريد الإلكتروني")>
    Public Property Email As String

    <Required>
    <StringLength(500)>
    <Display(Name:="كلمة المرور المشفرة")>
    Public Property PasswordHash As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="آخر تسجيل دخول")>
    Public Property LastLoginDate As DateTime?

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    <Display(Name:="تاريخ التحديث")>
    Public Property ModifiedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المنشئ")>
    Public Property CreatedBy As String

    <StringLength(100)>
    <Display(Name:="المستخدم المحدث")>
    Public Property ModifiedBy As String

    ' خصائص التنقل
    Public Overridable Property UserGroups As ICollection(Of UserGroup)
    Public Overridable Property UserPermissions As ICollection(Of UserPermission)

    Public Sub New()
        UserGroups = New HashSet(Of UserGroup)()
        UserPermissions = New HashSet(Of UserPermission)()
    End Sub

    ''' <summary>
    ''' تشفير كلمة المرور
    ''' </summary>
    Public Shared Function HashPassword(password As String) As String
        Using sha256Hash As SHA256 = SHA256.Create()
            Dim bytes As Byte() = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password))
            Dim builder As New StringBuilder()
            For i As Integer = 0 To bytes.Length - 1
                builder.Append(bytes(i).ToString("x2"))
            Next
            Return builder.ToString()
        End Using
    End Function

    ''' <summary>
    ''' التحقق من كلمة المرور
    ''' </summary>
    Public Function VerifyPassword(password As String) As Boolean
        Dim hashOfInput As String = HashPassword(password)
        Return String.Equals(PasswordHash, hashOfInput, StringComparison.OrdinalIgnoreCase)
    End Function
End Class

''' <summary>
''' نموذج مجموعات المستخدمين
''' </summary>
<Table("Groups")>
Public Class Group
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property GroupId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم المجموعة")>
    Public Property GroupName As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property UserGroups As ICollection(Of UserGroup)
    Public Overridable Property GroupPermissions As ICollection(Of GroupPermission)

    Public Sub New()
        UserGroups = New HashSet(Of UserGroup)()
        GroupPermissions = New HashSet(Of GroupPermission)()
    End Sub
End Class

''' <summary>
''' نموذج ربط المستخدمين بالمجموعات
''' </summary>
<Table("UserGroups")>
Public Class UserGroup
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer

    <Required>
    <Display(Name:="المستخدم")>
    Public Property UserId As Integer

    <Required>
    <Display(Name:="المجموعة")>
    Public Property GroupId As Integer

    <Display(Name:="تاريخ الإضافة")>
    Public Property AssignedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المضيف")>
    Public Property AssignedBy As String

    ' خصائص التنقل
    <ForeignKey("UserId")>
    Public Overridable Property User As User

    <ForeignKey("GroupId")>
    Public Overridable Property Group As Group
End Class

''' <summary>
''' نموذج الصلاحيات
''' </summary>
<Table("Permissions")>
Public Class Permission
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property PermissionId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم الصلاحية")>
    Public Property PermissionName As String

    <Required>
    <StringLength(100)>
    <Display(Name:="رمز الصلاحية")>
    Public Property PermissionCode As String

    <StringLength(500)>
    <Display(Name:="الوصف")>
    Public Property Description As String

    <StringLength(100)>
    <Display(Name:="الوحدة")>
    Public Property ModuleName As String

    <Display(Name:="حالة النشاط")>
    Public Property IsActive As Boolean = True

    <Display(Name:="تاريخ الإنشاء")>
    Public Property CreatedDate As DateTime = DateTime.Now

    ' خصائص التنقل
    Public Overridable Property UserPermissions As ICollection(Of UserPermission)
    Public Overridable Property GroupPermissions As ICollection(Of GroupPermission)

    Public Sub New()
        UserPermissions = New HashSet(Of UserPermission)()
        GroupPermissions = New HashSet(Of GroupPermission)()
    End Sub
End Class

''' <summary>
''' نموذج صلاحيات المستخدمين
''' </summary>
<Table("UserPermissions")>
Public Class UserPermission
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer

    <Required>
    <Display(Name:="المستخدم")>
    Public Property UserId As Integer

    <Required>
    <Display(Name:="الصلاحية")>
    Public Property PermissionId As Integer

    <Display(Name:="إضافة")>
    Public Property CanAdd As Boolean = False

    <Display(Name:="تعديل")>
    Public Property CanEdit As Boolean = False

    <Display(Name:="حذف")>
    Public Property CanDelete As Boolean = False

    <Display(Name:="عرض")>
    Public Property CanView As Boolean = True

    <Display(Name:="تاريخ الإضافة")>
    Public Property AssignedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المضيف")>
    Public Property AssignedBy As String

    ' خصائص التنقل
    <ForeignKey("UserId")>
    Public Overridable Property User As User

    <ForeignKey("PermissionId")>
    Public Overridable Property Permission As Permission
End Class

''' <summary>
''' نموذج صلاحيات المجموعات
''' </summary>
<Table("GroupPermissions")>
Public Class GroupPermission
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer

    <Required>
    <Display(Name:="المجموعة")>
    Public Property GroupId As Integer

    <Required>
    <Display(Name:="الصلاحية")>
    Public Property PermissionId As Integer

    <Display(Name:="إضافة")>
    Public Property CanAdd As Boolean = False

    <Display(Name:="تعديل")>
    Public Property CanEdit As Boolean = False

    <Display(Name:="حذف")>
    Public Property CanDelete As Boolean = False

    <Display(Name:="عرض")>
    Public Property CanView As Boolean = True

    <Display(Name:="تاريخ الإضافة")>
    Public Property AssignedDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="المستخدم المضيف")>
    Public Property AssignedBy As String

    ' خصائص التنقل
    <ForeignKey("GroupId")>
    Public Overridable Property Group As Group

    <ForeignKey("PermissionId")>
    Public Overridable Property Permission As Permission
End Class

''' <summary>
''' نموذج سجل العمليات
''' </summary>
<Table("AuditLogs")>
Public Class AuditLog
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property LogId As Integer

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم المستخدم")>
    Public Property Username As String

    <Required>
    <StringLength(100)>
    <Display(Name:="نوع العملية")>
    Public Property ActionType As String ' إضافة / تعديل / حذف / عرض

    <Required>
    <StringLength(100)>
    <Display(Name:="اسم الجدول")>
    Public Property TableName As String

    <StringLength(100)>
    <Display(Name:="معرف السجل")>
    Public Property RecordId As String

    <StringLength(1000)>
    <Display(Name:="القيم القديمة")>
    Public Property OldValues As String

    <StringLength(1000)>
    <Display(Name:="القيم الجديدة")>
    Public Property NewValues As String

    <Required>
    <Display(Name:="تاريخ العملية")>
    Public Property ActionDate As DateTime = DateTime.Now

    <StringLength(100)>
    <Display(Name:="عنوان IP")>
    Public Property IPAddress As String

    <StringLength(500)>
    <Display(Name:="تفاصيل إضافية")>
    Public Property AdditionalInfo As String
End Class
