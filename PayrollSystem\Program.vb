Imports System
Imports System.Windows.Forms
Imports System.Globalization
Imports System.Threading

''' <summary>
''' نقطة البداية الرئيسية لبرنامج نظام الرواتب - وزارة الشباب والرياضة
''' </summary>
Module Program
    ''' <summary>
    ''' نقطة الدخول الرئيسية للتطبيق
    ''' </summary>
    <STAThread>
    Sub Main()
        ' تعيين الثقافة العربية للتطبيق
        Thread.CurrentThread.CurrentCulture = New CultureInfo("ar-IQ")
        Thread.CurrentThread.CurrentUICulture = New CultureInfo("ar-IQ")
        
        ' تمكين الأنماط البصرية
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        
        ' تعيين اتجاه النص من اليمين إلى اليسار
        Application.SetDefaultFont(New Drawing.Font("Sakkal Majalla", 14.25F, Drawing.FontStyle.Bold))
        
        Try
            ' بدء تشغيل نموذج تسجيل الدخول
            Application.Run(New LoginForm())
        Catch ex As Exception
            MessageBox.Show($"خطأ في بدء تشغيل التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Module
