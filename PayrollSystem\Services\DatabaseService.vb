Imports System
Imports System.Data.SqlClient
Imports System.IO
Imports System.Configuration
Imports System.Windows.Forms

''' <summary>
''' خدمة إدارة قاعدة البيانات
''' </summary>
Public Class DatabaseService
    Private ReadOnly connectionString As String

    Public Sub New()
        connectionString = ConfigurationManager.ConnectionStrings("PayrollConnectionString").ConnectionString
    End Sub

    ''' <summary>
    ''' اختبار الاتصال بقاعدة البيانات
    ''' </summary>
    Public Function TestConnection() As Boolean
        Try
            Using connection As New SqlConnection(connectionString)
                connection.Open()
                Return True
            End Using
        Catch ex As Exception
            MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إنشاء نسخة احتياطية من قاعدة البيانات
    ''' </summary>
    Public Function CreateBackup(backupPath As String) As Boolean
        Try
            Dim backupFileName As String = $"PayrollBackup_{DateTime.Now:yyyyMMdd_HHmmss}.bak"
            Dim fullBackupPath As String = Path.Combine(backupPath, backupFileName)

            ' التأكد من وجود مجلد النسخ الاحتياطي
            If Not Directory.Exists(backupPath) Then
                Directory.CreateDirectory(backupPath)
            End If

            Dim backupQuery As String = $"BACKUP DATABASE [PayrollSystem] TO DISK = '{fullBackupPath}'"

            Using connection As New SqlConnection(connectionString)
                connection.Open()
                Using command As New SqlCommand(backupQuery, connection)
                    command.CommandTimeout = 300 ' 5 دقائق
                    command.ExecuteNonQuery()
                End Using
            End Using

            MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح في: {fullBackupPath}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' استرجاع نسخة احتياطية
    ''' </summary>
    Public Function RestoreBackup(backupFilePath As String) As Boolean
        Try
            If Not File.Exists(backupFilePath) Then
                MessageBox.Show("ملف النسخة الاحتياطية غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return False
            End If

            Dim result As DialogResult = MessageBox.Show(
                "تحذير: سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية. هل تريد المتابعة؟",
                "تأكيد الاسترجاع",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning)

            If result = DialogResult.No Then
                Return False
            End If

            ' إغلاق جميع الاتصالات النشطة
            Dim killConnectionsQuery As String = "ALTER DATABASE [PayrollSystem] SET SINGLE_USER WITH ROLLBACK IMMEDIATE"
            Dim restoreQuery As String = $"RESTORE DATABASE [PayrollSystem] FROM DISK = '{backupFilePath}' WITH REPLACE"
            Dim multiUserQuery As String = "ALTER DATABASE [PayrollSystem] SET MULTI_USER"

            Using connection As New SqlConnection(connectionString)
                connection.Open()

                ' تعيين قاعدة البيانات للمستخدم الواحد
                Using command As New SqlCommand(killConnectionsQuery, connection)
                    command.ExecuteNonQuery()
                End Using

                ' استرجاع النسخة الاحتياطية
                Using command As New SqlCommand(restoreQuery, connection)
                    command.CommandTimeout = 600 ' 10 دقائق
                    command.ExecuteNonQuery()
                End Using

                ' إعادة تعيين قاعدة البيانات لمتعدد المستخدمين
                Using command As New SqlCommand(multiUserQuery, connection)
                    command.ExecuteNonQuery()
                End Using
            End Using

            MessageBox.Show("تم استرجاع النسخة الاحتياطية بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في استرجاع النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' تنظيف قاعدة البيانات وإعادة فهرستها
    ''' </summary>
    Public Function OptimizeDatabase() As Boolean
        Try
            Dim optimizeQueries() As String = {
                "UPDATE STATISTICS",
                "DBCC UPDATEUSAGE(0)",
                "DBCC CHECKDB WITH NO_INFOMSGS",
                "DBCC SHRINKDATABASE([PayrollSystem])"
            }

            Using connection As New SqlConnection(connectionString)
                connection.Open()
                For Each query In optimizeQueries
                    Using command As New SqlCommand(query, connection)
                        command.CommandTimeout = 300
                        command.ExecuteNonQuery()
                    End Using
                Next
            End Using

            MessageBox.Show("تم تحسين قاعدة البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحسين قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' الحصول على معلومات قاعدة البيانات
    ''' </summary>
    Public Function GetDatabaseInfo() As Dictionary(Of String, String)
        Dim info As New Dictionary(Of String, String)

        Try
            Using connection As New SqlConnection(connectionString)
                connection.Open()

                ' حجم قاعدة البيانات
                Dim sizeQuery As String = "SELECT SUM(size * 8.0 / 1024) AS SizeMB FROM sys.database_files"
                Using command As New SqlCommand(sizeQuery, connection)
                    Dim size As Object = command.ExecuteScalar()
                    info("DatabaseSize") = If(size IsNot Nothing, $"{Convert.ToDouble(size):F2} MB", "غير معروف")
                End Using

                ' عدد الجداول
                Dim tablesQuery As String = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
                Using command As New SqlCommand(tablesQuery, connection)
                    info("TablesCount") = command.ExecuteScalar().ToString()
                End Using

                ' إصدار SQL Server
                Dim versionQuery As String = "SELECT @@VERSION"
                Using command As New SqlCommand(versionQuery, connection)
                    info("ServerVersion") = command.ExecuteScalar().ToString()
                End Using

                ' تاريخ آخر نسخة احتياطية
                Dim backupQuery As String = "SELECT TOP 1 backup_finish_date FROM msdb.dbo.backupset WHERE database_name = 'PayrollSystem' ORDER BY backup_finish_date DESC"
                Using command As New SqlCommand(backupQuery, connection)
                    Dim lastBackup As Object = command.ExecuteScalar()
                    info("LastBackup") = If(lastBackup IsNot Nothing, Convert.ToDateTime(lastBackup).ToString("yyyy/MM/dd HH:mm"), "لا توجد نسخ احتياطية")
                End Using
            End Using

        Catch ex As Exception
            info("Error") = ex.Message
        End Try

        Return info
    End Function

    ''' <summary>
    ''' تشغيل النسخ الاحتياطي التلقائي
    ''' </summary>
    Public Sub StartAutoBackup()
        Try
            Dim autoBackupEnabled As Boolean = Boolean.Parse(ConfigurationManager.AppSettings("AutoBackupEnabled"))
            If Not autoBackupEnabled Then Return

            Dim backupPath As String = ConfigurationManager.AppSettings("BackupPath")
            Dim intervalHours As Integer = Integer.Parse(ConfigurationManager.AppSettings("AutoBackupInterval"))

            ' إنشاء مؤقت للنسخ الاحتياطي التلقائي
            Dim backupTimer As New Timer()
            backupTimer.Interval = intervalHours * 60 * 60 * 1000 ' تحويل إلى ميلي ثانية
            AddHandler backupTimer.Tick, Sub() CreateBackup(backupPath)
            backupTimer.Start()

        Catch ex As Exception
            ' تسجيل الخطأ دون إظهار رسالة للمستخدم
            Console.WriteLine($"خطأ في بدء النسخ الاحتياطي التلقائي: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تصدير البيانات إلى ملف SQL
    ''' </summary>
    Public Function ExportToSQL(exportPath As String) As Boolean
        Try
            Dim exportFileName As String = $"PayrollExport_{DateTime.Now:yyyyMMdd_HHmmss}.sql"
            Dim fullExportPath As String = Path.Combine(exportPath, exportFileName)

            ' التأكد من وجود مجلد التصدير
            If Not Directory.Exists(exportPath) Then
                Directory.CreateDirectory(exportPath)
            End If

            Using connection As New SqlConnection(connectionString)
                connection.Open()

                ' الحصول على قائمة الجداول
                Dim tablesQuery As String = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
                Dim tables As New List(Of String)

                Using command As New SqlCommand(tablesQuery, connection)
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            tables.Add(reader("TABLE_NAME").ToString())
                        End While
                    End Using
                End Using

                ' تصدير البيانات
                Using writer As New StreamWriter(fullExportPath, False, System.Text.Encoding.UTF8)
                    writer.WriteLine("-- تصدير بيانات نظام الرواتب")
                    writer.WriteLine($"-- تاريخ التصدير: {DateTime.Now}")
                    writer.WriteLine()

                    For Each tableName In tables
                        writer.WriteLine($"-- بيانات جدول {tableName}")
                        
                        Dim selectQuery As String = $"SELECT * FROM [{tableName}]"
                        Using command As New SqlCommand(selectQuery, connection)
                            Using reader As SqlDataReader = command.ExecuteReader()
                                While reader.Read()
                                    Dim values As New List(Of String)
                                    For i As Integer = 0 To reader.FieldCount - 1
                                        Dim value As String = If(reader.IsDBNull(i), "NULL", $"'{reader(i).ToString().Replace("'", "''")}'")
                                        values.Add(value)
                                    Next
                                    writer.WriteLine($"INSERT INTO [{tableName}] VALUES ({String.Join(", ", values)});")
                                End While
                            End Using
                        End Using
                        writer.WriteLine()
                    Next
                End Using
            End Using

            MessageBox.Show($"تم تصدير البيانات بنجاح إلى: {fullExportPath}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function
End Class
