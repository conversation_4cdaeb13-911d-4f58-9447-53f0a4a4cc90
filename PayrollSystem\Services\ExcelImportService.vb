Imports System
Imports System.IO
Imports System.Data
Imports System.Windows.Forms
Imports System.Configuration
Imports OfficeOpenXml

''' <summary>
''' خدمة استيراد البيانات من ملفات Excel
''' </summary>
Public Class ExcelImportService
    Private ReadOnly dbContext As PayrollDbContext

    Public Sub New()
        dbContext = New PayrollDbContext()
        ' تعيين ترخيص EPPlus للاستخدام غير التجاري
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial
    End Sub

    ''' <summary>
    ''' استيراد بيانات الموظفين من ملف Excel
    ''' </summary>
    Public Function ImportEmployees(filePath As String) As ImportResult
        Dim result As New ImportResult()

        Try
            If Not ValidateFile(filePath) Then
                result.Success = False
                result.ErrorMessage = "ملف غير صالح أو غير مدعوم"
                Return result
            End If

            Using package As New ExcelPackage(New FileInfo(filePath))
                Dim worksheet As ExcelWorksheet = package.Workbook.Worksheets.FirstOrDefault()
                If worksheet Is Nothing Then
                    result.Success = False
                    result.ErrorMessage = "لا توجد أوراق عمل في الملف"
                    Return result
                End If

                ' قراءة رؤوس الأعمدة
                Dim headers As New Dictionary(Of String, Integer)
                For col As Integer = 1 To worksheet.Dimension.End.Column
                    Dim headerValue As String = worksheet.Cells(1, col).Text.Trim()
                    If Not String.IsNullOrEmpty(headerValue) Then
                        headers(headerValue) = col
                    End If
                Next

                ' التحقق من وجود الأعمدة المطلوبة
                If Not headers.ContainsKey("اسم الموظف") OrElse Not headers.ContainsKey("الراتب الأساسي") Then
                    result.Success = False
                    result.ErrorMessage = "الملف يجب أن يحتوي على الأعمدة المطلوبة: 'اسم الموظف' و 'الراتب الأساسي'"
                    Return result
                End If

                ' استيراد البيانات
                For row As Integer = 2 To worksheet.Dimension.End.Row
                    Try
                        Dim employee As New Employee()

                        ' الحقول المطلوبة
                        employee.FullName = GetCellValue(worksheet, row, headers("اسم الموظف"))
                        employee.BasicSalary = Convert.ToDecimal(GetCellValue(worksheet, row, headers("الراتب الأساسي")))

                        ' التحقق من صحة البيانات المطلوبة
                        If String.IsNullOrWhiteSpace(employee.FullName) Then
                            result.Errors.Add($"الصف {row}: اسم الموظف مطلوب")
                            Continue For
                        End If

                        If employee.BasicSalary <= 0 Then
                            result.Errors.Add($"الصف {row}: الراتب الأساسي يجب أن يكون أكبر من صفر")
                            Continue For
                        End If

                        ' الحقول الاختيارية
                        If headers.ContainsKey("الرقم الوظيفي") Then
                            employee.EmployeeNumber = GetCellValue(worksheet, row, headers("الرقم الوظيفي"))
                        Else
                            employee.EmployeeNumber = GenerateEmployeeNumber()
                        End If

                        If headers.ContainsKey("اسم الأم") Then
                            employee.MotherName = GetCellValue(worksheet, row, headers("اسم الأم"))
                        End If

                        If headers.ContainsKey("رقم الـ IBAN") Then
                            employee.IBANNumber = GetCellValue(worksheet, row, headers("رقم الـ IBAN"))
                        End If

                        If headers.ContainsKey("تاريخ التعيين") Then
                            Dim hireDateText As String = GetCellValue(worksheet, row, headers("تاريخ التعيين"))
                            If Not String.IsNullOrWhiteSpace(hireDateText) Then
                                Dim hireDate As DateTime
                                If DateTime.TryParse(hireDateText, hireDate) Then
                                    employee.HireDate = hireDate
                                End If
                            End If
                        End If

                        ' ربط بالهيكل التنظيمي
                        LinkToOrganizationalStructure(employee, worksheet, row, headers)

                        ' التحقق من عدم وجود موظف بنفس الرقم الوظيفي
                        If dbContext.Employees.Any(Function(e) e.EmployeeNumber = employee.EmployeeNumber) Then
                            result.Errors.Add($"الصف {row}: الرقم الوظيفي '{employee.EmployeeNumber}' موجود مسبقاً")
                            Continue For
                        End If

                        ' إضافة الموظف
                        employee.CreatedBy = "Excel Import"
                        employee.CreatedDate = DateTime.Now
                        employee.IsActive = True

                        dbContext.Employees.Add(employee)
                        result.SuccessCount += 1

                    Catch ex As Exception
                        result.Errors.Add($"الصف {row}: {ex.Message}")
                    End Try
                Next

                ' حفظ التغييرات
                If result.SuccessCount > 0 Then
                    dbContext.SaveChanges()
                End If

                result.Success = True
                result.TotalRows = worksheet.Dimension.End.Row - 1 ' استثناء صف الرؤوس

            End Using

        Catch ex As Exception
            result.Success = False
            result.ErrorMessage = $"خطأ في استيراد الملف: {ex.Message}"
        End Try

        Return result
    End Function

    ''' <summary>
    ''' ربط الموظف بالهيكل التنظيمي
    ''' </summary>
    Private Sub LinkToOrganizationalStructure(employee As Employee, worksheet As ExcelWorksheet, row As Integer, headers As Dictionary(Of String, Integer))
        Try
            ' ربط بالدائرة
            If headers.ContainsKey("الدائرة") Then
                Dim departmentName As String = GetCellValue(worksheet, row, headers("الدائرة"))
                If Not String.IsNullOrWhiteSpace(departmentName) Then
                    Dim department = dbContext.Departments.FirstOrDefault(Function(d) d.DepartmentName = departmentName)
                    If department IsNot Nothing Then
                        employee.DepartmentId = department.DepartmentId
                    End If
                End If
            End If

            ' ربط بالقسم
            If headers.ContainsKey("القسم") Then
                Dim sectionName As String = GetCellValue(worksheet, row, headers("القسم"))
                If Not String.IsNullOrWhiteSpace(sectionName) Then
                    Dim section = dbContext.Sections.FirstOrDefault(Function(s) s.SectionName = sectionName)
                    If section IsNot Nothing Then
                        employee.SectionId = section.SectionId
                    End If
                End If
            End If

            ' ربط بالشعبة
            If headers.ContainsKey("الشعبة") Then
                Dim divisionName As String = GetCellValue(worksheet, row, headers("الشعبة"))
                If Not String.IsNullOrWhiteSpace(divisionName) Then
                    Dim division = dbContext.Divisions.FirstOrDefault(Function(d) d.DivisionName = divisionName)
                    If division IsNot Nothing Then
                        employee.DivisionId = division.DivisionId
                    End If
                End If
            End If

            ' ربط بالعنوان الوظيفي
            If headers.ContainsKey("العنوان الوظيفي") Then
                Dim jobTitleName As String = GetCellValue(worksheet, row, headers("العنوان الوظيفي"))
                If Not String.IsNullOrWhiteSpace(jobTitleName) Then
                    Dim jobTitle = dbContext.JobTitles.FirstOrDefault(Function(jt) jt.TitleName = jobTitleName)
                    If jobTitle IsNot Nothing Then
                        employee.JobTitleId = jobTitle.JobTitleId
                    End If
                End If
            End If

            ' ربط بالدرجة الوظيفية
            If headers.ContainsKey("الدرجة الوظيفية") Then
                Dim jobGradeName As String = GetCellValue(worksheet, row, headers("الدرجة الوظيفية"))
                If Not String.IsNullOrWhiteSpace(jobGradeName) Then
                    Dim jobGrade = dbContext.JobGrades.FirstOrDefault(Function(jg) jg.GradeName = jobGradeName)
                    If jobGrade IsNot Nothing Then
                        employee.JobGradeId = jobGrade.JobGradeId
                    End If
                End If
            End If

            ' ربط بالشهادة العلمية
            If headers.ContainsKey("الشهادة العلمية") Then
                Dim educationName As String = GetCellValue(worksheet, row, headers("الشهادة العلمية"))
                If Not String.IsNullOrWhiteSpace(educationName) Then
                    Dim education = dbContext.Educations.FirstOrDefault(Function(e) e.EducationName = educationName)
                    If education IsNot Nothing Then
                        employee.EducationId = education.EducationId
                    End If
                End If
            End If

            ' ربط بالمنصب
            If headers.ContainsKey("المنصب") Then
                Dim positionName As String = GetCellValue(worksheet, row, headers("المنصب"))
                If Not String.IsNullOrWhiteSpace(positionName) Then
                    Dim position = dbContext.Positions.FirstOrDefault(Function(p) p.PositionName = positionName)
                    If position IsNot Nothing Then
                        employee.PositionId = position.PositionId
                    End If
                End If
            End If

        Catch ex As Exception
            ' تجاهل أخطاء الربط وإكمال الاستيراد
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على قيمة الخلية
    ''' </summary>
    Private Function GetCellValue(worksheet As ExcelWorksheet, row As Integer, column As Integer) As String
        Try
            Return worksheet.Cells(row, column).Text.Trim()
        Catch
            Return String.Empty
        End Try
    End Function

    ''' <summary>
    ''' توليد رقم وظيفي تلقائي
    ''' </summary>
    Private Function GenerateEmployeeNumber() As String
        Dim lastEmployee = dbContext.Employees.OrderByDescending(Function(e) e.EmployeeId)).FirstOrDefault()
        Dim nextNumber As Integer = If(lastEmployee IsNot Nothing, lastEmployee.EmployeeId + 1, 1)
        Return $"EMP{nextNumber:D6}"
    End Function

    ''' <summary>
    ''' التحقق من صحة الملف
    ''' </summary>
    Private Function ValidateFile(filePath As String) As Boolean
        Try
            If Not File.Exists(filePath) Then Return False

            Dim extension As String = Path.GetExtension(filePath).ToLower()
            Dim allowedExtensions() As String = {".xlsx", ".xls"}

            If Not allowedExtensions.Contains(extension) Then Return False

            Dim fileInfo As New FileInfo(filePath)
            Dim maxFileSize As Long = Long.Parse(ConfigurationManager.AppSettings("MaxFileSize"))

            If fileInfo.Length > maxFileSize Then Return False

            Return True

        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إنشاء قالب Excel للاستيراد
    ''' </summary>
    Public Function CreateImportTemplate(savePath As String) As Boolean
        Try
            Using package As New ExcelPackage()
                Dim worksheet As ExcelWorksheet = package.Workbook.Worksheets.Add("قالب استيراد الموظفين")

                ' إضافة رؤوس الأعمدة
                Dim headers() As String = {
                    "اسم الموظف", "الراتب الأساسي", "الرقم الوظيفي", "اسم الأم", "رقم الـ IBAN",
                    "تاريخ التعيين", "الدائرة", "القسم", "الشعبة", "العنوان الوظيفي",
                    "الدرجة الوظيفية", "الشهادة العلمية", "المنصب"
                }

                For i As Integer = 0 To headers.Length - 1
                    worksheet.Cells(1, i + 1).Value = headers(i)
                    worksheet.Cells(1, i + 1).Style.Font.Bold = True
                    worksheet.Cells(1, i + 1).Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid
                    worksheet.Cells(1, i + 1).Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue)
                Next

                ' إضافة بيانات تجريبية
                worksheet.Cells(2, 1).Value = "أحمد محمد علي"
                worksheet.Cells(2, 2).Value = 500000
                worksheet.Cells(2, 3).Value = "EMP001"
                worksheet.Cells(2, 4).Value = "فاطمة"
                worksheet.Cells(2, 5).Value = "IQ12CBIQ861800101010001"
                worksheet.Cells(2, 6).Value = DateTime.Now.AddYears(-2).ToString("yyyy/MM/dd")

                ' تنسيق الأعمدة
                worksheet.Cells.AutoFitColumns()

                ' حفظ الملف
                Dim templatePath As String = Path.Combine(savePath, $"قالب_استيراد_الموظفين_{DateTime.Now:yyyyMMdd}.xlsx")
                package.SaveAs(New FileInfo(templatePath))

                MessageBox.Show($"تم إنشاء القالب بنجاح في: {templatePath}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return True
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء القالب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    Public Sub Dispose()
        dbContext?.Dispose()
    End Sub
End Class

''' <summary>
''' نتيجة عملية الاستيراد
''' </summary>
Public Class ImportResult
    Public Property Success As Boolean = False
    Public Property TotalRows As Integer = 0
    Public Property SuccessCount As Integer = 0
    Public Property ErrorMessage As String = String.Empty
    Public Property Errors As New List(Of String)

    Public ReadOnly Property FailureCount As Integer
        Get
            Return TotalRows - SuccessCount
        End Get
    End Property

    Public ReadOnly Property HasErrors As Boolean
        Get
            Return Errors.Count > 0 OrElse Not String.IsNullOrEmpty(ErrorMessage)
        End Get
    End Property
End Class
