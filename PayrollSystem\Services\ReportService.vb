Imports System
Imports System.IO
Imports System.Data
Imports System.Windows.Forms
Imports System.Configuration
Imports OfficeOpenXml
Imports OfficeOpenXml.Style
Imports iTextSharp.text
Imports iTextSharp.text.pdf

''' <summary>
''' خدمة إنشاء وتصدير التقارير
''' </summary>
Public Class ReportService
    Private ReadOnly dbContext As PayrollDbContext
    Private ReadOnly reportsPath As String

    Public Sub New()
        dbContext = New PayrollDbContext()
        reportsPath = ConfigurationManager.AppSettings("ReportsPath")
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial

        ' التأكد من وجود مجلد التقارير
        If Not Directory.Exists(reportsPath) Then
            Directory.CreateDirectory(reportsPath)
        End If
    End Sub

    ''' <summary>
    ''' تصدير قائمة الموظفين إلى Excel
    ''' </summary>
    Public Function ExportEmployeesToExcel() As String
        Try
            Using package As New ExcelPackage()
                Dim worksheet As ExcelWorksheet = package.Workbook.Worksheets.Add("قائمة الموظفين")

                ' إعداد رؤوس الأعمدة
                Dim headers() As String = {
                    "الرقم الوظيفي", "اسم الموظف", "اسم الأم", "الراتب الأساسي",
                    "الدائرة", "القسم", "الشعبة", "العنوان الوظيفي",
                    "الدرجة الوظيفية", "الشهادة العلمية", "المنصب", "تاريخ التعيين", "الحالة"
                }

                ' إضافة رؤوس الأعمدة
                For i As Integer = 0 To headers.Length - 1
                    worksheet.Cells(1, i + 1).Value = headers(i)
                    worksheet.Cells(1, i + 1).Style.Font.Bold = True
                    worksheet.Cells(1, i + 1).Style.Fill.PatternType = ExcelFillStyle.Solid
                    worksheet.Cells(1, i + 1).Style.Fill.BackgroundColor.SetColor(Drawing.Color.LightBlue)
                    worksheet.Cells(1, i + 1).Style.Border.BorderAround(ExcelBorderStyle.Thin)
                Next

                ' جلب بيانات الموظفين
                Dim employees = dbContext.Employees.Include("Department").Include("Section").Include("Division") _
                    .Include("JobTitle").Include("JobGrade").Include("Education").Include("Position") _
                    .OrderBy(Function(e) e.EmployeeNumber).ToList()

                ' إضافة البيانات
                For i As Integer = 0 To employees.Count - 1
                    Dim emp = employees(i)
                    Dim row As Integer = i + 2

                    worksheet.Cells(row, 1).Value = emp.EmployeeNumber
                    worksheet.Cells(row, 2).Value = emp.FullName
                    worksheet.Cells(row, 3).Value = emp.MotherName
                    worksheet.Cells(row, 4).Value = emp.BasicSalary
                    worksheet.Cells(row, 5).Value = emp.Department?.DepartmentName
                    worksheet.Cells(row, 6).Value = emp.Section?.SectionName
                    worksheet.Cells(row, 7).Value = emp.Division?.DivisionName
                    worksheet.Cells(row, 8).Value = emp.JobTitle?.TitleName
                    worksheet.Cells(row, 9).Value = emp.JobGrade?.GradeName
                    worksheet.Cells(row, 10).Value = emp.Education?.EducationName
                    worksheet.Cells(row, 11).Value = emp.Position?.PositionName
                    worksheet.Cells(row, 12).Value = emp.HireDate?.ToString("yyyy/MM/dd")
                    worksheet.Cells(row, 13).Value = If(emp.IsActive, "نشط", "غير نشط")

                    ' تنسيق الصفوف
                    For col As Integer = 1 To headers.Length
                        worksheet.Cells(row, col).Style.Border.BorderAround(ExcelBorderStyle.Thin)
                    Next
                Next

                ' تنسيق عام
                worksheet.Cells.AutoFitColumns()
                worksheet.View.RightToLeft = True

                ' حفظ الملف
                Dim fileName As String = $"قائمة_الموظفين_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                Dim filePath As String = Path.Combine(reportsPath, fileName)
                package.SaveAs(New FileInfo(filePath))

                Return filePath
            End Using

        Catch ex As Exception
            Throw New Exception($"خطأ في تصدير قائمة الموظفين: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' تصدير كشف راتب شهري إلى Excel
    ''' </summary>
    Public Function ExportPayrollToExcel(year As Integer, month As Integer) As String
        Try
            Using package As New ExcelPackage()
                Dim worksheet As ExcelWorksheet = package.Workbook.Worksheets.Add($"كشف راتب {year}/{month:D2}")

                ' عنوان التقرير
                worksheet.Cells(1, 1, 1, 15).Merge = True
                worksheet.Cells(1, 1).Value = $"وزارة الشباب والرياضة - كشف راتب شهر {month}/{year}"
                worksheet.Cells(1, 1).Style.Font.Size = 16
                worksheet.Cells(1, 1).Style.Font.Bold = True
                worksheet.Cells(1, 1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center

                ' رؤوس الأعمدة
                Dim headers() As String = {
                    "ت", "الرقم الوظيفي", "اسم الموظف", "الراتب الأساسي", "المخصصات",
                    "إجمالي الراتب", "ضريبة الدخل", "التقاعد", "الاستقطاعات الأخرى",
                    "إجمالي الاستقطاعات", "صافي الراتب", "الدائرة", "القسم", "التوقيع"
                }

                For i As Integer = 0 To headers.Length - 1
                    worksheet.Cells(3, i + 1).Value = headers(i)
                    worksheet.Cells(3, i + 1).Style.Font.Bold = True
                    worksheet.Cells(3, i + 1).Style.Fill.PatternType = ExcelFillStyle.Solid
                    worksheet.Cells(3, i + 1).Style.Fill.BackgroundColor.SetColor(Drawing.Color.LightGray)
                    worksheet.Cells(3, i + 1).Style.Border.BorderAround(ExcelBorderStyle.Thin)
                Next

                ' جلب بيانات الرواتب
                Dim payrollRecords = dbContext.PayrollRecords.Include("Employee").Include("Employee.Department") _
                    .Include("Employee.Section").Where(Function(pr) pr.PayrollYear = year AndAlso pr.PayrollMonth = month) _
                    .OrderBy(Function(pr) pr.Employee.EmployeeNumber).ToList()

                ' إضافة البيانات
                Dim totalGross As Decimal = 0
                Dim totalNet As Decimal = 0
                Dim totalDeductions As Decimal = 0

                For i As Integer = 0 To payrollRecords.Count - 1
                    Dim record = payrollRecords(i)
                    Dim row As Integer = i + 4

                    worksheet.Cells(row, 1).Value = i + 1
                    worksheet.Cells(row, 2).Value = record.Employee.EmployeeNumber
                    worksheet.Cells(row, 3).Value = record.Employee.FullName
                    worksheet.Cells(row, 4).Value = record.BasicSalary
                    worksheet.Cells(row, 5).Value = record.TotalAllowances
                    worksheet.Cells(row, 6).Value = record.GrossSalary
                    worksheet.Cells(row, 7).Value = CalculateTax(record.GrossSalary)
                    worksheet.Cells(row, 8).Value = CalculateRetirement(record.BasicSalary)
                    worksheet.Cells(row, 9).Value = record.TotalDeductions - CalculateTax(record.GrossSalary) - CalculateRetirement(record.BasicSalary)
                    worksheet.Cells(row, 10).Value = record.TotalDeductions
                    worksheet.Cells(row, 11).Value = record.NetSalary
                    worksheet.Cells(row, 12).Value = record.Employee.Department?.DepartmentName
                    worksheet.Cells(row, 13).Value = record.Employee.Section?.SectionName
                    worksheet.Cells(row, 14).Value = ""

                    ' تجميع الإجماليات
                    totalGross += record.GrossSalary
                    totalNet += record.NetSalary
                    totalDeductions += record.TotalDeductions

                    ' تنسيق الصفوف
                    For col As Integer = 1 To headers.Length
                        worksheet.Cells(row, col).Style.Border.BorderAround(ExcelBorderStyle.Thin)
                        If col >= 4 AndAlso col <= 11 Then
                            worksheet.Cells(row, col).Style.Numberformat.Format = "#,##0"
                        End If
                    Next
                Next

                ' إضافة صف الإجماليات
                Dim totalRow As Integer = payrollRecords.Count + 5
                worksheet.Cells(totalRow, 1, totalRow, 5).Merge = True
                worksheet.Cells(totalRow, 1).Value = "الإجماليات"
                worksheet.Cells(totalRow, 6).Value = totalGross
                worksheet.Cells(totalRow, 10).Value = totalDeductions
                worksheet.Cells(totalRow, 11).Value = totalNet

                For col As Integer = 1 To headers.Length
                    worksheet.Cells(totalRow, col).Style.Font.Bold = True
                    worksheet.Cells(totalRow, col).Style.Fill.PatternType = ExcelFillStyle.Solid
                    worksheet.Cells(totalRow, col).Style.Fill.BackgroundColor.SetColor(Drawing.Color.Yellow)
                    worksheet.Cells(totalRow, col).Style.Border.BorderAround(ExcelBorderStyle.Thin)
                Next

                ' تنسيق عام
                worksheet.Cells.AutoFitColumns()
                worksheet.View.RightToLeft = True

                ' حفظ الملف
                Dim fileName As String = $"كشف_راتب_{year}_{month:D2}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                Dim filePath As String = Path.Combine(reportsPath, fileName)
                package.SaveAs(New FileInfo(filePath))

                Return filePath
            End Using

        Catch ex As Exception
            Throw New Exception($"خطأ في تصدير كشف الراتب: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' إنشاء قسيمة راتب PDF
    ''' </summary>
    Public Function GeneratePayslipPDF(employeeId As Integer, year As Integer, month As Integer) As String
        Try
            ' جلب بيانات الموظف والراتب
            Dim employee = dbContext.Employees.Include("Department").Include("Section") _
                .Include("JobTitle").FirstOrDefault(Function(e) e.EmployeeId = employeeId)

            If employee Is Nothing Then
                Throw New Exception("الموظف غير موجود")
            End If

            Dim payrollRecord = dbContext.PayrollRecords.Include("PayrollDetails") _
                .FirstOrDefault(Function(pr) pr.EmployeeId = employeeId AndAlso pr.PayrollYear = year AndAlso pr.PayrollMonth = month)

            If payrollRecord Is Nothing Then
                Throw New Exception("لا توجد بيانات راتب لهذا الشهر")
            End If

            ' إنشاء ملف PDF
            Dim fileName As String = $"قسيمة_راتب_{employee.EmployeeNumber}_{year}_{month:D2}.pdf"
            Dim filePath As String = Path.Combine(reportsPath, fileName)

            Using document As New Document(PageSize.A4)
                PdfWriter.GetInstance(document, New FileStream(filePath, FileMode.Create))
                document.Open()

                ' إعداد الخط العربي
                Dim arabicFont As BaseFont = BaseFont.CreateFont("c:\windows\fonts\arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED)
                Dim titleFont As New Font(arabicFont, 16, Font.BOLD)
                Dim headerFont As New Font(arabicFont, 12, Font.BOLD)
                Dim normalFont As New Font(arabicFont, 10, Font.NORMAL)

                ' عنوان القسيمة
                Dim title As New Paragraph("وزارة الشباب والرياضة", titleFont)
                title.Alignment = Element.ALIGN_CENTER
                document.Add(title)

                Dim subtitle As New Paragraph($"قسيمة راتب شهر {month}/{year}", headerFont)
                subtitle.Alignment = Element.ALIGN_CENTER
                document.Add(subtitle)

                document.Add(New Paragraph(" "))

                ' بيانات الموظف
                Dim empTable As New PdfPTable(2)
                empTable.WidthPercentage = 100
                empTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL

                empTable.AddCell(New PdfPCell(New Phrase("الرقم الوظيفي:", headerFont)))
                empTable.AddCell(New PdfPCell(New Phrase(employee.EmployeeNumber, normalFont)))
                empTable.AddCell(New PdfPCell(New Phrase("اسم الموظف:", headerFont)))
                empTable.AddCell(New PdfPCell(New Phrase(employee.FullName, normalFont)))
                empTable.AddCell(New PdfPCell(New Phrase("الدائرة:", headerFont)))
                empTable.AddCell(New PdfPCell(New Phrase(employee.Department?.DepartmentName, normalFont)))
                empTable.AddCell(New PdfPCell(New Phrase("القسم:", headerFont)))
                empTable.AddCell(New PdfPCell(New Phrase(employee.Section?.SectionName, normalFont)))

                document.Add(empTable)
                document.Add(New Paragraph(" "))

                ' تفاصيل الراتب
                Dim salaryTable As New PdfPTable(2)
                salaryTable.WidthPercentage = 100
                salaryTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL

                salaryTable.AddCell(New PdfPCell(New Phrase("الراتب الأساسي:", headerFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase(payrollRecord.BasicSalary.ToString("N0"), normalFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase("إجمالي المخصصات:", headerFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase(payrollRecord.TotalAllowances.ToString("N0"), normalFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase("إجمالي الراتب:", headerFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase(payrollRecord.GrossSalary.ToString("N0"), normalFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase("إجمالي الاستقطاعات:", headerFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase(payrollRecord.TotalDeductions.ToString("N0"), normalFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase("صافي الراتب:", headerFont)))
                salaryTable.AddCell(New PdfPCell(New Phrase(payrollRecord.NetSalary.ToString("N0"), normalFont)))

                document.Add(salaryTable)

                document.Close()
            End Using

            Return filePath

        Catch ex As Exception
            Throw New Exception($"خطأ في إنشاء قسيمة الراتب: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' حساب ضريبة الدخل
    ''' </summary>
    Private Function CalculateTax(grossSalary As Decimal) As Decimal
        Dim taxRate As Decimal = Decimal.Parse(ConfigurationManager.AppSettings("TaxRate")) / 100
        Return grossSalary * taxRate
    End Function

    ''' <summary>
    ''' حساب التقاعد
    ''' </summary>
    Private Function CalculateRetirement(basicSalary As Decimal) As Decimal
        Dim retirementRate As Decimal = Decimal.Parse(ConfigurationManager.AppSettings("RetirementRate")) / 100
        Return basicSalary * retirementRate
    End Function

    ''' <summary>
    ''' فتح مجلد التقارير
    ''' </summary>
    Public Sub OpenReportsFolder()
        Try
            If Directory.Exists(reportsPath) Then
                Process.Start("explorer.exe", reportsPath)
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح مجلد التقارير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Public Sub Dispose()
        dbContext?.Dispose()
    End Sub
End Class
