<Application x:Class="UnifiedAccountingSystem.Application"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="LightBlue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Resources -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#B00020"/>
            <SolidColorBrush x:Key="OnPrimaryBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="OnSurfaceBrush" Color="#000000"/>
            
            <!-- Arabic Font -->
            <FontFamily x:Key="ArabicFont">Segoe UI, Tahoma, Arial</FontFamily>
            
            <!-- Common Styles -->
            <Style x:Key="PageTitleStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
                <Setter Property="HorizontalAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>
            
            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
                <Setter Property="Margin" Value="0,10,0,10"/>
                <Setter Property="HorizontalAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>
            
            <Style x:Key="LabelStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Margin" Value="0,0,10,0"/>
            </Style>
            
            <Style x:Key="InputStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Margin" Value="0,5"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            </Style>
            
            <Style x:Key="ComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Margin" Value="0,5"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            </Style>
            
            <Style x:Key="ButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Height" Value="36"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="16,0"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>
            
            <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource OnPrimaryBrush}"/>
            </Style>
            
            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="36"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="16,0"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            </Style>
            
            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="10"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            </Style>
            
            <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="SelectionMode" Value="Single"/>
                <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
                <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
                <Setter Property="RowBackground" Value="White"/>
                <Setter Property="Margin" Value="0,10"/>
            </Style>
            
            <!-- Menu Styles -->
            <Style x:Key="MenuItemStyle" TargetType="MenuItem">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Padding" Value="10,5"/>
            </Style>
            
            <!-- Navigation Styles -->
            <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="48"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="Padding" Value="20,0"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#E3F2FD"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <!-- Status Bar Style -->
            <Style x:Key="StatusBarStyle" TargetType="StatusBar">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource OnPrimaryBrush}"/>
                <Setter Property="Height" Value="25"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
