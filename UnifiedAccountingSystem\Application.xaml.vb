Imports System.Globalization
Imports System.Threading
Imports System.Windows
Imports System.Configuration

''' <summary>
''' تطبيق النظام المحاسبي الموحد
''' </summary>
Class Application
    Inherits System.Windows.Application

    ''' <summary>
    ''' بدء تشغيل التطبيق
    ''' </summary>
    Protected Overrides Sub OnStartup(e As StartupEventArgs)
        ' تعيين الثقافة العربية
        SetupCulture()

        ' تعيين معالج الأخطاء العامة
        AddHandler Me.DispatcherUnhandledException, AddressOf Application_DispatcherUnhandledException
        AddHandler AppDomain.CurrentDomain.UnhandledException, AddressOf CurrentDomain_UnhandledException

        ' تهيئة قاعدة البيانات
        InitializeDatabase()

        ' تطبيق الثيم
        ApplyTheme()

        MyBase.OnStartup(e)
    End Sub

    ''' <summary>
    ''' تعيين الثقافة العربية للتطبيق
    ''' </summary>
    Private Sub SetupCulture()
        Try
            Dim culture As String = ConfigurationManager.AppSettings("DefaultCulture")
            If String.IsNullOrEmpty(culture) Then culture = "ar-IQ"

            Dim cultureInfo As New CultureInfo(culture)
            Thread.CurrentThread.CurrentCulture = cultureInfo
            Thread.CurrentThread.CurrentUICulture = cultureInfo

            ' تعيين اتجاه النص من اليمين إلى اليسار
            FrameworkElement.LanguageProperty.OverrideMetadata(
                GetType(FrameworkElement),
                New FrameworkPropertyMetadata(
                    System.Windows.Markup.XmlLanguage.GetLanguage(culture)))

        Catch ex As Exception
            ' في حالة فشل تعيين الثقافة، استخدم الافتراضية
            Console.WriteLine($"خطأ في تعيين الثقافة: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة قاعدة البيانات
    ''' </summary>
    Private Sub InitializeDatabase()
        Try
            Using context As New Data.AccountingDbContext()
                ' التأكد من إنشاء قاعدة البيانات
                context.Database.CreateIfNotExists()

                ' تشغيل النسخ الاحتياطي التلقائي
                ' Dim databaseService As New Services.DatabaseService()
                ' databaseService.StartAutoBackup()
            End Using
        Catch ex As Exception
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}",
                          "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق الثيم المحدد
    ''' </summary>
    Private Sub ApplyTheme()
        Try
            Dim theme As String = ConfigurationManager.AppSettings("Theme")
            Dim primaryColor As String = ConfigurationManager.AppSettings("PrimaryColor")

            If String.IsNullOrEmpty(theme) Then theme = "Light"
            If String.IsNullOrEmpty(primaryColor) Then primaryColor = "Blue"

            ' يمكن إضافة منطق تطبيق الثيم هنا

        Catch ex As Exception
            Console.WriteLine($"خطأ في تطبيق الثيم: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج الأخطاء العامة للتطبيق
    ''' </summary>
    Private Sub Application_DispatcherUnhandledException(sender As Object, e As Threading.DispatcherUnhandledExceptionEventArgs)
        Try
            Dim errorMessage As String = $"حدث خطأ غير متوقع: {e.Exception.Message}"

            ' تسجيل الخطأ
            LogError(e.Exception)

            ' إظهار رسالة للمستخدم
            MessageBox.Show(errorMessage, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)

            ' منع إغلاق التطبيق
            e.Handled = True

        Catch
            ' في حالة فشل معالجة الخطأ، اتركه يمر
        End Try
    End Sub

    ''' <summary>
    ''' معالج الأخطاء العامة للنطاق
    ''' </summary>
    Private Sub CurrentDomain_UnhandledException(sender As Object, e As UnhandledExceptionEventArgs)
        Try
            Dim ex As Exception = TryCast(e.ExceptionObject, Exception)
            If ex IsNot Nothing Then
                LogError(ex)
            End If
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل الأخطاء في ملف
    ''' </summary>
    Private Sub LogError(ex As Exception)
        Try
            Dim logPath As String = IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs")
            If Not IO.Directory.Exists(logPath) Then
                IO.Directory.CreateDirectory(logPath)
            End If

            Dim logFile As String = IO.Path.Combine(logPath, $"Error_{DateTime.Now:yyyyMMdd}.log")
            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {ex.ToString()}{Environment.NewLine}{Environment.NewLine}"

            IO.File.AppendAllText(logFile, logEntry)

        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    ''' <summary>
    ''' إنهاء التطبيق
    ''' </summary>
    Protected Overrides Sub OnExit(e As ExitEventArgs)
        Try
            ' تنظيف الموارد
            CleanupResources()
        Catch
            ' تجاهل أخطاء التنظيف
        Finally
            MyBase.OnExit(e)
        End Try
    End Sub

    ''' <summary>
    ''' تنظيف الموارد
    ''' </summary>
    Private Sub CleanupResources()
        Try
            ' إغلاق اتصالات قاعدة البيانات
            ' تنظيف الملفات المؤقتة
            ' حفظ الإعدادات
        Catch ex As Exception
            Console.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}")
        End Try
    End Sub

End Class
