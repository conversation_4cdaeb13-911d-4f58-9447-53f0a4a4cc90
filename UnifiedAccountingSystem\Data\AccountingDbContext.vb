Imports System.Data.Entity
Imports System.Data.Entity.ModelConfiguration.Conventions
Imports System.Configuration
Imports UnifiedAccountingSystem.Models

Namespace Data

    ''' <summary>
    ''' سياق قاعدة البيانات للنظام المحاسبي الموحد
    ''' </summary>
    Public Class AccountingDbContext
        Inherits DbContext

        Public Sub New()
            MyBase.New("AccountingConnectionString")
            Database.SetInitializer(New AccountingDbInitializer())
        End Sub

        ' جداول إدارة المستخدمين
        Public Property UserGroups As DbSet(Of UserGroup)
        Public Property Users As DbSet(Of User)
        Public Property Permissions As DbSet(Of Permission)
        Public Property UserPermissions As DbSet(Of UserPermission)
        Public Property AuditLogs As DbSet(Of AuditLog)
        Public Property UserSessions As DbSet(Of UserSession)

        ' جداول الهيكل التنظيمي
        Public Property Organizations As DbSet(Of Organization)
        Public Property Departments As DbSet(Of Department)
        Public Property Sections As DbSet(Of Section)
        Public Property Divisions As DbSet(Of Division)
        Public Property JobTitles As DbSet(Of JobTitle)
        Public Property Qualifications As DbSet(Of Qualification)
        Public Property JobGrades As DbSet(Of JobGrade)
        Public Property Stages As DbSet(Of Stage)

        ' جداول المحاسبة
        Public Property Currencies As DbSet(Of Currency)
        Public Property FinalAccounts As DbSet(Of FinalAccount)
        Public Property AccountingPeriods As DbSet(Of AccountingPeriod)
        Public Property ChartOfAccounts As DbSet(Of ChartOfAccount)
        Public Property CashBoxes As DbSet(Of CashBox)
        Public Property Banks As DbSet(Of Bank)
        Public Property BankBranches As DbSet(Of BankBranch)
        Public Property BankAccounts As DbSet(Of BankAccount)

        ' جداول الموظفين والرواتب
        Public Property Employees As DbSet(Of Employee)
        Public Property EmployeeAllowances As DbSet(Of EmployeeAllowance)
        Public Property EmployeeDeductions As DbSet(Of EmployeeDeduction)
        Public Property PayrollSettings As DbSet(Of PayrollSetting)
        Public Property PayrollRecords As DbSet(Of PayrollRecord)
        Public Property PayrollDetails As DbSet(Of PayrollDetail)
        Public Property PayrollTemplates As DbSet(Of PayrollTemplate)
        Public Property PayrollBatches As DbSet(Of PayrollBatch)

        ' جداول المصروفات والقيود
        Public Property JournalEntries As DbSet(Of JournalEntry)
        Public Property JournalEntryDetails As DbSet(Of JournalEntryDetail)
        Public Property PaymentVouchers As DbSet(Of PaymentVoucher)
        Public Property PaymentVoucherDetails As DbSet(Of PaymentVoucherDetail)
        Public Property ReceiptVouchers As DbSet(Of ReceiptVoucher)
        Public Property ReceiptVoucherDetails As DbSet(Of ReceiptVoucherDetail)

        Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
            ' إزالة اتفاقية تسمية الجداول بالجمع
            modelBuilder.Conventions.Remove(Of PluralizingTableNameConvention)()

            ' تكوين العلاقات والقيود
            ConfigureUserRelationships(modelBuilder)
            ConfigureOrganizationRelationships(modelBuilder)
            ConfigureAccountingRelationships(modelBuilder)
            ConfigureEmployeeRelationships(modelBuilder)
            ConfigurePayrollRelationships(modelBuilder)
            ConfigureExpenseRelationships(modelBuilder)

            MyBase.OnModelCreating(modelBuilder)
        End Sub

        Private Sub ConfigureUserRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات المستخدمين
            modelBuilder.Entity(Of User)() _
                .HasOptional(Function(u) u.UserGroup) _
                .WithMany(Function(ug) ug.Users) _
                .HasForeignKey(Function(u) u.UserGroupId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of UserPermission)() _
                .HasRequired(Function(up) up.User) _
                .WithMany(Function(u) u.UserPermissions) _
                .HasForeignKey(Function(up) up.UserId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of AuditLog)() _
                .HasRequired(Function(al) al.User) _
                .WithMany(Function(u) u.AuditLogs) _
                .HasForeignKey(Function(al) al.UserId) _
                .WillCascadeOnDelete(False)

            ' فهارس فريدة
            modelBuilder.Entity(Of User)() _
                .HasIndex(Function(u) u.Username) _
                .IsUnique()

            modelBuilder.Entity(Of User)() _
                .HasIndex(Function(u) u.AccountNumber) _
                .IsUnique()
        End Sub

        Private Sub ConfigureOrganizationRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات الهيكل التنظيمي
            modelBuilder.Entity(Of Department)() _
                .HasRequired(Function(d) d.Organization) _
                .WithMany(Function(o) o.Departments) _
                .HasForeignKey(Function(d) d.OrganizationId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Section)() _
                .HasOptional(Function(s) s.Department) _
                .WithMany(Function(d) d.Sections) _
                .HasForeignKey(Function(s) s.DepartmentId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Division)() _
                .HasRequired(Function(div) div.Section) _
                .WithMany(Function(s) s.Divisions) _
                .HasForeignKey(Function(div) div.SectionId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Stage)() _
                .HasOptional(Function(st) st.JobGrade) _
                .WithMany(Function(jg) jg.Stages) _
                .HasForeignKey(Function(st) st.JobGradeId) _
                .WillCascadeOnDelete(False)
        End Sub

        Private Sub ConfigureAccountingRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات المحاسبة
            modelBuilder.Entity(Of ChartOfAccount)() _
                .HasOptional(Function(c) c.ParentAccount) _
                .WithMany(Function(c) c.ChildAccounts) _
                .HasForeignKey(Function(c) c.ParentAccountId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of BankBranch)() _
                .HasRequired(Function(bb) bb.Bank) _
                .WithMany(Function(b) b.BankBranches) _
                .HasForeignKey(Function(bb) bb.BankId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of BankAccount)() _
                .HasRequired(Function(ba) ba.BankBranch) _
                .WithMany(Function(bb) bb.BankAccounts) _
                .HasForeignKey(Function(ba) ba.BranchId) _
                .WillCascadeOnDelete(False)

            ' فهارس فريدة
            modelBuilder.Entity(Of ChartOfAccount)() _
                .HasIndex(Function(c) c.AccountCode) _
                .IsUnique()

            modelBuilder.Entity(Of Currency)() _
                .HasIndex(Function(c) c.CurrencyCode) _
                .IsUnique()
        End Sub

        Private Sub ConfigureEmployeeRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات الموظفين
            modelBuilder.Entity(Of Employee)() _
                .HasOptional(Function(e) e.Department) _
                .WithMany(Function(d) d.Employees) _
                .HasForeignKey(Function(e) e.DepartmentId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of EmployeeAllowance)() _
                .HasRequired(Function(ea) ea.Employee) _
                .WithMany(Function(e) e.EmployeeAllowances) _
                .HasForeignKey(Function(ea) ea.EmployeeId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of EmployeeDeduction)() _
                .HasRequired(Function(ed) ed.Employee) _
                .WithMany(Function(e) e.EmployeeDeductions) _
                .HasForeignKey(Function(ed) ed.EmployeeId) _
                .WillCascadeOnDelete(True)

            ' فهرس فريد
            modelBuilder.Entity(Of Employee)() _
                .HasIndex(Function(e) e.EmployeeNumber) _
                .IsUnique()
        End Sub

        Private Sub ConfigurePayrollRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات الرواتب
            modelBuilder.Entity(Of PayrollRecord)() _
                .HasRequired(Function(pr) pr.Employee) _
                .WithMany(Function(e) e.PayrollRecords) _
                .HasForeignKey(Function(pr) pr.EmployeeId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of PayrollDetail)() _
                .HasRequired(Function(pd) pd.PayrollRecord) _
                .WithMany(Function(pr) pr.PayrollDetails) _
                .HasForeignKey(Function(pd) pd.PayrollId) _
                .WillCascadeOnDelete(True)

            ' فهرس مركب لمنع تكرار الراتب للموظف في نفس الشهر
            modelBuilder.Entity(Of PayrollRecord)() _
                .HasIndex(Function(pr) New With {pr.EmployeeId, pr.PayrollYear, pr.PayrollMonth}) _
                .IsUnique()
        End Sub

        Private Sub ConfigureExpenseRelationships(modelBuilder As DbModelBuilder)
            ' تكوين علاقات المصروفات والقيود
            modelBuilder.Entity(Of JournalEntryDetail)() _
                .HasRequired(Function(jed) jed.JournalEntry) _
                .WithMany(Function(je) je.JournalEntryDetails) _
                .HasForeignKey(Function(jed) jed.EntryId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of PaymentVoucherDetail)() _
                .HasRequired(Function(pvd) pvd.PaymentVoucher) _
                .WithMany(Function(pv) pv.PaymentVoucherDetails) _
                .HasForeignKey(Function(pvd) pvd.VoucherId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of ReceiptVoucherDetail)() _
                .HasRequired(Function(rvd) rvd.ReceiptVoucher) _
                .WithMany(Function(rv) rv.ReceiptVoucherDetails) _
                .HasForeignKey(Function(rvd) rvd.VoucherId) _
                .WillCascadeOnDelete(True)

            ' فهارس فريدة
            modelBuilder.Entity(Of JournalEntry)() _
                .HasIndex(Function(je) je.EntryNumber) _
                .IsUnique()

            modelBuilder.Entity(Of PaymentVoucher)() _
                .HasIndex(Function(pv) pv.VoucherNumber) _
                .IsUnique()

            modelBuilder.Entity(Of ReceiptVoucher)() _
                .HasIndex(Function(rv) rv.VoucherNumber) _
                .IsUnique()
        End Sub

    End Class

    ''' <summary>
    ''' مُهيئ قاعدة البيانات
    ''' </summary>
    Public Class AccountingDbInitializer
        Inherits CreateDatabaseIfNotExists(Of AccountingDbContext)

        Protected Overrides Sub Seed(context As AccountingDbContext)
            ' إضافة البيانات الأولية
            SeedUsers(context)
            SeedPermissions(context)
            SeedCurrencies(context)
            SeedPayrollSettings(context)
            SeedDefaultAccounts(context)
            SeedOrganizationData(context)

            MyBase.Seed(context)
        End Sub

        Private Sub SeedUsers(context As AccountingDbContext)
            ' إضافة مجموعة المدراء
            Dim adminGroup As New UserGroup() With {
                .GroupNumber = "001",
                .GroupName = "مجموعة المدراء",
                .Notes = "مجموعة المستخدمين الإداريين",
                .CreatedBy = "System"
            }
            context.UserGroups.Add(adminGroup)

            ' إضافة المستخدم الافتراضي
            Dim adminUser As New User() With {
                .AccountNumber = "ADM001",
                .AccountName = "مدير النظام",
                .Username = "admin",
                .PasswordHash = User.HashPassword("admin"),
                .AccountType = "مدير",
                .UserGroup = adminGroup,
                .IsActive = True,
                .CreatedBy = "System"
            }
            context.Users.Add(adminUser)

            context.SaveChanges()
        End Sub

        Private Sub SeedPermissions(context As AccountingDbContext)
            ' إضافة الصلاحيات الأساسية
            Dim permissions() As Permission = {
                New Permission() With {.PermissionName = "إدارة المستخدمين", .PermissionCode = "USERS_MANAGE", .ModuleName = "النظام"},
                New Permission() With {.PermissionName = "إدارة الموظفين", .PermissionCode = "EMPLOYEES_MANAGE", .ModuleName = "الموظفين"},
                New Permission() With {.PermissionName = "إدارة الحسابات", .PermissionCode = "ACCOUNTS_MANAGE", .ModuleName = "المحاسبة"},
                New Permission() With {.PermissionName = "احتساب الرواتب", .PermissionCode = "PAYROLL_CALCULATE", .ModuleName = "الرواتب"},
                New Permission() With {.PermissionName = "القيود المحاسبية", .PermissionCode = "JOURNAL_ENTRIES", .ModuleName = "المحاسبة"},
                New Permission() With {.PermissionName = "سندات الصرف", .PermissionCode = "PAYMENT_VOUCHERS", .ModuleName = "المحاسبة"},
                New Permission() With {.PermissionName = "سندات القبض", .PermissionCode = "RECEIPT_VOUCHERS", .ModuleName = "المحاسبة"},
                New Permission() With {.PermissionName = "التقارير", .PermissionCode = "REPORTS_VIEW", .ModuleName = "التقارير"},
                New Permission() With {.PermissionName = "النسخ الاحتياطي", .PermissionCode = "BACKUP_MANAGE", .ModuleName = "النظام"}
            }

            For Each permission In permissions
                context.Permissions.Add(permission)
            Next

            context.SaveChanges()
        End Sub

        Private Sub SeedCurrencies(context As AccountingDbContext)
            ' إضافة العملات الأساسية
            Dim currencies() As Currency = {
                New Currency() With {
                    .CurrencyNumber = "001",
                    .CurrencyName = "الدينار العراقي",
                    .CurrencyCode = "IQD",
                    .CurrencySubunit = "فلس",
                    .CurrencyType = "محلية",
                    .ExchangeRate = 1,
                    .IsDefault = True,
                    .CreatedBy = "System"
                },
                New Currency() With {
                    .CurrencyNumber = "002",
                    .CurrencyName = "الدولار الأمريكي",
                    .CurrencyCode = "USD",
                    .CurrencySubunit = "سنت",
                    .CurrencyType = "أجنبية",
                    .ExchangeRate = 1320,
                    .CreatedBy = "System"
                }
            }

            For Each currency In currencies
                context.Currencies.Add(currency)
            Next

            context.SaveChanges()
        End Sub

        Private Sub SeedPayrollSettings(context As AccountingDbContext)
            ' إضافة إعدادات الرواتب الافتراضية
            Dim settings() As PayrollSetting = {
                New PayrollSetting() With {.SettingName = "نسبة صندوق التقاعد", .SettingValue = "10", .Description = "نسبة صندوق تقاعد موظفي الدولة", .DataType = "رقم"},
                New PayrollSetting() With {.SettingName = "نسبة مساهمة الدائرة", .SettingValue = "15", .Description = "نسبة مساهمة الدائرة في المساهمة الحكومية", .DataType = "رقم"},
                New PayrollSetting() With {.SettingName = "مخصص الزوجية", .SettingValue = "25000", .Description = "مبلغ مخصص الزوجية الشهري", .DataType = "رقم"},
                New PayrollSetting() With {.SettingName = "مخصص الطفل الواحد", .SettingValue = "12500", .Description = "مبلغ مخصص الطفل الواحد الشهري", .DataType = "رقم"},
                New PayrollSetting() With {.SettingName = "الحد الأقصى للأطفال", .SettingValue = "6", .Description = "الحد الأقصى لعدد الأطفال المشمولين بالمخصص", .DataType = "رقم"}
            }

            For Each setting In settings
                context.PayrollSettings.Add(setting)
            Next

            context.SaveChanges()
        End Sub

        Private Sub SeedDefaultAccounts(context As AccountingDbContext)
            ' إضافة الحسابات الأساسية
            Dim accounts() As ChartOfAccount = {
                New ChartOfAccount() With {.AccountCode = "1000", .AccountName = "الأصول", .AccountType = "أصول", .AccountNature = "مدين", .IsAnalytical = False},
                New ChartOfAccount() With {.AccountCode = "2000", .AccountName = "الخصوم", .AccountType = "خصوم", .AccountNature = "دائن", .IsAnalytical = False},
                New ChartOfAccount() With {.AccountCode = "3000", .AccountName = "حقوق الملكية", .AccountType = "حقوق ملكية", .AccountNature = "دائن", .IsAnalytical = False},
                New ChartOfAccount() With {.AccountCode = "4000", .AccountName = "الإيرادات", .AccountType = "إيرادات", .AccountNature = "دائن", .IsAnalytical = False},
                New ChartOfAccount() With {.AccountCode = "5000", .AccountName = "المصروفات", .AccountType = "مصروفات", .AccountNature = "مدين", .IsAnalytical = False}
            }

            For Each account In accounts
                context.ChartOfAccounts.Add(account)
            Next

            context.SaveChanges()
        End Sub

        Private Sub SeedOrganizationData(context As AccountingDbContext)
            ' إضافة بيانات المؤسسة الافتراضية
            Dim organization As New Organization() With {
                .OrganizationNumber = "001",
                .OrganizationName = "المؤسسة الافتراضية",
                .Address = "بغداد - العراق",
                .Email = "<EMAIL>",
                .CreatedBy = "System"
            }
            context.Organizations.Add(organization)

            context.SaveChanges()
        End Sub

    End Class

End Namespace
