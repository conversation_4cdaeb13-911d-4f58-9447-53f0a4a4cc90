Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models

    ''' <summary>
    ''' نموذج دليل العملات
    ''' </summary>
    <Table("Currencies")>
    Public Class Currency
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property CurrencyId As Integer

        <Required>
        <Display(Name:="رقم العملة")>
        Public Property CurrencyNumber As String

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم العملة")>
        Public Property CurrencyName As String

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز العملة")>
        Public Property CurrencyCode As String

        <StringLength(50)>
        <Display(Name:="أجزاء العملة")>
        Public Property CurrencySubunit As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Required>
        <StringLength(20)>
        <Display(Name:="نوع العملة")>
        Public Property CurrencyType As String ' محلية - أجنبية

        <Column(TypeName:="decimal(18,6)")>
        <Display(Name:="سعر الصرف")>
        Public Property ExchangeRate As Decimal = 1

        <Display(Name:="العملة الافتراضية")>
        Public Property IsDefault As Boolean = False

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property ChartOfAccounts As ICollection(Of ChartOfAccount)
        Public Overridable Property JournalEntries As ICollection(Of JournalEntry)

        Public Sub New()
            ChartOfAccounts = New HashSet(Of ChartOfAccount)()
            JournalEntries = New HashSet(Of JournalEntry)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج الحسابات الختامية
    ''' </summary>
    <Table("FinalAccounts")>
    Public Class FinalAccount
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property FinalAccountId As Integer

        <Required>
        <Display(Name:="رقم الحساب")>
        Public Property AccountNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الحساب")>
        Public Property AccountName As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property ChartOfAccounts As ICollection(Of ChartOfAccount)

        Public Sub New()
            ChartOfAccounts = New HashSet(Of ChartOfAccount)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج الفترة المحاسبية
    ''' </summary>
    <Table("AccountingPeriods")>
    Public Class AccountingPeriod
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property PeriodId As Integer

        <Required>
        <Display(Name:="رقم الفترة")>
        Public Property PeriodNumber As String

        <Required>
        <Display(Name:="السنة المالية")>
        Public Property FiscalYear As Integer

        <Display(Name:="الفترة الحالية")>
        Public Property IsCurrent As Boolean = False

        <Required>
        <Display(Name:="عدد الأشهر")>
        Public Property NumberOfMonths As Integer

        <Required>
        <Display(Name:="من شهر")>
        Public Property FromMonth As Integer

        <Required>
        <Display(Name:="إلى شهر")>
        Public Property ToMonth As Integer

        <Required>
        <Display(Name:="تاريخ البداية")>
        Public Property StartDate As DateTime

        <Required>
        <Display(Name:="تاريخ النهاية")>
        Public Property EndDate As DateTime

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="مغلقة")>
        Public Property IsClosed As Boolean = False

        <Display(Name:="تاريخ الإغلاق")>
        Public Property ClosedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المستخدم المغلق")>
        Public Property ClosedBy As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property JournalEntries As ICollection(Of JournalEntry)
        Public Overridable Property PayrollRecords As ICollection(Of PayrollRecord)

        Public Sub New()
            JournalEntries = New HashSet(Of JournalEntry)()
            PayrollRecords = New HashSet(Of PayrollRecord)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الحسابات
    ''' </summary>
    <Table("ChartOfAccounts")>
    Public Class ChartOfAccount
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property AccountId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="رقم الحساب")>
        Public Property AccountCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الحساب")>
        Public Property AccountName As String

        <Display(Name:="الحساب الأب")>
        Public Property ParentAccountId As Integer?

        <Required>
        <StringLength(20)>
        <Display(Name:="نوع الحساب")>
        Public Property AccountType As String ' أصول / خصوم / حقوق ملكية / إيرادات / مصروفات

        <Required>
        <StringLength(20)>
        <Display(Name:="طبيعة الحساب")>
        Public Property AccountNature As String ' مدين / دائن

        <Display(Name:="الحساب الختامي")>
        Public Property FinalAccountId As Integer?

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الرصيد الافتتاحي")>
        Public Property OpeningBalance As Decimal = 0

        <Display(Name:="حساب تحليلي")>
        Public Property IsAnalytical As Boolean = True

        <Display(Name:="حساب نقدي")>
        Public Property IsCash As Boolean = False

        <Display(Name:="حساب بنكي")>
        Public Property IsBank As Boolean = False

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("ParentAccountId")>
        Public Overridable Property ParentAccount As ChartOfAccount

        <ForeignKey("FinalAccountId")>
        Public Overridable Property FinalAccount As FinalAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        Public Overridable Property ChildAccounts As ICollection(Of ChartOfAccount)
        Public Overridable Property JournalEntryDetails As ICollection(Of JournalEntryDetail)
        Public Overridable Property BankAccounts As ICollection(Of BankAccount)

        Public Sub New()
            ChildAccounts = New HashSet(Of ChartOfAccount)()
            JournalEntryDetails = New HashSet(Of JournalEntryDetail)()
            BankAccounts = New HashSet(Of BankAccount)()
        End Sub

        ''' <summary>
        ''' الرصيد الحالي للحساب
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property CurrentBalance As Decimal
            Get
                ' يتم حساب الرصيد من القيود المحاسبية
                Return OpeningBalance ' + مجموع المدين - مجموع الدائن
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج دليل الصناديق
    ''' </summary>
    <Table("CashBoxes")>
    Public Class CashBox
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property CashBoxId As Integer

        <Required>
        <Display(Name:="رقم الصندوق")>
        Public Property CashBoxNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الصندوق")>
        Public Property CashBoxName As String

        <Display(Name:="الحساب المحاسبي")>
        Public Property AccountId As Integer?

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الرصيد الحالي")>
        Public Property CurrentBalance As Decimal = 0

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("AccountId")>
        Public Overridable Property Account As ChartOfAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        Public Overridable Property JournalEntries As ICollection(Of JournalEntry)

        Public Sub New()
            JournalEntries = New HashSet(Of JournalEntry)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل المصارف
    ''' </summary>
    <Table("Banks")>
    Public Class Bank
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property BankId As Integer

        <Required>
        <Display(Name:="رقم المصرف")>
        Public Property BankNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم المصرف")>
        Public Property BankName As String

        <StringLength(100)>
        <Display(Name:="رمز المصرف")>
        Public Property BankCode As String

        <StringLength(500)>
        <Display(Name:="العنوان")>
        Public Property Address As String

        <StringLength(50)>
        <Display(Name:="الهاتف")>
        Public Property Phone As String

        <StringLength(100)>
        <Display(Name:="البريد الإلكتروني")>
        Public Property Email As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property BankBranches As ICollection(Of BankBranch)

        Public Sub New()
            BankBranches = New HashSet(Of BankBranch)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل فروع المصارف
    ''' </summary>
    <Table("BankBranches")>
    Public Class BankBranch
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property BranchId As Integer

        <Required>
        <Display(Name:="رقم الفرع")>
        Public Property BranchNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الفرع")>
        Public Property BranchName As String

        <Required>
        <Display(Name:="المصرف")>
        Public Property BankId As Integer

        <StringLength(100)>
        <Display(Name:="الحساب البنكي")>
        Public Property BankAccountNumber As String

        <StringLength(500)>
        <Display(Name:="العنوان")>
        Public Property Address As String

        <StringLength(50)>
        <Display(Name:="الهاتف")>
        Public Property Phone As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("BankId")>
        Public Overridable Property Bank As Bank

        Public Overridable Property BankAccounts As ICollection(Of BankAccount)

        Public Sub New()
            BankAccounts = New HashSet(Of BankAccount)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الحسابات البنكية
    ''' </summary>
    <Table("BankAccounts")>
    Public Class BankAccount
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property BankAccountId As Integer

        <Required>
        <Display(Name:="رقم الحساب")>
        Public Property AccountNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الحساب البنكي")>
        Public Property AccountName As String

        <Required>
        <Display(Name:="الفرع")>
        Public Property BranchId As Integer

        <Display(Name:="الحساب المحاسبي")>
        Public Property ChartAccountId As Integer?

        <StringLength(50)>
        <Display(Name:="رقم الـ IBAN")>
        Public Property IBANNumber As String

        <StringLength(50)>
        <Display(Name:="نوع الحساب")>
        Public Property AccountType As String ' جاري / توفير / ودائع

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الرصيد الحالي")>
        Public Property CurrentBalance As Decimal = 0

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("BranchId")>
        Public Overridable Property BankBranch As BankBranch

        <ForeignKey("ChartAccountId")>
        Public Overridable Property ChartAccount As ChartOfAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        Public Overridable Property JournalEntries As ICollection(Of JournalEntry)
        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            JournalEntries = New HashSet(Of JournalEntry)()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج الأرصدة الافتتاحية
    ''' </summary>
    <Table("OpeningBalances")>
    Public Class OpeningBalance
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property OpeningBalanceId As Integer

        <Required>
        <Display(Name:="الحساب")>
        Public Property AccountId As Integer

        <Required>
        <Display(Name:="السنة المالية")>
        Public Property FiscalYear As Integer

        <Required>
        <Column(TypeName:="decimal(18,3)")>
        <Display(Name:="المبلغ المدين")>
        Public Property DebitAmount As Decimal

        <Required>
        <Column(TypeName:="decimal(18,3)")>
        <Display(Name:="المبلغ الدائن")>
        Public Property CreditAmount As Decimal

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        <Display(Name:="تاريخ التعديل")>
        Public Property ModifiedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المستخدم المعدل")>
        Public Property ModifiedBy As String

        ' خصائص التنقل
        <ForeignKey("AccountId")>
        Public Overridable Property Account As ChartOfAccount

    End Class

End Namespace
