Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models

    ''' <summary>
    ''' نموذج بيانات الموظفين
    ''' </summary>
    <Table("Employees")>
    Public Class Employee
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property EmployeeId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="الرقم الوظيفي")>
        Public Property EmployeeNumber As String

        <StringLength(50)>
        <Display(Name:="رقم الآيبان")>
        Public Property IBANNumber As String

        <Required>
        <StringLength(300)>
        <Display(Name:="الاسم الرباعي")>
        Public Property FullName As String

        <StringLength(10)>
        <Display(Name:="الجنس")>
        Public Property Gender As String ' ذكر / أنثى

        <StringLength(20)>
        <Display(Name:="الحالة الزوجية")>
        Public Property MaritalStatus As String ' أعزب / متزوج / مطلق / أرمل

        <Display(Name:="العنوان الوظيفي")>
        Public Property JobTitleId As Integer?

        <Display(Name:="الشهادة")>
        Public Property QualificationId As Integer?

        <Display(Name:="الدرجة الوظيفية")>
        Public Property JobGradeId As Integer?

        <Display(Name:="المرحلة")>
        Public Property StageId As Integer?

        <Display(Name:="تاريخ الميلاد")>
        Public Property BirthDate As DateTime?

        <Display(Name:="تاريخ التعيين")>
        Public Property HireDate As DateTime?

        <Required>
        <StringLength(20)>
        <Display(Name:="حالة الموظف")>
        Public Property EmployeeStatus As String ' مستمر / متقاعد / متوفي / نقل / متوقف

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer?

        <Display(Name:="القسم")>
        Public Property SectionId As Integer?

        <Display(Name:="الشعبة")>
        Public Property DivisionId As Integer?

        <Display(Name:="الحساب البنكي")>
        Public Property BankAccountId As Integer?

        <StringLength(500)>
        <Display(Name:="العنوان")>
        Public Property Address As String

        <StringLength(50)>
        <Display(Name:="رقم الهاتف")>
        Public Property Phone As String

        <StringLength(100)>
        <Display(Name:="البريد الإلكتروني")>
        Public Property Email As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <Display(Name:="تاريخ التحديث")>
        Public Property ModifiedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        <StringLength(100)>
        <Display(Name:="المستخدم المحدث")>
        Public Property ModifiedBy As String

        ' خصائص التنقل
        <ForeignKey("JobTitleId")>
        Public Overridable Property JobTitle As JobTitle

        <ForeignKey("QualificationId")>
        Public Overridable Property Qualification As Qualification

        <ForeignKey("JobGradeId")>
        Public Overridable Property JobGrade As JobGrade

        <ForeignKey("StageId")>
        Public Overridable Property Stage As Stage

        <ForeignKey("DepartmentId")>
        Public Overridable Property Department As Department

        <ForeignKey("SectionId")>
        Public Overridable Property Section As Section

        <ForeignKey("DivisionId")>
        Public Overridable Property Division As Division

        <ForeignKey("BankAccountId")>
        Public Overridable Property BankAccount As BankAccount

        Public Overridable Property PayrollRecords As ICollection(Of PayrollRecord)
        Public Overridable Property EmployeeAllowances As ICollection(Of EmployeeAllowance)
        Public Overridable Property EmployeeDeductions As ICollection(Of EmployeeDeduction)

        Public Sub New()
            PayrollRecords = New HashSet(Of PayrollRecord)()
            EmployeeAllowances = New HashSet(Of EmployeeAllowance)()
            EmployeeDeductions = New HashSet(Of EmployeeDeduction)()
        End Sub

        ''' <summary>
        ''' العمر بالسنوات
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property Age As Integer?
            Get
                If BirthDate.HasValue Then
                    Return DateTime.Now.Year - BirthDate.Value.Year
                End If
                Return Nothing
            End Get
        End Property

        ''' <summary>
        ''' سنوات الخدمة
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property ServiceYears As Integer?
            Get
                If HireDate.HasValue Then
                    Return DateTime.Now.Year - HireDate.Value.Year
                End If
                Return Nothing
            End Get
        End Property

        ''' <summary>
        ''' الاسم المختصر
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property ShortName As String
            Get
                If Not String.IsNullOrEmpty(FullName) Then
                    Dim names = FullName.Split(" "c)
                    If names.Length >= 2 Then
                        Return $"{names(0)} {names(1)}"
                    End If
                End If
                Return FullName
            End Get
        End Property

        ''' <summary>
        ''' معلومات الموظف المختصرة
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property EmployeeInfo As String
            Get
                Return $"{EmployeeNumber} - {ShortName}"
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج مخصصات الموظفين
    ''' </summary>
    <Table("EmployeeAllowances")>
    Public Class EmployeeAllowance
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property AllowanceId As Integer

        <Required>
        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="نوع المخصص")>
        Public Property AllowanceType As String

        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="النسبة")>
        Public Property Percentage As Decimal?

        <Display(Name:="تاريخ البداية")>
        Public Property StartDate As DateTime

        <Display(Name:="تاريخ النهاية")>
        Public Property EndDate As DateTime?

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee
    End Class

    ''' <summary>
    ''' نموذج استقطاعات الموظفين
    ''' </summary>
    <Table("EmployeeDeductions")>
    Public Class EmployeeDeduction
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DeductionId As Integer

        <Required>
        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="نوع الاستقطاع")>
        Public Property DeductionType As String

        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="النسبة")>
        Public Property Percentage As Decimal?

        <Display(Name:="تاريخ البداية")>
        Public Property StartDate As DateTime

        <Display(Name:="تاريخ النهاية")>
        Public Property EndDate As DateTime?

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee
    End Class

    ''' <summary>
    ''' نموذج إعدادات الرواتب
    ''' </summary>
    <Table("PayrollSettings")>
    Public Class PayrollSetting
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property SettingId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم الإعداد")>
        Public Property SettingName As String

        <Required>
        <StringLength(500)>
        <Display(Name:="قيمة الإعداد")>
        Public Property SettingValue As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <StringLength(50)>
        <Display(Name:="نوع البيانات")>
        Public Property DataType As String ' نص / رقم / منطقي / تاريخ

        <Display(Name:="تاريخ التحديث")>
        Public Property ModifiedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المحدث")>
        Public Property ModifiedBy As String

        ''' <summary>
        ''' الحصول على القيمة كرقم
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property NumericValue As Decimal?
            Get
                Dim result As Decimal
                If Decimal.TryParse(SettingValue, result) Then
                    Return result
                End If
                Return Nothing
            End Get
        End Property

        ''' <summary>
        ''' الحصول على القيمة كمنطقي
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property BooleanValue As Boolean?
            Get
                Dim result As Boolean
                If Boolean.TryParse(SettingValue, result) Then
                    Return result
                End If
                Return Nothing
            End Get
        End Property

        ''' <summary>
        ''' الحصول على القيمة كتاريخ
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property DateValue As DateTime?
            Get
                Dim result As DateTime
                If DateTime.TryParse(SettingValue, result) Then
                    Return result
                End If
                Return Nothing
            End Get
        End Property
    End Class

End Namespace
