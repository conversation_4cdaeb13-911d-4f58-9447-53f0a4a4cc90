Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models

    ''' <summary>
    ''' نموذج القيود اليومية
    ''' </summary>
    <Table("JournalEntries")>
    Public Class JournalEntry
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property EntryId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="رقم القيد")>
        Public Property EntryNumber As String

        <Required>
        <Display(Name:="تاريخ القيد")>
        Public Property EntryDate As DateTime

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <StringLength(50)>
        <Display(Name:="نوع القيد")>
        Public Property EntryType As String ' يومية / قبض / صرف / تسوية

        <StringLength(100)>
        <Display(Name:="المرجع")>
        Public Property Reference As String

        <Display(Name:="المصرف")>
        Public Property BankId As Integer?

        <Display(Name:="الفرع")>
        Public Property BranchId As Integer?

        <Display(Name:="الحساب البنكي")>
        Public Property BankAccountId As Integer?

        <Display(Name:="الصندوق")>
        Public Property CashBoxId As Integer?

        <Display(Name:="الفترة المحاسبية")>
        Public Property AccountingPeriodId As Integer?

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="إجمالي المبلغ")>
        Public Property TotalAmount As Decimal

        <Display(Name:="معتمد")>
        Public Property IsApproved As Boolean = False

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المعتمد من")>
        Public Property ApprovedBy As String

        <Display(Name:="مرحل")>
        Public Property IsPosted As Boolean = False

        <Display(Name:="تاريخ الترحيل")>
        Public Property PostedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المرحل من")>
        Public Property PostedBy As String

        <StringLength(500)>
        <Display(Name:="ملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("BankId")>
        Public Overridable Property Bank As Bank

        <ForeignKey("BranchId")>
        Public Overridable Property BankBranch As BankBranch

        <ForeignKey("BankAccountId")>
        Public Overridable Property BankAccount As BankAccount

        <ForeignKey("CashBoxId")>
        Public Overridable Property CashBox As CashBox

        <ForeignKey("AccountingPeriodId")>
        Public Overridable Property AccountingPeriod As AccountingPeriod

        Public Overridable Property JournalEntryDetails As ICollection(Of JournalEntryDetail)

        Public Sub New()
            JournalEntryDetails = New HashSet(Of JournalEntryDetail)()
        End Sub

        ''' <summary>
        ''' التحقق من توازن القيد
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property IsBalanced As Boolean
            Get
                If JournalEntryDetails IsNot Nothing Then
                    Dim totalDebit = JournalEntryDetails.Sum(Function(d) d.DebitAmount)
                    Dim totalCredit = JournalEntryDetails.Sum(Function(d) d.CreditAmount)
                    Return totalDebit = totalCredit
                End If
                Return False
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج تفاصيل القيود اليومية
    ''' </summary>
    <Table("JournalEntryDetails")>
    Public Class JournalEntryDetail
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DetailId As Integer

        <Required>
        <Display(Name:="القيد")>
        Public Property EntryId As Integer

        <Required>
        <Display(Name:="الحساب")>
        Public Property AccountId As Integer

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ المدين")>
        Public Property DebitAmount As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ الدائن")>
        Public Property CreditAmount As Decimal = 0

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,6)")>
        <Display(Name:="سعر الصرف")>
        Public Property ExchangeRate As Decimal = 1

        <StringLength(100)>
        <Display(Name:="المرجع")>
        Public Property Reference As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' خصائص التنقل
        <ForeignKey("EntryId")>
        Public Overridable Property JournalEntry As JournalEntry

        <ForeignKey("AccountId")>
        Public Overridable Property Account As ChartOfAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        ''' <summary>
        ''' المبلغ بالعملة المحلية
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property LocalAmount As Decimal
            Get
                Dim amount = If(DebitAmount > 0, DebitAmount, CreditAmount)
                Return amount * ExchangeRate
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج سندات الصرف
    ''' </summary>
    <Table("PaymentVouchers")>
    Public Class PaymentVoucher
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property VoucherId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="رقم السند")>
        Public Property VoucherNumber As String

        <Required>
        <Display(Name:="تاريخ السند")>
        Public Property VoucherDate As DateTime

        <Required>
        <StringLength(300)>
        <Display(Name:="المستفيد")>
        Public Property Beneficiary As String

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <Required>
        <Display(Name:="المصرف")>
        Public Property BankId As Integer

        <Required>
        <Display(Name:="الفرع")>
        Public Property BranchId As Integer

        <Required>
        <Display(Name:="الحساب البنكي")>
        Public Property BankAccountId As Integer

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,6)")>
        <Display(Name:="سعر الصرف")>
        Public Property ExchangeRate As Decimal = 1

        <StringLength(100)>
        <Display(Name:="رقم الشيك")>
        Public Property CheckNumber As String

        <Display(Name:="تاريخ الشيك")>
        Public Property CheckDate As DateTime?

        <StringLength(100)>
        <Display(Name:="طريقة الدفع")>
        Public Property PaymentMethod As String ' شيك / تحويل / نقد

        <Display(Name:="القيد المحاسبي")>
        Public Property JournalEntryId As Integer?

        <Display(Name:="معتمد")>
        Public Property IsApproved As Boolean = False

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المعتمد من")>
        Public Property ApprovedBy As String

        <Display(Name:="مدفوع")>
        Public Property IsPaid As Boolean = False

        <Display(Name:="تاريخ الدفع")>
        Public Property PaidDate As DateTime?

        <StringLength(500)>
        <Display(Name:="ملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("BankId")>
        Public Overridable Property Bank As Bank

        <ForeignKey("BranchId")>
        Public Overridable Property BankBranch As BankBranch

        <ForeignKey("BankAccountId")>
        Public Overridable Property BankAccount As BankAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        <ForeignKey("JournalEntryId")>
        Public Overridable Property JournalEntry As JournalEntry

        Public Overridable Property PaymentVoucherDetails As ICollection(Of PaymentVoucherDetail)

        Public Sub New()
            PaymentVoucherDetails = New HashSet(Of PaymentVoucherDetail)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج تفاصيل سندات الصرف
    ''' </summary>
    <Table("PaymentVoucherDetails")>
    Public Class PaymentVoucherDetail
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DetailId As Integer

        <Required>
        <Display(Name:="سند الصرف")>
        Public Property VoucherId As Integer

        <Required>
        <Display(Name:="الحساب")>
        Public Property AccountId As Integer

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <StringLength(100)>
        <Display(Name:="المرجع")>
        Public Property Reference As String

        ' خصائص التنقل
        <ForeignKey("VoucherId")>
        Public Overridable Property PaymentVoucher As PaymentVoucher

        <ForeignKey("AccountId")>
        Public Overridable Property Account As ChartOfAccount
    End Class

    ''' <summary>
    ''' نموذج سندات القبض
    ''' </summary>
    <Table("ReceiptVouchers")>
    Public Class ReceiptVoucher
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property VoucherId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="رقم السند")>
        Public Property VoucherNumber As String

        <Required>
        <Display(Name:="تاريخ السند")>
        Public Property VoucherDate As DateTime

        <Required>
        <StringLength(300)>
        <Display(Name:="المودع")>
        Public Property Depositor As String

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <Required>
        <Display(Name:="المصرف")>
        Public Property BankId As Integer

        <Required>
        <Display(Name:="الفرع")>
        Public Property BranchId As Integer

        <Required>
        <Display(Name:="الحساب البنكي")>
        Public Property BankAccountId As Integer

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <Display(Name:="العملة")>
        Public Property CurrencyId As Integer?

        <Column(TypeName:="decimal(18,6)")>
        <Display(Name:="سعر الصرف")>
        Public Property ExchangeRate As Decimal = 1

        <StringLength(100)>
        <Display(Name:="رقم الإيصال")>
        Public Property ReceiptNumber As String

        <Display(Name:="تاريخ الإيصال")>
        Public Property ReceiptDate As DateTime?

        <StringLength(100)>
        <Display(Name:="طريقة القبض")>
        Public Property ReceiptMethod As String ' شيك / تحويل / نقد

        <Display(Name:="القيد المحاسبي")>
        Public Property JournalEntryId As Integer?

        <Display(Name:="معتمد")>
        Public Property IsApproved As Boolean = False

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المعتمد من")>
        Public Property ApprovedBy As String

        <StringLength(500)>
        <Display(Name:="ملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("BankId")>
        Public Overridable Property Bank As Bank

        <ForeignKey("BranchId")>
        Public Overridable Property BankBranch As BankBranch

        <ForeignKey("BankAccountId")>
        Public Overridable Property BankAccount As BankAccount

        <ForeignKey("CurrencyId")>
        Public Overridable Property Currency As Currency

        <ForeignKey("JournalEntryId")>
        Public Overridable Property JournalEntry As JournalEntry

        Public Overridable Property ReceiptVoucherDetails As ICollection(Of ReceiptVoucherDetail)

        Public Sub New()
            ReceiptVoucherDetails = New HashSet(Of ReceiptVoucherDetail)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج تفاصيل سندات القبض
    ''' </summary>
    <Table("ReceiptVoucherDetails")>
    Public Class ReceiptVoucherDetail
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DetailId As Integer

        <Required>
        <Display(Name:="سند القبض")>
        Public Property VoucherId As Integer

        <Required>
        <Display(Name:="الحساب")>
        Public Property AccountId As Integer

        <Required>
        <StringLength(500)>
        <Display(Name:="البيان")>
        Public Property Description As String

        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <StringLength(100)>
        <Display(Name:="المرجع")>
        Public Property Reference As String

        ' خصائص التنقل
        <ForeignKey("VoucherId")>
        Public Overridable Property ReceiptVoucher As ReceiptVoucher

        <ForeignKey("AccountId")>
        Public Overridable Property Account As ChartOfAccount
    End Class

End Namespace
