Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models

    ''' <summary>
    ''' نموذج بيانات المؤسسة
    ''' </summary>
    <Table("Organizations")>
    Public Class Organization
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property OrganizationId As Integer

        <Required>
        <Display(Name:="رقم المؤسسة")>
        Public Property OrganizationNumber As String

        <Required>
        <StringLength(300)>
        <Display(Name:="اسم المؤسسة")>
        Public Property OrganizationName As String

        <StringLength(500)>
        <Display(Name:="العنوان")>
        Public Property Address As String

        <StringLength(100)>
        <Display(Name:="البريد الإلكتروني")>
        Public Property Email As String

        <StringLength(200)>
        <Display(Name:="الموقع الإلكتروني")>
        Public Property Website As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="شعار المؤسسة")>
        Public Property Logo As Byte()

        <StringLength(50)>
        <Display(Name:="رقم الهاتف")>
        Public Property Phone As String

        <StringLength(50)>
        <Display(Name:="رقم الفاكس")>
        Public Property Fax As String

        <StringLength(100)>
        <Display(Name:="الرقم الضريبي")>
        Public Property TaxNumber As String

        <StringLength(100)>
        <Display(Name:="رقم السجل التجاري")>
        Public Property CommercialRegister As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property Departments As ICollection(Of Department)

        Public Sub New()
            Departments = New HashSet(Of Department)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج بيانات الدائرة
    ''' </summary>
    <Table("Departments")>
    Public Class Department
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DepartmentId As Integer

        <Required>
        <Display(Name:="رقم الدائرة")>
        Public Property DepartmentNumber As String

        <Required>
        <Display(Name:="المؤسسة")>
        Public Property OrganizationId As Integer

        <Required>
        <StringLength(300)>
        <Display(Name:="اسم الدائرة")>
        Public Property DepartmentName As String

        <StringLength(500)>
        <Display(Name:="العنوان")>
        Public Property Address As String

        <StringLength(50)>
        <Display(Name:="رقم الهاتف")>
        Public Property Phone As String

        <StringLength(100)>
        <Display(Name:="البريد الإلكتروني")>
        Public Property Email As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("OrganizationId")>
        Public Overridable Property Organization As Organization

        Public Overridable Property Sections As ICollection(Of Section)
        Public Overridable Property Users As ICollection(Of User)
        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Sections = New HashSet(Of Section)()
            Users = New HashSet(Of User)()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الأقسام
    ''' </summary>
    <Table("Sections")>
    Public Class Section
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property SectionId As Integer

        <Required>
        <Display(Name:="رقم القسم")>
        Public Property SectionNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم القسم")>
        Public Property SectionName As String

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer?

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("DepartmentId")>
        Public Overridable Property Department As Department

        Public Overridable Property Divisions As ICollection(Of Division)
        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Divisions = New HashSet(Of Division)()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الشعب
    ''' </summary>
    <Table("Divisions")>
    Public Class Division
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DivisionId As Integer

        <Required>
        <Display(Name:="رقم الشعبة")>
        Public Property DivisionNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الشعبة")>
        Public Property DivisionName As String

        <Required>
        <Display(Name:="القسم المرتبط")>
        Public Property SectionId As Integer

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("SectionId")>
        Public Overridable Property Section As Section

        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل العناوين الوظيفية
    ''' </summary>
    <Table("JobTitles")>
    Public Class JobTitle
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property JobTitleId As Integer

        <Required>
        <Display(Name:="رقم العنوان الوظيفي")>
        Public Property JobTitleNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم العنوان الوظيفي")>
        Public Property JobTitleName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الراتب الأساسي")>
        Public Property BasicSalary As Decimal?

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الشهادات
    ''' </summary>
    <Table("Qualifications")>
    Public Class Qualification
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property QualificationId As Integer

        <Required>
        <Display(Name:="رقم الشهادة")>
        Public Property QualificationNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الشهادة")>
        Public Property QualificationName As String

        <StringLength(100)>
        <Display(Name:="المستوى")>
        Public Property Level As String

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصص الشهادة")>
        Public Property AllowanceAmount As Decimal?

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل الدرجات الوظيفية
    ''' </summary>
    <Table("JobGrades")>
    Public Class JobGrade
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property JobGradeId As Integer

        <Required>
        <Display(Name:="رقم الدرجة")>
        Public Property GradeNumber As String

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم الدرجة")>
        Public Property GradeName As String

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الراتب الأساسي")>
        Public Property BasicSalary As Decimal?

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property Employees As ICollection(Of Employee)
        Public Overridable Property Stages As ICollection(Of Stage)

        Public Sub New()
            Employees = New HashSet(Of Employee)()
            Stages = New HashSet(Of Stage)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج دليل المراحل
    ''' </summary>
    <Table("Stages")>
    Public Class Stage
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property StageId As Integer

        <Required>
        <Display(Name:="رقم المرحلة")>
        Public Property StageNumber As String

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم المرحلة")>
        Public Property StageName As String

        <Display(Name:="الدرجة الوظيفية")>
        Public Property JobGradeId As Integer?

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة الزيادة")>
        Public Property IncreasePercentage As Decimal?

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مبلغ الزيادة")>
        Public Property IncreaseAmount As Decimal?

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("JobGradeId")>
        Public Overridable Property JobGrade As JobGrade

        Public Overridable Property Employees As ICollection(Of Employee)

        Public Sub New()
            Employees = New HashSet(Of Employee)()
        End Sub
    End Class

End Namespace
