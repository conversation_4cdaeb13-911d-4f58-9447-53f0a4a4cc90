Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models

    ''' <summary>
    ''' نموذج سجلات الرواتب الشهرية
    ''' </summary>
    <Table("PayrollRecords")>
    Public Class PayrollRecord
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property PayrollId As Integer

        <Required>
        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Required>
        <Display(Name:="السنة")>
        Public Property PayrollYear As Integer

        <Required>
        <Display(Name:="الشهر")>
        Public Property PayrollMonth As Integer

        <Display(Name:="الفترة المحاسبية")>
        Public Property AccountingPeriodId As Integer?

        ' مكونات الراتب
        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="الراتب الجديد")>
        Public Property NewSalary As Decimal

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="حصة الدائرة في المساهمة الحكومية")>
        Public Property GovernmentContribution As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="فرق راتب")>
        Public Property SalaryDifference As Decimal = 0

        ' المخصصات
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات المنصب")>
        Public Property PositionAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الزوجية")>
        Public Property MarriageAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الأولاد")>
        Public Property ChildrenAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات هندسية")>
        Public Property EngineeringAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الشهادة")>
        Public Property QualificationAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الحرفة")>
        Public Property CraftAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الخطورة")>
        Public Property DangerAllowance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مخصصات الموقع الجغرافي")>
        Public Property LocationAllowance As Decimal = 0

        ' الاستقطاعات
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="صندوق تقاعد موظفي الدولة")>
        Public Property RetirementFund As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="حصة الدائرة في المساهمة الحكومية")>
        Public Property DepartmentContribution As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="ضريبة الدخل")>
        Public Property IncomeTax As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="صندوق هيئة الحماية الاجتماعية")>
        Public Property SocialSecurityFund As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="أمانات الضمان الصحي")>
        Public Property HealthInsurance As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="دوائر وجهات أخرى")>
        Public Property OtherDepartments As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="فرق راتب مستقطع")>
        Public Property DeductedSalaryDifference As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="دوائر التنفيذ")>
        Public Property ExecutionDepartments As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="أقساط المصارف")>
        Public Property BankInstallments As Decimal = 0

        <Display(Name:="تاريخ الاحتساب")>
        Public Property CalculatedDate As DateTime

        <Display(Name:="معتمد")>
        Public Property IsApproved As Boolean = False

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المعتمد من")>
        Public Property ApprovedBy As String

        <Display(Name:="مدفوع")>
        Public Property IsPaid As Boolean = False

        <Display(Name:="تاريخ الدفع")>
        Public Property PaidDate As DateTime?

        <StringLength(500)>
        <Display(Name:="ملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee

        <ForeignKey("AccountingPeriodId")>
        Public Overridable Property AccountingPeriod As AccountingPeriod

        Public Overridable Property PayrollDetails As ICollection(Of PayrollDetail)

        Public Sub New()
            PayrollDetails = New HashSet(Of PayrollDetail)()
        End Sub

        ''' <summary>
        ''' إجمالي المخصصات
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property TotalAllowances As Decimal
            Get
                Return PositionAllowance + MarriageAllowance + ChildrenAllowance +
                       EngineeringAllowance + QualificationAllowance + CraftAllowance +
                       DangerAllowance + LocationAllowance
            End Get
        End Property

        ''' <summary>
        ''' إجمالي الاستقطاعات
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property TotalDeductions As Decimal
            Get
                Return RetirementFund + DepartmentContribution + IncomeTax +
                       SocialSecurityFund + HealthInsurance + OtherDepartments +
                       DeductedSalaryDifference + ExecutionDepartments + BankInstallments
            End Get
        End Property

        ''' <summary>
        ''' إجمالي الراتب
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property GrossSalary As Decimal
            Get
                Return NewSalary + GovernmentContribution + SalaryDifference + TotalAllowances
            End Get
        End Property

        ''' <summary>
        ''' صافي الراتب
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property NetSalary As Decimal
            Get
                Return GrossSalary - TotalDeductions
            End Get
        End Property

        ''' <summary>
        ''' اسم الشهر والسنة
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property PeriodName As String
            Get
                Dim monthNames() As String = {
                    "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                    "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
                }
                If PayrollMonth >= 1 AndAlso PayrollMonth <= 12 Then
                    Return $"{monthNames(PayrollMonth)} {PayrollYear}"
                End If
                Return $"{PayrollMonth}/{PayrollYear}"
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج تفاصيل الرواتب
    ''' </summary>
    <Table("PayrollDetails")>
    Public Class PayrollDetail
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DetailId As Integer

        <Required>
        <Display(Name:="سجل الراتب")>
        Public Property PayrollId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="نوع العنصر")>
        Public Property ItemType As String ' مخصص / استقطاع

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم العنصر")>
        Public Property ItemName As String

        <Required>
        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="المبلغ")>
        Public Property Amount As Decimal

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="النسبة")>
        Public Property Percentage As Decimal?

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' خصائص التنقل
        <ForeignKey("PayrollId")>
        Public Overridable Property PayrollRecord As PayrollRecord
    End Class

    ''' <summary>
    ''' نموذج قوالب الرواتب
    ''' </summary>
    <Table("PayrollTemplates")>
    Public Class PayrollTemplate
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property TemplateId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم القالب")>
        Public Property TemplateName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer?

        <Display(Name:="القسم")>
        Public Property SectionId As Integer?

        <Display(Name:="الدرجة الوظيفية")>
        Public Property JobGradeId As Integer?

        ' إعدادات المخصصات الافتراضية
        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة مخصص المنصب")>
        Public Property PositionAllowanceRate As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مبلغ مخصص الزوجية")>
        Public Property MarriageAllowanceAmount As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="مبلغ مخصص الطفل الواحد")>
        Public Property ChildAllowanceAmount As Decimal = 0

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة المخصص الهندسي")>
        Public Property EngineeringAllowanceRate As Decimal = 0

        ' إعدادات الاستقطاعات الافتراضية
        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة صندوق التقاعد")>
        Public Property RetirementFundRate As Decimal = 10

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة مساهمة الدائرة")>
        Public Property DepartmentContributionRate As Decimal = 15

        <Column(TypeName:="decimal(5,2)")>
        <Display(Name:="نسبة ضريبة الدخل")>
        Public Property IncomeTaxRate As Decimal = 0

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("DepartmentId")>
        Public Overridable Property Department As Department

        <ForeignKey("SectionId")>
        Public Overridable Property Section As Section

        <ForeignKey("JobGradeId")>
        Public Overridable Property JobGrade As JobGrade
    End Class

    ''' <summary>
    ''' نموذج دفعات الرواتب
    ''' </summary>
    <Table("PayrollBatches")>
    Public Class PayrollBatch
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property BatchId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="رقم الدفعة")>
        Public Property BatchNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الدفعة")>
        Public Property BatchName As String

        <Required>
        <Display(Name:="السنة")>
        Public Property PayrollYear As Integer

        <Required>
        <Display(Name:="الشهر")>
        Public Property PayrollMonth As Integer

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer?

        <Display(Name:="القسم")>
        Public Property SectionId As Integer?

        <Display(Name:="عدد الموظفين")>
        Public Property EmployeeCount As Integer = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="إجمالي الرواتب")>
        Public Property TotalGrossSalary As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="إجمالي الاستقطاعات")>
        Public Property TotalDeductions As Decimal = 0

        <Column(TypeName:="decimal(18,2)")>
        <Display(Name:="إجمالي صافي الرواتب")>
        Public Property TotalNetSalary As Decimal = 0

        <Display(Name:="حالة الدفعة")>
        Public Property BatchStatus As String ' قيد الإعداد / معتمدة / مدفوعة / ملغاة

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المعتمد من")>
        Public Property ApprovedBy As String

        <Display(Name:="تاريخ الدفع")>
        Public Property PaidDate As DateTime?

        <StringLength(100)>
        <Display(Name:="المدفوع من")>
        Public Property PaidBy As String

        <StringLength(500)>
        <Display(Name:="ملاحظات")>
        Public Property Notes As String

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        <ForeignKey("DepartmentId")>
        Public Overridable Property Department As Department

        <ForeignKey("SectionId")>
        Public Overridable Property Section As Section

        Public Overridable Property PayrollRecords As ICollection(Of PayrollRecord)

        Public Sub New()
            PayrollRecords = New HashSet(Of PayrollRecord)()
        End Sub
    End Class

End Namespace
