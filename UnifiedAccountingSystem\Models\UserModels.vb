Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema
Imports System.Security.Cryptography
Imports System.Text

Namespace Models

    ''' <summary>
    ''' نموذج مجموعة المستخدمين
    ''' </summary>
    <Table("UserGroups")>
    Public Class UserGroup
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property GroupId As Integer

        <Required>
        <Display(Name:="رقم المجموعة")>
        Public Property GroupNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم المجموعة")>
        Public Property GroupName As String

        <StringLength(500)>
        <Display(Name:="الملاحظات")>
        Public Property Notes As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        ' خصائص التنقل
        Public Overridable Property Users As ICollection(Of User)

        Public Sub New()
            Users = New HashSet(Of User)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج حسابات المستخدمين
    ''' </summary>
    <Table("Users")>
    Public Class User
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property UserId As Integer

        <Required>
        <Display(Name:="رقم الحساب")>
        Public Property AccountNumber As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الحساب")>
        Public Property AccountName As String

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم المستخدم")>
        Public Property Username As String

        <Required>
        <StringLength(500)>
        <Display(Name:="كلمة المرور المشفرة")>
        Public Property PasswordHash As String

        <Required>
        <StringLength(50)>
        <Display(Name:="نوع الحساب")>
        Public Property AccountType As String ' مدير - مستخدم

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer?

        <Display(Name:="مجموعة المستخدمين")>
        Public Property UserGroupId As Integer?

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="آخر تسجيل دخول")>
        Public Property LastLoginDate As DateTime?

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        <Display(Name:="تاريخ التحديث")>
        Public Property ModifiedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المنشئ")>
        Public Property CreatedBy As String

        <StringLength(100)>
        <Display(Name:="المستخدم المحدث")>
        Public Property ModifiedBy As String

        ' خصائص التنقل
        <ForeignKey("UserGroupId")>
        Public Overridable Property UserGroup As UserGroup

        <ForeignKey("DepartmentId")>
        Public Overridable Property Department As Department

        Public Overridable Property UserPermissions As ICollection(Of UserPermission)
        Public Overridable Property AuditLogs As ICollection(Of AuditLog)

        Public Sub New()
            UserPermissions = New HashSet(Of UserPermission)()
            AuditLogs = New HashSet(Of AuditLog)()
        End Sub

        ''' <summary>
        ''' تشفير كلمة المرور
        ''' </summary>
        Public Shared Function HashPassword(password As String) As String
            Using sha256Hash As SHA256 = SHA256.Create()
                Dim bytes As Byte() = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password))
                Dim builder As New StringBuilder()
                For i As Integer = 0 To bytes.Length - 1
                    builder.Append(bytes(i).ToString("x2"))
                Next
                Return builder.ToString()
            End Using
        End Function

        ''' <summary>
        ''' التحقق من كلمة المرور
        ''' </summary>
        Public Function VerifyPassword(password As String) As Boolean
            Dim hashOfInput As String = HashPassword(password)
            Return String.Equals(PasswordHash, hashOfInput, StringComparison.OrdinalIgnoreCase)
        End Function

        ''' <summary>
        ''' التحقق من كون المستخدم مدير
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property IsAdmin As Boolean
            Get
                Return AccountType = "مدير"
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نموذج الصلاحيات
    ''' </summary>
    <Table("Permissions")>
    Public Class Permission
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property PermissionId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم الصلاحية")>
        Public Property PermissionName As String

        <Required>
        <StringLength(100)>
        <Display(Name:="رمز الصلاحية")>
        Public Property PermissionCode As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <StringLength(100)>
        <Display(Name:="الوحدة")>
        Public Property ModuleName As String

        <Display(Name:="حالة النشاط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' خصائص التنقل
        Public Overridable Property UserPermissions As ICollection(Of UserPermission)

        Public Sub New()
            UserPermissions = New HashSet(Of UserPermission)()
        End Sub
    End Class

    ''' <summary>
    ''' نموذج صلاحيات المستخدمين
    ''' </summary>
    <Table("UserPermissions")>
    Public Class UserPermission
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property Id As Integer

        <Required>
        <Display(Name:="المستخدم")>
        Public Property UserId As Integer

        <Required>
        <Display(Name:="الصلاحية")>
        Public Property PermissionId As Integer

        <Display(Name:="إضافة")>
        Public Property CanAdd As Boolean = False

        <Display(Name:="تعديل")>
        Public Property CanEdit As Boolean = False

        <Display(Name:="حذف")>
        Public Property CanDelete As Boolean = False

        <Display(Name:="عرض")>
        Public Property CanView As Boolean = True

        <Display(Name:="طباعة")>
        Public Property CanPrint As Boolean = False

        <Display(Name:="تصدير")>
        Public Property CanExport As Boolean = False

        <Display(Name:="تاريخ الإضافة")>
        Public Property AssignedDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="المستخدم المضيف")>
        Public Property AssignedBy As String

        ' خصائص التنقل
        <ForeignKey("UserId")>
        Public Overridable Property User As User

        <ForeignKey("PermissionId")>
        Public Overridable Property Permission As Permission
    End Class

    ''' <summary>
    ''' نموذج سجل العمليات
    ''' </summary>
    <Table("AuditLogs")>
    Public Class AuditLog
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property LogId As Integer

        <Required>
        <Display(Name:="المستخدم")>
        Public Property UserId As Integer

        <Required>
        <StringLength(100)>
        <Display(Name:="نوع العملية")>
        Public Property ActionType As String ' إضافة / تعديل / حذف / عرض / طباعة

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم الجدول")>
        Public Property TableName As String

        <StringLength(100)>
        <Display(Name:="معرف السجل")>
        Public Property RecordId As String

        <StringLength(2000)>
        <Display(Name:="القيم القديمة")>
        Public Property OldValues As String

        <StringLength(2000)>
        <Display(Name:="القيم الجديدة")>
        Public Property NewValues As String

        <Required>
        <Display(Name:="تاريخ العملية")>
        Public Property ActionDate As DateTime = DateTime.Now

        <StringLength(100)>
        <Display(Name:="عنوان IP")>
        Public Property IPAddress As String

        <StringLength(500)>
        <Display(Name:="تفاصيل إضافية")>
        Public Property AdditionalInfo As String

        ' خصائص التنقل
        <ForeignKey("UserId")>
        Public Overridable Property User As User
    End Class

    ''' <summary>
    ''' نموذج جلسات المستخدمين
    ''' </summary>
    <Table("UserSessions")>
    Public Class UserSession
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property SessionId As Integer

        <Required>
        <Display(Name:="المستخدم")>
        Public Property UserId As Integer

        <Required>
        <StringLength(500)>
        <Display(Name:="رمز الجلسة")>
        Public Property SessionToken As String

        <Required>
        <Display(Name:="تاريخ البداية")>
        Public Property StartTime As DateTime = DateTime.Now

        <Display(Name:="تاريخ النهاية")>
        Public Property EndTime As DateTime?

        <Display(Name:="نشطة")>
        Public Property IsActive As Boolean = True

        <StringLength(100)>
        <Display(Name:="عنوان IP")>
        Public Property IPAddress As String

        <StringLength(500)>
        <Display(Name:="معلومات المتصفح")>
        Public Property UserAgent As String

        ' خصائص التنقل
        <ForeignKey("UserId")>
        Public Overridable Property User As User

        ''' <summary>
        ''' مدة الجلسة بالدقائق
        ''' </summary>
        <NotMapped>
        Public ReadOnly Property DurationMinutes As Integer
            Get
                Dim endTime As DateTime = If(Me.EndTime, DateTime.Now)
                Return CInt((endTime - StartTime).TotalMinutes)
            End Get
        End Property
    End Class

End Namespace
