Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة المحاسبة العامة
    ''' </summary>
    Public Class AccountingService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' الحصول على الأرصدة الافتتاحية
        ''' </summary>
        ''' <returns>قائمة الأرصدة الافتتاحية</returns>
        Public Async Function GetOpeningBalancesAsync() As Task(Of List(Of OpeningBalance))
            Try
                Return Await _context.OpeningBalances.Include(Function(ob) ob.Account).ToListAsync()
            Catch ex As Exception
                Return New List(Of OpeningBalance)()
            End Try
        End Function

        ''' <summary>
        ''' إضافة رصيد افتتاحي
        ''' </summary>
        ''' <param name="openingBalance">الرصيد الافتتاحي</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddOpeningBalanceAsync(openingBalance As OpeningBalance) As Task(Of Boolean)
            Try
                openingBalance.CreatedDate = DateTime.Now
                openingBalance.CreatedBy = CurrentUserService.CurrentUserName

                _context.OpeningBalances.Add(openingBalance)
                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
