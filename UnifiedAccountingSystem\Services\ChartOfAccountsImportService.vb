Imports System.Data.Entity
Imports System.IO
Imports OfficeOpenXml
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة استيراد دليل الحسابات من Excel
    ''' </summary>
    Public Class ChartOfAccountsImportService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' استيراد دليل الحسابات من ملف Excel
        ''' </summary>
        ''' <param name="filePath">مسار الملف</param>
        ''' <returns>نتيجة الاستيراد</returns>
        Public Async Function ImportFromExcelAsync(filePath As String) As Task(Of ImportResult)
            Dim result As New ImportResult()

            Try
                If Not File.Exists(filePath) Then
                    result.Success = False
                    result.ErrorMessage = "الملف غير موجود"
                    Return result
                End If

                Using package As New ExcelPackage(New FileInfo(filePath))
                    Dim worksheet = package.Workbook.Worksheets.FirstOrDefault()
                    If worksheet Is Nothing Then
                        result.Success = False
                        result.ErrorMessage = "لا توجد أوراق عمل في الملف"
                        Return result
                    End If

                    ' قراءة البيانات من Excel
                    Dim accounts = ReadAccountsFromWorksheet(worksheet)
                    
                    ' التحقق من صحة البيانات
                    Dim validationResult = ValidateAccounts(accounts)
                    If Not validationResult.IsValid Then
                        result.Success = False
                        result.ErrorMessage = validationResult.ErrorMessage
                        Return result
                    End If

                    ' حفظ الحسابات في قاعدة البيانات
                    Await SaveAccountsToDatabase(accounts, result)

                End Using

            Catch ex As Exception
                result.Success = False
                result.ErrorMessage = $"خطأ في استيراد الملف: {ex.Message}"
            End Try

            Return result
        End Function

        ''' <summary>
        ''' قراءة الحسابات من ورقة العمل
        ''' </summary>
        ''' <param name="worksheet">ورقة العمل</param>
        ''' <returns>قائمة الحسابات</returns>
        Private Function ReadAccountsFromWorksheet(worksheet As ExcelWorksheet) As List(Of ChartOfAccountImportModel)
            Dim accounts As New List(Of ChartOfAccountImportModel)()
            Dim rowCount = worksheet.Dimension?.Rows

            If Not rowCount.HasValue OrElse rowCount.Value < 2 Then
                Return accounts
            End If

            ' قراءة البيانات من الصف الثاني (تجاهل العناوين)
            For row As Integer = 2 To rowCount.Value
                Try
                    Dim account As New ChartOfAccountImportModel() With {
                        .Level1 = GetCellValue(worksheet, row, 1),
                        .Level2 = GetCellValue(worksheet, row, 2),
                        .Level3 = GetCellValue(worksheet, row, 3),
                        .Level4 = GetCellValue(worksheet, row, 4),
                        .Level5 = GetCellValue(worksheet, row, 5),
                        .Level6 = GetCellValue(worksheet, row, 6),
                        .AccountName = GetCellValue(worksheet, row, 7),
                        .AccountType = GetCellValue(worksheet, row, 8),
                        .Notes = GetCellValue(worksheet, row, 9)
                    }

                    ' تكوين رمز الحساب
                    account.AccountCode = BuildAccountCode(account)

                    ' تحديد طبيعة الحساب
                    account.AccountNature = DetermineAccountNature(account.AccountCode, account.AccountType)

                    ' تحديد ما إذا كان الحساب تحليلي
                    account.IsAnalytical = DetermineIfAnalytical(account)

                    If Not String.IsNullOrEmpty(account.AccountCode) AndAlso Not String.IsNullOrEmpty(account.AccountName) Then
                        accounts.Add(account)
                    End If

                Catch ex As Exception
                    ' تجاهل الصفوف التي تحتوي على أخطاء
                    Continue For
                End Try
            Next

            Return accounts
        End Function

        ''' <summary>
        ''' الحصول على قيمة الخلية
        ''' </summary>
        ''' <param name="worksheet">ورقة العمل</param>
        ''' <param name="row">الصف</param>
        ''' <param name="col">العمود</param>
        ''' <returns>قيمة الخلية</returns>
        Private Function GetCellValue(worksheet As ExcelWorksheet, row As Integer, col As Integer) As String
            Try
                Dim cellValue = worksheet.Cells(row, col).Value
                Return If(cellValue?.ToString()?.Trim(), String.Empty)
            Catch
                Return String.Empty
            End Try
        End Function

        ''' <summary>
        ''' بناء رمز الحساب من المستويات
        ''' </summary>
        ''' <param name="account">نموذج الحساب</param>
        ''' <returns>رمز الحساب</returns>
        Private Function BuildAccountCode(account As ChartOfAccountImportModel) As String
            Dim codeParts As New List(Of String)()

            If Not String.IsNullOrEmpty(account.Level1) Then codeParts.Add(account.Level1)
            If Not String.IsNullOrEmpty(account.Level2) Then codeParts.Add(account.Level2)
            If Not String.IsNullOrEmpty(account.Level3) Then codeParts.Add(account.Level3)
            If Not String.IsNullOrEmpty(account.Level4) Then codeParts.Add(account.Level4)
            If Not String.IsNullOrEmpty(account.Level5) Then codeParts.Add(account.Level5)
            If Not String.IsNullOrEmpty(account.Level6) Then codeParts.Add(account.Level6)

            Return String.Join("", codeParts)
        End Function

        ''' <summary>
        ''' تحديد طبيعة الحساب
        ''' </summary>
        ''' <param name="accountCode">رمز الحساب</param>
        ''' <param name="accountType">نوع الحساب</param>
        ''' <returns>طبيعة الحساب</returns>
        Private Function DetermineAccountNature(accountCode As String, accountType As String) As String
            If String.IsNullOrEmpty(accountCode) Then Return "مدين"

            Dim firstDigit = accountCode.Substring(0, 1)

            Select Case firstDigit
                Case "1" ' الإيرادات النهائية
                    Return "دائن"
                Case "2" ' المصرف النهائي (النفقات)
                    Return "مدين"
                Case "3" ' الموجودات المالية
                    Return "مدين"
                Case "4" ' المطلوبات المالية
                    Return "دائن"
                Case Else
                    ' تحديد حسب نوع الحساب
                    If accountType.Contains("إيراد") OrElse accountType.Contains("دائن") Then
                        Return "دائن"
                    Else
                        Return "مدين"
                    End If
            End Select
        End Function

        ''' <summary>
        ''' تحديد ما إذا كان الحساب تحليلي
        ''' </summary>
        ''' <param name="account">نموذج الحساب</param>
        ''' <returns>True إذا كان تحليلي</returns>
        Private Function DetermineIfAnalytical(account As ChartOfAccountImportModel) As Boolean
            ' الحساب تحليلي إذا كان له مستويات فرعية أو كان في المستوى الأخير
            Return Not String.IsNullOrEmpty(account.Level4) OrElse 
                   Not String.IsNullOrEmpty(account.Level5) OrElse 
                   Not String.IsNullOrEmpty(account.Level6)
        End Function

        ''' <summary>
        ''' التحقق من صحة الحسابات
        ''' </summary>
        ''' <param name="accounts">قائمة الحسابات</param>
        ''' <returns>نتيجة التحقق</returns>
        Private Function ValidateAccounts(accounts As List(Of ChartOfAccountImportModel)) As ValidationResult
            Dim result As New ValidationResult() With {.IsValid = True}

            If accounts.Count = 0 Then
                result.IsValid = False
                result.ErrorMessage = "لا توجد حسابات صالحة في الملف"
                Return result
            End If

            ' التحقق من عدم تكرار رموز الحسابات
            Dim duplicateCodes = accounts.GroupBy(Function(a) a.AccountCode).Where(Function(g) g.Count() > 1).Select(Function(g) g.Key).ToList()
            If duplicateCodes.Any() Then
                result.IsValid = False
                result.ErrorMessage = $"رموز الحسابات التالية مكررة: {String.Join(", ", duplicateCodes)}"
                Return result
            End If

            Return result
        End Function

        ''' <summary>
        ''' حفظ الحسابات في قاعدة البيانات
        ''' </summary>
        ''' <param name="accounts">قائمة الحسابات</param>
        ''' <param name="result">نتيجة الاستيراد</param>
        Private Async Function SaveAccountsToDatabase(accounts As List(Of ChartOfAccountImportModel), result As ImportResult) As Task
            Try
                ' ترتيب الحسابات حسب المستوى (الحسابات الرئيسية أولاً)
                Dim sortedAccounts = accounts.OrderBy(Function(a) a.AccountCode.Length).ThenBy(Function(a) a.AccountCode).ToList()

                For Each importAccount In sortedAccounts
                    ' التحقق من عدم وجود الحساب مسبقاً
                    Dim existingAccount = Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountCode = importAccount.AccountCode)
                    
                    If existingAccount Is Nothing Then
                        ' إنشاء حساب جديد
                        Dim newAccount As New ChartOfAccount() With {
                            .AccountCode = importAccount.AccountCode,
                            .AccountName = importAccount.AccountName,
                            .AccountType = If(String.IsNullOrEmpty(importAccount.AccountType), "عام", importAccount.AccountType),
                            .AccountNature = importAccount.AccountNature,
                            .IsAnalytical = importAccount.IsAnalytical,
                            .IsActive = True,
                            .CreatedDate = DateTime.Now,
                            .CreatedBy = CurrentUserService.CurrentUserName,
                            .Notes = importAccount.Notes
                        }

                        ' تحديد الحساب الأب
                        newAccount.ParentAccountId = Await FindParentAccountId(importAccount.AccountCode)

                        _context.ChartOfAccounts.Add(newAccount)
                        result.SuccessCount += 1
                    Else
                        result.SkippedCount += 1
                    End If
                Next

                Await _context.SaveChangesAsync()
                result.Success = True

            Catch ex As Exception
                result.Success = False
                result.ErrorMessage = $"خطأ في حفظ البيانات: {ex.Message}"
            End Try
        End Function

        ''' <summary>
        ''' البحث عن معرف الحساب الأب
        ''' </summary>
        ''' <param name="accountCode">رمز الحساب</param>
        ''' <returns>معرف الحساب الأب</returns>
        Private Async Function FindParentAccountId(accountCode As String) As Task(Of Integer?)
            If String.IsNullOrEmpty(accountCode) OrElse accountCode.Length <= 1 Then
                Return Nothing
            End If

            ' البحث عن الحساب الأب بإزالة آخر رقم
            For i As Integer = accountCode.Length - 1 To 1 Step -1
                Dim parentCode = accountCode.Substring(0, i)
                Dim parentAccount = Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountCode = parentCode)
                If parentAccount IsNot Nothing Then
                    Return parentAccount.AccountId
                End If
            Next

            Return Nothing
        End Function

        ''' <summary>
        ''' إنشاء ملف Excel نموذجي
        ''' </summary>
        ''' <param name="filePath">مسار الملف</param>
        ''' <returns>True إذا تم الإنشاء بنجاح</returns>
        Public Function CreateSampleExcelFile(filePath As String) As Boolean
            Try
                Using package As New ExcelPackage()
                    Dim worksheet = package.Workbook.Worksheets.Add("دليل الحسابات")

                    ' إضافة العناوين
                    worksheet.Cells(1, 1).Value = "المستوى 1"
                    worksheet.Cells(1, 2).Value = "المستوى 2"
                    worksheet.Cells(1, 3).Value = "المستوى 3"
                    worksheet.Cells(1, 4).Value = "المستوى 4"
                    worksheet.Cells(1, 5).Value = "المستوى 5"
                    worksheet.Cells(1, 6).Value = "المستوى 6"
                    worksheet.Cells(1, 7).Value = "اسم الحساب"
                    worksheet.Cells(1, 8).Value = "نوع الحساب"
                    worksheet.Cells(1, 9).Value = "ملاحظات"

                    ' تنسيق العناوين
                    Using range = worksheet.Cells(1, 1, 1, 9)
                        range.Style.Font.Bold = True
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue)
                    End Using

                    ' إضافة بيانات نموذجية
                    AddSampleData(worksheet)

                    ' حفظ الملف
                    package.SaveAs(New FileInfo(filePath))
                End Using

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إضافة بيانات نموذجية
        ''' </summary>
        ''' <param name="worksheet">ورقة العمل</param>
        Private Sub AddSampleData(worksheet As ExcelWorksheet)
            ' بيانات نموذجية من ميزان المراجعة
            Dim sampleData() As Object = {
                {1, "", "", "", "", "", "الإيرادات النهائية", "إيرادات", ""},
                {2, "", "", "", "", "", "المصرف النهائي", "نفقات", ""},
                {3, "", "", "", "", "", "الموجودات المالية", "موجودات", ""},
                {3, 1, "", "", "", "", "الموجودات المحلية", "موجودات", ""},
                {3, 1, 1, "", "", "", "عملة الودائع (نقد)", "موجودات", ""},
                {3, 1, 1, 1, "", "", "نقد في الصندوق", "موجودات", ""},
                {3, 1, 2, "", "", "", "نقد في المصارف", "موجودات", ""},
                {3, 1, 2, 1, "", "", "بنك النفقات الاعتيادي/ التشغيلية", "موجودات", ""},
                {3, 1, 2, 1, 1, "", "بنك النفقات الاعتيادي/ التشغيلية", "موجودات", ""},
                {3, 2, "", "", "", "", "حسابات دائنة أخرى", "موجودات", ""},
                {3, 2, 1, "", "", "", "السلف النقدية", "موجودات", ""},
                {3, 2, 1, 1, "", "", "السلف المؤقتة", "موجودات", ""},
                {3, 2, 1, 2, "", "", "سلف اللجان", "موجودات", ""},
                {4, "", "", "", "", "", "المطلوبات المالية", "مطلوبات", ""},
                {4, 1, "", "", "", "", "المطلوبات المحلية", "مطلوبات", ""},
                {4, 1, 2, 1, "", "", "الرصيد النقدي المدور", "مطلوبات", ""},
                {4, 2, "", "", "", "", "الحسابات الدائنة الأخرى", "مطلوبات", ""},
                {4, 2, 1, "", "", "", "أمانات ضمان تحصيل الإيرادات", "مطلوبات", ""},
                {4, 2, 1, 9, "", "", "أمانات أخرى", "مطلوبات", ""},
                {4, 3, "", "", "", "", "حسابات المطلوبات النظامية", "مطلوبات", ""}
            }

            For i As Integer = 0 To sampleData.GetLength(0) - 1
                For j As Integer = 0 To sampleData.GetLength(1) - 1
                    worksheet.Cells(i + 2, j + 1).Value = sampleData(i, j)
                Next
            Next
        End Sub

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' نموذج استيراد الحساب
    ''' </summary>
    Public Class ChartOfAccountImportModel
        Public Property Level1 As String
        Public Property Level2 As String
        Public Property Level3 As String
        Public Property Level4 As String
        Public Property Level5 As String
        Public Property Level6 As String
        Public Property AccountCode As String
        Public Property AccountName As String
        Public Property AccountType As String
        Public Property AccountNature As String
        Public Property IsAnalytical As Boolean
        Public Property Notes As String
    End Class

    ''' <summary>
    ''' نتيجة الاستيراد
    ''' </summary>
    Public Class ImportResult
        Public Property Success As Boolean
        Public Property ErrorMessage As String
        Public Property SuccessCount As Integer
        Public Property SkippedCount As Integer
        Public Property TotalCount As Integer
            Get
                Return SuccessCount + SkippedCount
            End Get
        End Property
    End Class

    ''' <summary>
    ''' نتيجة التحقق
    ''' </summary>
    Public Class ValidationResult
        Public Property IsValid As Boolean
        Public Property ErrorMessage As String
    End Class

End Namespace
