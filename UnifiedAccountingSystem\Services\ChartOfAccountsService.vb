Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة إدارة دليل الحسابات
    ''' </summary>
    Public Class ChartOfAccountsService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' الحصول على جميع الحسابات
        ''' </summary>
        ''' <returns>قائمة الحسابات</returns>
        Public Async Function GetAllAccountsAsync() As Task(Of List(Of ChartOfAccount))
            Try
                Return Await _context.ChartOfAccounts.OrderBy(Function(ca) ca.AccountCode).ToListAsync()
            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على الحسابات النشطة فقط
        ''' </summary>
        ''' <returns>قائمة الحسابات النشطة</returns>
        Public Async Function GetActiveAccountsAsync() As Task(Of List(Of ChartOfAccount))
            Try
                Return Await _context.ChartOfAccounts.Where(Function(ca) ca.IsActive).OrderBy(Function(ca) ca.AccountCode).ToListAsync()
            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على حساب بالمعرف
        ''' </summary>
        ''' <param name="accountId">معرف الحساب</param>
        ''' <returns>الحساب</returns>
        Public Async Function GetAccountByIdAsync(accountId As Integer) As Task(Of ChartOfAccount)
            Try
                Return Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountId = accountId)
            Catch ex As Exception
                Return Nothing
            End Try
        End Function

        ''' <summary>
        ''' إضافة حساب جديد
        ''' </summary>
        ''' <param name="account">الحساب الجديد</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddAccountAsync(account As ChartOfAccount) As Task(Of Boolean)
            Try
                ' التحقق من عدم تكرار رمز الحساب
                Dim existingAccount = Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountCode = account.AccountCode)
                If existingAccount IsNot Nothing Then
                    Return False
                End If

                ' تحديد طبيعة الحساب تلقائياً إذا لم تكن محددة
                If String.IsNullOrEmpty(account.AccountNature) Then
                    account.AccountNature = DetermineAccountNature(account.AccountCode, account.AccountType)
                End If

                account.CreatedDate = DateTime.Now
                account.CreatedBy = CurrentUserService.CurrentUserName

                _context.ChartOfAccounts.Add(account)
                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث حساب موجود
        ''' </summary>
        ''' <param name="account">الحساب المحدث</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateAccountAsync(account As ChartOfAccount) As Task(Of Boolean)
            Try
                Dim existingAccount = Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountId = account.AccountId)
                If existingAccount Is Nothing Then
                    Return False
                End If

                ' تحديث البيانات
                existingAccount.AccountName = account.AccountName
                existingAccount.AccountType = account.AccountType
                existingAccount.AccountNature = account.AccountNature
                existingAccount.IsAnalytical = account.IsAnalytical
                existingAccount.IsActive = account.IsActive
                existingAccount.ParentAccountId = account.ParentAccountId
                existingAccount.Notes = account.Notes
                existingAccount.ModifiedDate = DateTime.Now
                existingAccount.ModifiedBy = CurrentUserService.CurrentUserName

                Await _context.SaveChangesAsync()
                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' حذف حساب
        ''' </summary>
        ''' <param name="accountId">معرف الحساب</param>
        ''' <returns>True إذا تم الحذف بنجاح</returns>
        Public Async Function DeleteAccountAsync(accountId As Integer) As Task(Of Boolean)
            Try
                ' التحقق من وجود حسابات فرعية
                Dim hasChildAccounts = Await _context.ChartOfAccounts.AnyAsync(Function(ca) ca.ParentAccountId = accountId)
                If hasChildAccounts Then
                    Return False ' لا يمكن حذف حساب له حسابات فرعية
                End If

                ' التحقق من وجود معاملات على الحساب
                Dim hasTransactions = Await _context.JournalEntryDetails.AnyAsync(Function(jed) jed.AccountId = accountId)
                If hasTransactions Then
                    Return False ' لا يمكن حذف حساب له معاملات
                End If

                Dim account = Await _context.ChartOfAccounts.FirstOrDefaultAsync(Function(ca) ca.AccountId = accountId)
                If account IsNot Nothing Then
                    _context.ChartOfAccounts.Remove(account)
                    Await _context.SaveChangesAsync()
                    Return True
                End If

                Return False

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديد طبيعة الحساب بناءً على رمز الحساب ونوعه
        ''' </summary>
        ''' <param name="accountCode">رمز الحساب</param>
        ''' <param name="accountType">نوع الحساب</param>
        ''' <returns>طبيعة الحساب</returns>
        Private Function DetermineAccountNature(accountCode As String, accountType As String) As String
            If String.IsNullOrEmpty(accountCode) Then Return "مدين"

            Dim firstDigit = accountCode.Substring(0, 1)

            Select Case firstDigit
                Case "1" ' الإيرادات النهائية
                    Return "دائن"
                Case "2" ' المصرف النهائي (النفقات)
                    Return "مدين"
                Case "3" ' الموجودات المالية
                    Return "مدين"
                Case "4" ' المطلوبات المالية
                    Return "دائن"
                Case Else
                    ' تحديد حسب نوع الحساب
                    If accountType.Contains("إيراد") OrElse accountType.Contains("دائن") Then
                        Return "دائن"
                    Else
                        Return "مدين"
                    End If
            End Select
        End Function

        ''' <summary>
        ''' البحث في الحسابات
        ''' </summary>
        ''' <param name="searchText">نص البحث</param>
        ''' <returns>قائمة الحسابات المطابقة</returns>
        Public Async Function SearchAccountsAsync(searchText As String) As Task(Of List(Of ChartOfAccount))
            Try
                If String.IsNullOrWhiteSpace(searchText) Then
                    Return Await GetAllAccountsAsync()
                End If

                Return Await _context.ChartOfAccounts.Where(Function(ca) 
                    ca.AccountName.Contains(searchText) OrElse 
                    ca.AccountCode.Contains(searchText) OrElse 
                    ca.AccountType.Contains(searchText)
                ).OrderBy(Function(ca) ca.AccountCode).ToListAsync()

            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على الحسابات التحليلية فقط
        ''' </summary>
        ''' <returns>قائمة الحسابات التحليلية</returns>
        Public Async Function GetAnalyticalAccountsAsync() As Task(Of List(Of ChartOfAccount))
            Try
                Return Await _context.ChartOfAccounts.Where(Function(ca) ca.IsAnalytical AndAlso ca.IsActive).OrderBy(Function(ca) ca.AccountCode).ToListAsync()
            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على الحسابات حسب النوع
        ''' </summary>
        ''' <param name="accountType">نوع الحساب</param>
        ''' <returns>قائمة الحسابات</returns>
        Public Async Function GetAccountsByTypeAsync(accountType As String) As Task(Of List(Of ChartOfAccount))
            Try
                Return Await _context.ChartOfAccounts.Where(Function(ca) ca.AccountType = accountType AndAlso ca.IsActive).OrderBy(Function(ca) ca.AccountCode).ToListAsync()
            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على الحسابات حسب الطبيعة
        ''' </summary>
        ''' <param name="accountNature">طبيعة الحساب</param>
        ''' <returns>قائمة الحسابات</returns>
        Public Async Function GetAccountsByNatureAsync(accountNature As String) As Task(Of List(Of ChartOfAccount))
            Try
                Return Await _context.ChartOfAccounts.Where(Function(ca) ca.AccountNature = accountNature AndAlso ca.IsActive).OrderBy(Function(ca) ca.AccountCode).ToListAsync()
            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' إنشاء دليل الحسابات الافتراضي
        ''' </summary>
        ''' <returns>True إذا تم الإنشاء بنجاح</returns>
        Public Async Function CreateDefaultChartOfAccountsAsync() As Task(Of Boolean)
            Try
                ' التحقق من وجود حسابات مسبقاً
                Dim existingAccountsCount = Await _context.ChartOfAccounts.CountAsync()
                If existingAccountsCount > 0 Then
                    Return False ' يوجد حسابات مسبقاً
                End If

                ' إنشاء الحسابات الافتراضية
                Await CreateDefaultAccounts()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إنشاء الحسابات الافتراضية
        ''' </summary>
        Private Async Function CreateDefaultAccounts() As Task
            Dim defaultAccounts As New List(Of ChartOfAccount) From {
                ' الإيرادات النهائية
                New ChartOfAccount() With {.AccountCode = "1", .AccountName = "الإيرادات النهائية", .AccountType = "إيرادات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True},
                
                ' المصرف النهائي
                New ChartOfAccount() With {.AccountCode = "2", .AccountName = "المصرف النهائي", .AccountType = "نفقات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                
                ' الموجودات المالية
                New ChartOfAccount() With {.AccountCode = "3", .AccountName = "الموجودات المالية", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "31", .AccountName = "الموجودات المحلية", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "311", .AccountName = "عملة الودائع (نقد)", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "3111", .AccountName = "نقد في الصندوق", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = True, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "312", .AccountName = "نقد في المصارف", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "3121", .AccountName = "بنك النفقات الاعتيادي/ التشغيلية", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "31211", .AccountName = "بنك النفقات الاعتيادي/ التشغيلية", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = True, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "32", .AccountName = "حسابات دائنة أخرى", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "321", .AccountName = "السلف النقدية", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "3211", .AccountName = "السلف المؤقتة", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = True, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "3212", .AccountName = "سلف اللجان", .AccountType = "موجودات", .AccountNature = "مدين", .IsAnalytical = True, .IsActive = True},
                
                ' المطلوبات المالية
                New ChartOfAccount() With {.AccountCode = "4", .AccountName = "المطلوبات المالية", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "41", .AccountName = "المطلوبات المحلية", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "4121", .AccountName = "الرصيد النقدي المدور", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = True, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "42", .AccountName = "الحسابات الدائنة الأخرى", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "421", .AccountName = "أمانات ضمان تحصيل الإيرادات", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "4219", .AccountName = "أمانات أخرى", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = True, .IsActive = True},
                New ChartOfAccount() With {.AccountCode = "43", .AccountName = "حسابات المطلوبات النظامية", .AccountType = "مطلوبات", .AccountNature = "دائن", .IsAnalytical = False, .IsActive = True}
            }

            For Each account In defaultAccounts
                account.CreatedDate = DateTime.Now
                account.CreatedBy = CurrentUserService.CurrentUserName
                _context.ChartOfAccounts.Add(account)
            Next

            ' تحديد الحسابات الأب
            Await _context.SaveChangesAsync()
            Await UpdateParentAccountIds()
        End Function

        ''' <summary>
        ''' تحديث معرفات الحسابات الأب
        ''' </summary>
        Private Async Function UpdateParentAccountIds() As Task
            Dim allAccounts = Await _context.ChartOfAccounts.ToListAsync()

            For Each account In allAccounts
                If account.AccountCode.Length > 1 Then
                    For i As Integer = account.AccountCode.Length - 1 To 1 Step -1
                        Dim parentCode = account.AccountCode.Substring(0, i)
                        Dim parentAccount = allAccounts.FirstOrDefault(Function(a) a.AccountCode = parentCode)
                        If parentAccount IsNot Nothing Then
                            account.ParentAccountId = parentAccount.AccountId
                            Exit For
                        End If
                    Next
                End If
            Next

            Await _context.SaveChangesAsync()
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
