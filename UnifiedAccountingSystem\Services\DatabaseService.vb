Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data

Namespace Services

    ''' <summary>
    ''' خدمة قاعدة البيانات
    ''' </summary>
    Public Class DatabaseService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' إنشاء قاعدة البيانات
        ''' </summary>
        ''' <returns>True إذا تم الإنشاء بنجاح</returns>
        Public Async Function CreateDatabaseAsync() As Task(Of Boolean)
            Try
                Return Await Task.Run(Function() _context.Database.CreateIfNotExists())
            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' التحقق من وجود قاعدة البيانات
        ''' </summary>
        ''' <returns>True إذا كانت موجودة</returns>
        Public Async Function DatabaseExistsAsync() As Task(Of Boolean)
            Try
                Return Await Task.Run(Function() _context.Database.Exists())
            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث قاعدة البيانات
        ''' </summary>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateDatabaseAsync() As Task(Of Boolean)
            Try
                Return Await Task.Run(Function()
                    _context.Database.CreateIfNotExists()
                    Return True
                End Function)
            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
