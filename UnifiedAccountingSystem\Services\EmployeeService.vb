Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة إدارة الموظفين
    ''' </summary>
    Public Class EmployeeService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' الحصول على جميع الموظفين
        ''' </summary>
        ''' <returns>قائمة الموظفين</returns>
        Public Async Function GetAllEmployeesAsync() As Task(Of List(Of Employee))
            Try
                Return Await _context.Employees.Include(Function(e) e.Department).Include(Function(e) e.Section).Include(Function(e) e.Division).OrderBy(Function(e) e.EmployeeName).ToListAsync()
            Catch ex As Exception
                Return New List(Of Employee)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على موظف بالمعرف
        ''' </summary>
        ''' <param name="employeeId">معرف الموظف</param>
        ''' <returns>الموظف</returns>
        Public Async Function GetEmployeeByIdAsync(employeeId As Integer) As Task(Of Employee)
            Try
                Return Await _context.Employees.Include(Function(e) e.Department).Include(Function(e) e.Section).Include(Function(e) e.Division).FirstOrDefaultAsync(Function(e) e.EmployeeId = employeeId)
            Catch ex As Exception
                Return Nothing
            End Try
        End Function

        ''' <summary>
        ''' إضافة موظف جديد
        ''' </summary>
        ''' <param name="employee">الموظف الجديد</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddEmployeeAsync(employee As Employee) As Task(Of Boolean)
            Try
                employee.CreatedDate = DateTime.Now
                employee.CreatedBy = CurrentUserService.CurrentUserName

                _context.Employees.Add(employee)
                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث موظف موجود
        ''' </summary>
        ''' <param name="employee">الموظف المحدث</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateEmployeeAsync(employee As Employee) As Task(Of Boolean)
            Try
                Dim existingEmployee = Await _context.Employees.FirstOrDefaultAsync(Function(e) e.EmployeeId = employee.EmployeeId)
                If existingEmployee Is Nothing Then
                    Return False
                End If

                ' تحديث البيانات
                existingEmployee.EmployeeName = employee.EmployeeName
                existingEmployee.BaseSalary = employee.BaseSalary
                existingEmployee.DepartmentId = employee.DepartmentId
                existingEmployee.SectionId = employee.SectionId
                existingEmployee.DivisionId = employee.DivisionId
                existingEmployee.JobTitleId = employee.JobTitleId
                existingEmployee.JobGradeId = employee.JobGradeId
                existingEmployee.QualificationId = employee.QualificationId
                existingEmployee.IsActive = employee.IsActive
                existingEmployee.ModifiedDate = DateTime.Now
                existingEmployee.ModifiedBy = CurrentUserService.CurrentUserName

                Await _context.SaveChangesAsync()
                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' حذف موظف
        ''' </summary>
        ''' <param name="employeeId">معرف الموظف</param>
        ''' <returns>True إذا تم الحذف بنجاح</returns>
        Public Async Function DeleteEmployeeAsync(employeeId As Integer) As Task(Of Boolean)
            Try
                Dim employee = Await _context.Employees.FirstOrDefaultAsync(Function(e) e.EmployeeId = employeeId)
                If employee IsNot Nothing Then
                    _context.Employees.Remove(employee)
                    Await _context.SaveChangesAsync()
                    Return True
                End If

                Return False

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' البحث في الموظفين
        ''' </summary>
        ''' <param name="searchText">نص البحث</param>
        ''' <returns>قائمة الموظفين المطابقة</returns>
        Public Async Function SearchEmployeesAsync(searchText As String) As Task(Of List(Of Employee))
            Try
                If String.IsNullOrWhiteSpace(searchText) Then
                    Return Await GetAllEmployeesAsync()
                End If

                Return Await _context.Employees.Include(Function(e) e.Department).Include(Function(e) e.Section).Include(Function(e) e.Division).Where(Function(e) e.EmployeeName.Contains(searchText)).OrderBy(Function(e) e.EmployeeName).ToListAsync()

            Catch ex As Exception
                Return New List(Of Employee)()
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
