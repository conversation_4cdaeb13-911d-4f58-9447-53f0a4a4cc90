Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة إدارة المصروفات والتخصيصات المالية
    ''' </summary>
    Public Class ExpenseService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' الحصول على بيانات المصروفات
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="month">الشهر</param>
        ''' <param name="departmentId">معرف الدائرة</param>
        ''' <returns>قائمة بيانات المصروفات</returns>
        Public Async Function GetExpenseDataAsync(fiscalYear As Integer, Optional month As Integer = 0, Optional departmentId As Integer? = Nothing) As Task(Of List(Of BudgetAllocation))
            Try
                Dim query = _context.BudgetAllocations.Include(Function(ba) ba.Department).Include(Function(ba) ba.MonthlyExpenses).AsQueryable()

                ' تصفية حسب السنة المالية
                query = query.Where(Function(ba) ba.FiscalYear = fiscalYear)

                ' تصفية حسب الدائرة إذا تم تحديدها
                If departmentId.HasValue Then
                    query = query.Where(Function(ba) ba.DepartmentId = departmentId.Value)
                End If

                ' ترتيب البيانات
                query = query.OrderBy(Function(ba) ba.FormType).ThenBy(Function(ba) ba.ExpenseType).ThenBy(Function(ba) ba.Chapter).ThenBy(Function(ba) ba.Article)

                Return Await query.ToListAsync()

            Catch ex As Exception
                Return New List(Of BudgetAllocation)()
            End Try
        End Function

        ''' <summary>
        ''' إضافة تخصيص موازنة جديد
        ''' </summary>
        ''' <param name="allocation">بيانات التخصيص</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddBudgetAllocationAsync(allocation As BudgetAllocation) As Task(Of Boolean)
            Try
                ' التحقق من عدم وجود تخصيص مماثل
                Dim existingAllocation = Await _context.BudgetAllocations.FirstOrDefaultAsync(Function(ba) 
                    ba.FiscalYear = allocation.FiscalYear AndAlso
                    ba.FormType = allocation.FormType AndAlso
                    ba.ExpenseType = allocation.ExpenseType AndAlso
                    ba.Chapter = allocation.Chapter AndAlso
                    ba.Article = allocation.Article AndAlso
                    ba.ItemType = allocation.ItemType AndAlso
                    ba.ItemDetails = allocation.ItemDetails
                )

                If existingAllocation IsNot Nothing Then
                    Return False ' التخصيص موجود مسبقاً
                End If

                allocation.CreatedDate = DateTime.Now
                allocation.CreatedBy = CurrentUserService.CurrentUserName

                _context.BudgetAllocations.Add(allocation)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "إضافة", "BudgetAllocations", allocation.AllocationId.ToString())
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث تخصيص الموازنة
        ''' </summary>
        ''' <param name="allocation">بيانات التخصيص المحدثة</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateBudgetAllocationAsync(allocation As BudgetAllocation) As Task(Of Boolean)
            Try
                Dim existingAllocation = Await _context.BudgetAllocations.FindAsync(allocation.AllocationId)
                If existingAllocation Is Nothing Then
                    Return False
                End If

                ' حفظ القيم القديمة للتسجيل
                Dim oldValues = $"Description: {existingAllocation.Description}, AnnualAllocation: {existingAllocation.AnnualAllocation}"

                ' تحديث البيانات
                existingAllocation.FormType = allocation.FormType
                existingAllocation.ExpenseType = allocation.ExpenseType
                existingAllocation.Chapter = allocation.Chapter
                existingAllocation.Article = allocation.Article
                existingAllocation.ItemType = allocation.ItemType
                existingAllocation.ItemDetails = allocation.ItemDetails
                existingAllocation.Description = allocation.Description
                existingAllocation.AnnualAllocation = allocation.AnnualAllocation
                existingAllocation.DepartmentId = allocation.DepartmentId
                existingAllocation.Province = allocation.Province
                existingAllocation.Section = allocation.Section
                existingAllocation.Division = allocation.Division
                existingAllocation.Branch = allocation.Branch
                existingAllocation.IsActive = allocation.IsActive

                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                Dim newValues = $"Description: {existingAllocation.Description}, AnnualAllocation: {existingAllocation.AnnualAllocation}"
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "تعديل", "BudgetAllocations", allocation.AllocationId.ToString(), oldValues, newValues)
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' حذف تخصيص الموازنة
        ''' </summary>
        ''' <param name="allocationId">معرف التخصيص</param>
        ''' <returns>True إذا تم الحذف بنجاح</returns>
        Public Async Function DeleteBudgetAllocationAsync(allocationId As Integer) As Task(Of Boolean)
            Try
                Dim allocation = Await _context.BudgetAllocations.FindAsync(allocationId)
                If allocation Is Nothing Then
                    Return False
                End If

                ' التحقق من وجود مصروفات مرتبطة
                Dim hasExpenses = Await _context.MonthlyExpenses.AnyAsync(Function(me) me.AllocationId = allocationId)
                If hasExpenses Then
                    Return False ' لا يمكن حذف التخصيص لوجود مصروفات مرتبطة
                End If

                ' حفظ البيانات للتسجيل
                Dim allocationInfo = $"Description: {allocation.Description}, AnnualAllocation: {allocation.AnnualAllocation}"

                _context.BudgetAllocations.Remove(allocation)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "حذف", "BudgetAllocations", allocationId.ToString(), allocationInfo)
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إضافة مصروف شهري
        ''' </summary>
        ''' <param name="monthlyExpense">بيانات المصروف الشهري</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddMonthlyExpenseAsync(monthlyExpense As MonthlyExpense) As Task(Of Boolean)
            Try
                ' التحقق من عدم وجود مصروف للشهر نفسه
                Dim existingExpense = Await _context.MonthlyExpenses.FirstOrDefaultAsync(Function(me) 
                    me.AllocationId = monthlyExpense.AllocationId AndAlso
                    me.ExpenseYear = monthlyExpense.ExpenseYear AndAlso
                    me.ExpenseMonth = monthlyExpense.ExpenseMonth
                )

                If existingExpense IsNot Nothing Then
                    Return False ' المصروف موجود مسبقاً
                End If

                monthlyExpense.CreatedDate = DateTime.Now
                monthlyExpense.CreatedBy = CurrentUserService.CurrentUserName

                _context.MonthlyExpenses.Add(monthlyExpense)

                ' تحديث إجمالي المصروف في التخصيص
                Dim allocation = Await _context.BudgetAllocations.FindAsync(monthlyExpense.AllocationId)
                If allocation IsNot Nothing Then
                    allocation.SpentToDate += monthlyExpense.GrandTotal
                End If

                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث المصروف الشهري
        ''' </summary>
        ''' <param name="monthlyExpense">بيانات المصروف المحدثة</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateMonthlyExpenseAsync(monthlyExpense As MonthlyExpense) As Task(Of Boolean)
            Try
                Dim existingExpense = Await _context.MonthlyExpenses.FindAsync(monthlyExpense.ExpenseId)
                If existingExpense Is Nothing Then
                    Return False
                End If

                ' حساب الفرق في المبلغ
                Dim oldTotal = existingExpense.GrandTotal
                Dim newTotal = monthlyExpense.GrandTotal
                Dim difference = newTotal - oldTotal

                ' تحديث البيانات
                existingExpense.PreviousMonthFils = monthlyExpense.PreviousMonthFils
                existingExpense.PreviousMonthDinars = monthlyExpense.PreviousMonthDinars
                existingExpense.CurrentMonthFils = monthlyExpense.CurrentMonthFils
                existingExpense.CurrentMonthDinars = monthlyExpense.CurrentMonthDinars
                existingExpense.Notes = monthlyExpense.Notes

                ' تحديث إجمالي المصروف في التخصيص
                Dim allocation = Await _context.BudgetAllocations.FindAsync(existingExpense.AllocationId)
                If allocation IsNot Nothing Then
                    allocation.SpentToDate += difference
                End If

                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إنشاء ميزان المراجعة
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="month">الشهر</param>
        ''' <returns>True إذا تم الإنشاء بنجاح</returns>
        Public Async Function GenerateTrialBalanceAsync(fiscalYear As Integer, month As Integer) As Task(Of Boolean)
            Try
                ' حذف ميزان المراجعة الموجود للفترة نفسها
                Dim existingBalances = Await _context.TrialBalances.Where(Function(tb) tb.FiscalYear = fiscalYear AndAlso tb.Month = month).ToListAsync()
                _context.TrialBalances.RemoveRange(existingBalances)

                ' الحصول على جميع الحسابات
                Dim accounts = Await _context.ChartOfAccounts.Where(Function(ca) ca.IsAnalytical).ToListAsync()

                For Each account In accounts
                    ' حساب الأرصدة والحركات
                    Dim trialBalance As New TrialBalance() With {
                        .FiscalYear = fiscalYear,
                        .Month = month,
                        .AccountId = account.AccountId,
                        .CreatedDate = DateTime.Now,
                        .CreatedBy = CurrentUserService.CurrentUserName
                    }

                    ' حساب الرصيد الافتتاحي
                    ' يمكن إضافة منطق حساب الرصيد الافتتاحي هنا

                    ' حساب الحركات من القيود اليومية
                    Dim journalEntries = Await _context.JournalEntryDetails.Include(Function(jed) jed.JournalEntry) _
                        .Where(Function(jed) jed.AccountId = account.AccountId AndAlso 
                               jed.JournalEntry.EntryDate.Year = fiscalYear AndAlso 
                               jed.JournalEntry.EntryDate.Month <= month) _
                        .ToListAsync()

                    trialBalance.DebitMovement = journalEntries.Sum(Function(je) je.DebitAmount)
                    trialBalance.CreditMovement = journalEntries.Sum(Function(je) je.CreditAmount)

                    ' حساب الرصيد الختامي
                    If account.AccountNature = "مدين" Then
                        trialBalance.ClosingDebitBalance = trialBalance.OpeningDebitBalance + trialBalance.DebitMovement - trialBalance.CreditMovement
                        If trialBalance.ClosingDebitBalance < 0 Then
                            trialBalance.ClosingCreditBalance = Math.Abs(trialBalance.ClosingDebitBalance)
                            trialBalance.ClosingDebitBalance = 0
                        End If
                    Else
                        trialBalance.ClosingCreditBalance = trialBalance.OpeningCreditBalance + trialBalance.CreditMovement - trialBalance.DebitMovement
                        If trialBalance.ClosingCreditBalance < 0 Then
                            trialBalance.ClosingDebitBalance = Math.Abs(trialBalance.ClosingCreditBalance)
                            trialBalance.ClosingCreditBalance = 0
                        End If
                    End If

                    _context.TrialBalances.Add(trialBalance)
                Next

                Await _context.SaveChangesAsync()
                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' الحصول على ميزان المراجعة
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="month">الشهر</param>
        ''' <returns>قائمة ميزان المراجعة</returns>
        Public Async Function GetTrialBalanceAsync(fiscalYear As Integer, month As Integer) As Task(Of List(Of TrialBalance))
            Try
                Return Await _context.TrialBalances.Include(Function(tb) tb.Account) _
                    .Where(Function(tb) tb.FiscalYear = fiscalYear AndAlso tb.Month = month) _
                    .OrderBy(Function(tb) tb.Account.AccountCode) _
                    .ToListAsync()

            Catch ex As Exception
                Return New List(Of TrialBalance)()
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
