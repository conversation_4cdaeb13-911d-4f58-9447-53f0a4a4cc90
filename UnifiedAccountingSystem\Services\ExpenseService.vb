Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة إدارة المصروفات والتخصيصات المالية
    ''' </summary>
    Public Class ExpenseService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' الحصول على بيانات المصروفات
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="month">الشهر</param>
        ''' <param name="departmentId">معرف الدائرة</param>
        ''' <returns>قائمة بيانات المصروفات</returns>
        Public Async Function GetExpenseDataAsync(fiscalYear As Integer, Optional month As Integer = 0, Optional departmentId As Integer? = Nothing) As Task(Of List(Of BudgetAllocation))
            Try
                Dim query = _context.BudgetAllocations.Include(Function(ba) ba.Department).Include(Function(ba) ba.MonthlyExpenses).AsQueryable()

                ' تصفية حسب السنة المالية
                query = query.Where(Function(ba) ba.FiscalYear = fiscalYear)

                ' تصفية حسب الدائرة إذا تم تحديدها
                If departmentId.HasValue Then
                    query = query.Where(Function(ba) ba.DepartmentId = departmentId.Value)
                End If

                ' ترتيب البيانات
                query = query.OrderBy(Function(ba) ba.FormType).ThenBy(Function(ba) ba.ExpenseType).ThenBy(Function(ba) ba.Chapter).ThenBy(Function(ba) ba.Article)

                Return Await query.ToListAsync()

            Catch ex As Exception
                Return New List(Of BudgetAllocation)()
            End Try
        End Function

        ''' <summary>
        ''' إضافة تخصيص موازنة جديد
        ''' </summary>
        ''' <param name="allocation">بيانات التخصيص</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddBudgetAllocationAsync(allocation As BudgetAllocation) As Task(Of Boolean)
            Try
                ' التحقق من عدم وجود تخصيص مماثل
                Dim existingAllocation = Await _context.BudgetAllocations.FirstOrDefaultAsync(Function(ba)
                    ba.FiscalYear = allocation.FiscalYear AndAlso
                    ba.FormType = allocation.FormType AndAlso
                    ba.ExpenseType = allocation.ExpenseType AndAlso
                    ba.Chapter = allocation.Chapter AndAlso
                    ba.Article = allocation.Article AndAlso
                    ba.ItemType = allocation.ItemType AndAlso
                    ba.ItemDetails = allocation.ItemDetails
                )

                If existingAllocation IsNot Nothing Then
                    Return False ' التخصيص موجود مسبقاً
                End If

                allocation.CreatedDate = DateTime.Now
                allocation.CreatedBy = CurrentUserService.CurrentUserName

                _context.BudgetAllocations.Add(allocation)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "إضافة", "BudgetAllocations", allocation.AllocationId.ToString())
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث تخصيص الموازنة
        ''' </summary>
        ''' <param name="allocation">بيانات التخصيص المحدثة</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateBudgetAllocationAsync(allocation As BudgetAllocation) As Task(Of Boolean)
            Try
                Dim existingAllocation = Await _context.BudgetAllocations.FindAsync(allocation.AllocationId)
                If existingAllocation Is Nothing Then
                    Return False
                End If

                ' حفظ القيم القديمة للتسجيل
                Dim oldValues = $"Description: {existingAllocation.Description}, AnnualAllocation: {existingAllocation.AnnualAllocation}"

                ' تحديث البيانات
                existingAllocation.FormType = allocation.FormType
                existingAllocation.ExpenseType = allocation.ExpenseType
                existingAllocation.Chapter = allocation.Chapter
                existingAllocation.Article = allocation.Article
                existingAllocation.ItemType = allocation.ItemType
                existingAllocation.ItemDetails = allocation.ItemDetails
                existingAllocation.Description = allocation.Description
                existingAllocation.AnnualAllocation = allocation.AnnualAllocation
                existingAllocation.DepartmentId = allocation.DepartmentId
                existingAllocation.Province = allocation.Province
                existingAllocation.Section = allocation.Section
                existingAllocation.Division = allocation.Division
                existingAllocation.Branch = allocation.Branch
                existingAllocation.IsActive = allocation.IsActive

                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                Dim newValues = $"Description: {existingAllocation.Description}, AnnualAllocation: {existingAllocation.AnnualAllocation}"
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "تعديل", "BudgetAllocations", allocation.AllocationId.ToString(), oldValues, newValues)
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' حذف تخصيص الموازنة
        ''' </summary>
        ''' <param name="allocationId">معرف التخصيص</param>
        ''' <returns>True إذا تم الحذف بنجاح</returns>
        Public Async Function DeleteBudgetAllocationAsync(allocationId As Integer) As Task(Of Boolean)
            Try
                Dim allocation = Await _context.BudgetAllocations.FindAsync(allocationId)
                If allocation Is Nothing Then
                    Return False
                End If

                ' التحقق من وجود مصروفات مرتبطة
                Dim hasExpenses = Await _context.MonthlyExpenses.AnyAsync(Function(me) me.AllocationId = allocationId)
                If hasExpenses Then
                    Return False ' لا يمكن حذف التخصيص لوجود مصروفات مرتبطة
                End If

                ' حفظ البيانات للتسجيل
                Dim allocationInfo = $"Description: {allocation.Description}, AnnualAllocation: {allocation.AnnualAllocation}"

                _context.BudgetAllocations.Remove(allocation)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Dim userService As New UserService()
                    Await userService.LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "حذف", "BudgetAllocations", allocationId.ToString(), allocationInfo)
                    userService.Dispose()
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إضافة مصروف شهري
        ''' </summary>
        ''' <param name="monthlyExpense">بيانات المصروف الشهري</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddMonthlyExpenseAsync(monthlyExpense As MonthlyExpense) As Task(Of Boolean)
            Try
                ' التحقق من عدم وجود مصروف للشهر نفسه
                Dim existingExpense = Await _context.MonthlyExpenses.FirstOrDefaultAsync(Function(me)
                    me.AllocationId = monthlyExpense.AllocationId AndAlso
                    me.ExpenseYear = monthlyExpense.ExpenseYear AndAlso
                    me.ExpenseMonth = monthlyExpense.ExpenseMonth
                )

                If existingExpense IsNot Nothing Then
                    Return False ' المصروف موجود مسبقاً
                End If

                monthlyExpense.CreatedDate = DateTime.Now
                monthlyExpense.CreatedBy = CurrentUserService.CurrentUserName

                _context.MonthlyExpenses.Add(monthlyExpense)

                ' تحديث إجمالي المصروف في التخصيص
                Dim allocation = Await _context.BudgetAllocations.FindAsync(monthlyExpense.AllocationId)
                If allocation IsNot Nothing Then
                    allocation.SpentToDate += monthlyExpense.GrandTotal
                End If

                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث المصروف الشهري
        ''' </summary>
        ''' <param name="monthlyExpense">بيانات المصروف المحدثة</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateMonthlyExpenseAsync(monthlyExpense As MonthlyExpense) As Task(Of Boolean)
            Try
                Dim existingExpense = Await _context.MonthlyExpenses.FindAsync(monthlyExpense.ExpenseId)
                If existingExpense Is Nothing Then
                    Return False
                End If

                ' حساب الفرق في المبلغ
                Dim oldTotal = existingExpense.GrandTotal
                Dim newTotal = monthlyExpense.GrandTotal
                Dim difference = newTotal - oldTotal

                ' تحديث البيانات
                existingExpense.PreviousMonthFils = monthlyExpense.PreviousMonthFils
                existingExpense.PreviousMonthDinars = monthlyExpense.PreviousMonthDinars
                existingExpense.CurrentMonthFils = monthlyExpense.CurrentMonthFils
                existingExpense.CurrentMonthDinars = monthlyExpense.CurrentMonthDinars
                existingExpense.Notes = monthlyExpense.Notes

                ' تحديث إجمالي المصروف في التخصيص
                Dim allocation = Await _context.BudgetAllocations.FindAsync(existingExpense.AllocationId)
                If allocation IsNot Nothing Then
                    allocation.SpentToDate += difference
                End If

                Await _context.SaveChangesAsync()

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' إنشاء ميزان المراجعة المطور
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="fromMonth">من شهر</param>
        ''' <param name="toMonth">إلى شهر</param>
        ''' <param name="treasuryType">نوع الخزينة</param>
        ''' <param name="bankAccountNumber">رقم الحساب البنكي</param>
        ''' <returns>True إذا تم الإنشاء بنجاح</returns>
        Public Async Function GenerateTrialBalanceAsync(fiscalYear As Integer, fromMonth As Integer, toMonth As Integer, treasuryType As String, Optional bankAccountNumber As String = Nothing) As Task(Of Boolean)
            Try
                ' حذف ميزان المراجعة الموجود للفترة نفسها
                Dim existingBalances = Await _context.TrialBalances.Where(Function(tb)
                    tb.FiscalYear = fiscalYear AndAlso
                    tb.FromMonth = fromMonth AndAlso
                    tb.ToMonth = toMonth AndAlso
                    tb.TreasuryType = treasuryType).ToListAsync()
                _context.TrialBalances.RemoveRange(existingBalances)

                ' الحصول على الحسابات المناسبة حسب نوع الخزينة
                Dim accounts = Await GetAccountsByTreasuryType(treasuryType, bankAccountNumber)

                For Each account In accounts
                    ' إنشاء سجل ميزان المراجعة
                    Dim trialBalance As New TrialBalance() With {
                        .FiscalYear = fiscalYear,
                        .FromMonth = fromMonth,
                        .ToMonth = toMonth,
                        .AccountId = account.AccountId,
                        .TreasuryType = treasuryType,
                        .BankAccountNumber = bankAccountNumber,
                        .CreatedDate = DateTime.Now,
                        .CreatedBy = CurrentUserService.CurrentUserName
                    }

                    ' حساب معاملات الشهر الحالي
                    Dim currentMonthEntries = Await GetJournalEntriesForPeriod(account.AccountId, fiscalYear, toMonth, toMonth, treasuryType)
                    trialBalance.CurrentMonthDebitDinars = currentMonthEntries.Sum(Function(je) Math.Floor(je.DebitAmount))
                    trialBalance.CurrentMonthDebitFils = currentMonthEntries.Sum(Function(je) (je.DebitAmount - Math.Floor(je.DebitAmount)) * 1000)
                    trialBalance.CurrentMonthCreditDinars = currentMonthEntries.Sum(Function(je) Math.Floor(je.CreditAmount))
                    trialBalance.CurrentMonthCreditFils = currentMonthEntries.Sum(Function(je) (je.CreditAmount - Math.Floor(je.CreditAmount)) * 1000)

                    ' حساب المدور في الأشهر السابقة
                    If fromMonth < toMonth Then
                        Dim previousEntries = Await GetJournalEntriesForPeriod(account.AccountId, fiscalYear, fromMonth, toMonth - 1, treasuryType)
                        trialBalance.PreviousCarriedDebitDinars = previousEntries.Sum(Function(je) Math.Floor(je.DebitAmount))
                        trialBalance.PreviousCarriedDebitFils = previousEntries.Sum(Function(je) (je.DebitAmount - Math.Floor(je.DebitAmount)) * 1000)
                        trialBalance.PreviousCarriedCreditDinars = previousEntries.Sum(Function(je) Math.Floor(je.CreditAmount))
                        trialBalance.PreviousCarriedCreditFils = previousEntries.Sum(Function(je) (je.CreditAmount - Math.Floor(je.CreditAmount)) * 1000)
                    End If

                    _context.TrialBalances.Add(trialBalance)
                Next

                Await _context.SaveChangesAsync()
                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' الحصول على ميزان المراجعة المطور
        ''' </summary>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="fromMonth">من شهر</param>
        ''' <param name="toMonth">إلى شهر</param>
        ''' <param name="treasuryType">نوع الخزينة</param>
        ''' <param name="bankAccountNumber">رقم الحساب البنكي</param>
        ''' <returns>قائمة ميزان المراجعة</returns>
        Public Async Function GetTrialBalanceAsync(fiscalYear As Integer, fromMonth As Integer, toMonth As Integer, treasuryType As String, Optional bankAccountNumber As String = Nothing) As Task(Of List(Of TrialBalance))
            Try
                Dim query = _context.TrialBalances.Include(Function(tb) tb.Account).AsQueryable()

                query = query.Where(Function(tb)
                    tb.FiscalYear = fiscalYear AndAlso
                    tb.FromMonth = fromMonth AndAlso
                    tb.ToMonth = toMonth AndAlso
                    tb.TreasuryType = treasuryType)

                If Not String.IsNullOrEmpty(bankAccountNumber) Then
                    query = query.Where(Function(tb) tb.BankAccountNumber = bankAccountNumber)
                End If

                Return Await query.OrderBy(Function(tb) tb.Account.AccountCode).ToListAsync()

            Catch ex As Exception
                Return New List(Of TrialBalance)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على الحسابات حسب نوع الخزينة
        ''' </summary>
        ''' <param name="treasuryType">نوع الخزينة</param>
        ''' <param name="bankAccountNumber">رقم الحساب البنكي</param>
        ''' <returns>قائمة الحسابات</returns>
        Private Async Function GetAccountsByTreasuryType(treasuryType As String, Optional bankAccountNumber As String = Nothing) As Task(Of List(Of ChartOfAccount))
            Try
                Dim query = _context.ChartOfAccounts.AsQueryable()

                ' تصفية الحسابات حسب نوع الخزينة
                If treasuryType = "التشغيلية" Then
                    ' حسابات المصروفات التشغيلية
                    query = query.Where(Function(ca) ca.AccountCode.StartsWith("3") OrElse ca.AccountCode.StartsWith("4"))
                ElseIf treasuryType = "الرواتب" Then
                    ' حسابات الرواتب
                    query = query.Where(Function(ca) ca.AccountCode.StartsWith("2") OrElse ca.AccountCode.StartsWith("1"))
                End If

                ' تصفية إضافية حسب رقم الحساب البنكي إذا تم تحديده
                If Not String.IsNullOrEmpty(bankAccountNumber) Then
                    query = query.Where(Function(ca) ca.AccountName.Contains(bankAccountNumber))
                End If

                Return Await query.Where(Function(ca) ca.IsActive).OrderBy(Function(ca) ca.AccountCode).ToListAsync()

            Catch ex As Exception
                Return New List(Of ChartOfAccount)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على القيود اليومية لفترة محددة
        ''' </summary>
        ''' <param name="accountId">معرف الحساب</param>
        ''' <param name="fiscalYear">السنة المالية</param>
        ''' <param name="fromMonth">من شهر</param>
        ''' <param name="toMonth">إلى شهر</param>
        ''' <param name="treasuryType">نوع الخزينة</param>
        ''' <returns>قائمة تفاصيل القيود</returns>
        Private Async Function GetJournalEntriesForPeriod(accountId As Integer, fiscalYear As Integer, fromMonth As Integer, toMonth As Integer, treasuryType As String) As Task(Of List(Of JournalEntryDetail))
            Try
                Dim query = _context.JournalEntryDetails.Include(Function(jed) jed.JournalEntry).AsQueryable()

                query = query.Where(Function(jed)
                    jed.AccountId = accountId AndAlso
                    jed.JournalEntry.EntryDate.Year = fiscalYear AndAlso
                    jed.JournalEntry.EntryDate.Month >= fromMonth AndAlso
                    jed.JournalEntry.EntryDate.Month <= toMonth)

                ' تصفية إضافية حسب نوع الخزينة
                If treasuryType = "التشغيلية" Then
                    query = query.Where(Function(jed) jed.JournalEntry.EntryType <> "رواتب")
                ElseIf treasuryType = "الرواتب" Then
                    query = query.Where(Function(jed) jed.JournalEntry.EntryType = "رواتب")
                End If

                Return Await query.ToListAsync()

            Catch ex As Exception
                Return New List(Of JournalEntryDetail)()
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
