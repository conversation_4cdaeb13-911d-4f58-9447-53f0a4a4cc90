Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة إدارة الرواتب
    ''' </summary>
    Public Class PayrollService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' احتساب راتب موظف
        ''' </summary>
        ''' <param name="employeeId">معرف الموظف</param>
        ''' <param name="year">السنة</param>
        ''' <param name="month">الشهر</param>
        ''' <returns>تفاصيل الراتب</returns>
        Public Async Function CalculateEmployeeSalaryAsync(employeeId As Integer, year As Integer, month As Integer) As Task(Of PayrollCalculation)
            Try
                Dim employee = Await _context.Employees.Include(Function(e) e.JobGrade).FirstOrDefaultAsync(Function(e) e.EmployeeId = employeeId)
                If employee Is Nothing Then
                    Return Nothing
                End If

                Dim calculation As New PayrollCalculation() With {
                    .EmployeeId = employeeId,
                    .Year = year,
                    .Month = month,
                    .BaseSalary = employee.BaseSalary,
                    .CreatedDate = DateTime.Now,
                    .CreatedBy = CurrentUserService.CurrentUserName
                }

                ' حساب المخصصات
                Dim allowances = Await _context.EmployeeAllowances.Include(Function(ea) ea.AllowanceType).Where(Function(ea) ea.EmployeeId = employeeId AndAlso ea.IsActive).ToListAsync()
                calculation.TotalAllowances = allowances.Sum(Function(a) a.Amount)

                ' حساب الاستقطاعات
                Dim deductions = Await _context.EmployeeDeductions.Include(Function(ed) ed.DeductionType).Where(Function(ed) ed.EmployeeId = employeeId AndAlso ed.IsActive).ToListAsync()
                calculation.TotalDeductions = deductions.Sum(Function(d) d.Amount)

                ' حساب إجمالي الراتب
                calculation.GrossSalary = calculation.BaseSalary + calculation.TotalAllowances

                ' حساب صافي الراتب
                calculation.NetSalary = calculation.GrossSalary - calculation.TotalDeductions

                Return calculation

            Catch ex As Exception
                Return Nothing
            End Try
        End Function

        ''' <summary>
        ''' احتساب رواتب جميع الموظفين
        ''' </summary>
        ''' <param name="year">السنة</param>
        ''' <param name="month">الشهر</param>
        ''' <returns>True إذا تم الاحتساب بنجاح</returns>
        Public Async Function CalculateAllSalariesAsync(year As Integer, month As Integer) As Task(Of Boolean)
            Try
                ' حذف الحسابات الموجودة للفترة نفسها
                Dim existingCalculations = Await _context.PayrollCalculations.Where(Function(pc) pc.Year = year AndAlso pc.Month = month).ToListAsync()
                _context.PayrollCalculations.RemoveRange(existingCalculations)

                ' الحصول على جميع الموظفين النشطين
                Dim employees = Await _context.Employees.Where(Function(e) e.IsActive).ToListAsync()

                For Each employee In employees
                    Dim calculation = Await CalculateEmployeeSalaryAsync(employee.EmployeeId, year, month)
                    If calculation IsNot Nothing Then
                        _context.PayrollCalculations.Add(calculation)
                    End If
                Next

                Await _context.SaveChangesAsync()
                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' الحصول على حسابات الرواتب
        ''' </summary>
        ''' <param name="year">السنة</param>
        ''' <param name="month">الشهر</param>
        ''' <returns>قائمة حسابات الرواتب</returns>
        Public Async Function GetPayrollCalculationsAsync(year As Integer, month As Integer) As Task(Of List(Of PayrollCalculation))
            Try
                Return Await _context.PayrollCalculations.Include(Function(pc) pc.Employee).Where(Function(pc) pc.Year = year AndAlso pc.Month = month).OrderBy(Function(pc) pc.Employee.EmployeeName).ToListAsync()
            Catch ex As Exception
                Return New List(Of PayrollCalculation)()
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
