Imports System.Data.Entity
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models

Namespace Services

    ''' <summary>
    ''' خدمة التقارير
    ''' </summary>
    Public Class ReportService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' إنشاء تقرير كشف حساب
        ''' </summary>
        ''' <param name="accountId">معرف الحساب</param>
        ''' <param name="fromDate">من تاريخ</param>
        ''' <param name="toDate">إلى تاريخ</param>
        ''' <returns>بيانات التقرير</returns>
        Public Async Function GenerateAccountStatementAsync(accountId As Integer, fromDate As DateTime, toDate As DateTime) As Task(Of List(Of JournalEntryDetail))
            Try
                Return Await _context.JournalEntryDetails.Include(Function(jed) jed.JournalEntry).Include(Function(jed) jed.Account).Where(Function(jed) jed.AccountId = accountId AndAlso jed.JournalEntry.EntryDate >= fromDate AndAlso jed.JournalEntry.EntryDate <= toDate).OrderBy(Function(jed) jed.JournalEntry.EntryDate).ToListAsync()
            Catch ex As Exception
                Return New List(Of JournalEntryDetail)()
            End Try
        End Function

        ''' <summary>
        ''' إنشاء تقرير اليومية العامة
        ''' </summary>
        ''' <param name="fromDate">من تاريخ</param>
        ''' <param name="toDate">إلى تاريخ</param>
        ''' <returns>بيانات التقرير</returns>
        Public Async Function GenerateGeneralJournalAsync(fromDate As DateTime, toDate As DateTime) As Task(Of List(Of JournalEntry))
            Try
                Return Await _context.JournalEntries.Include(Function(je) je.JournalEntryDetails).Where(Function(je) je.EntryDate >= fromDate AndAlso je.EntryDate <= toDate).OrderBy(Function(je) je.EntryDate).ToListAsync()
            Catch ex As Exception
                Return New List(Of JournalEntry)()
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
