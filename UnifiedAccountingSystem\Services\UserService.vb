Imports System.Data.Entity
Imports System.Net
Imports UnifiedAccountingSystem.Data
Imports UnifiedAccountingSystem.Models
Imports UnifiedAccountingSystem.ViewModels

Namespace Services

    ''' <summary>
    ''' خدمة إدارة المستخدمين
    ''' </summary>
    Public Class UserService
        Implements IDisposable

        Private ReadOnly _context As AccountingDbContext

        Public Sub New()
            _context = New AccountingDbContext()
        End Sub

        ''' <summary>
        ''' تسجيل الدخول غير المتزامن
        ''' </summary>
        ''' <param name="username">اسم المستخدم</param>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>نتيجة تسجيل الدخول</returns>
        Public Async Function LoginAsync(username As String, password As String) As Task(Of LoginResult)
            Try
                ' البحث عن المستخدم
                Dim user = Await _context.Users.Include(Function(u) u.UserGroup).Include(Function(u) u.Department) _
                    .FirstOrDefaultAsync(Function(u) u.Username = username AndAlso u.IsActive)

                If user Is Nothing Then
                    Return New LoginResult() With {
                        .Success = False,
                        .ErrorMessage = "اسم المستخدم غير صحيح"
                    }
                End If

                ' التحقق من كلمة المرور
                If Not user.VerifyPassword(password) Then
                    ' تسجيل محاولة دخول فاشلة
                    Await LogFailedLoginAttemptAsync(username)
                    
                    Return New LoginResult() With {
                        .Success = False,
                        .ErrorMessage = "كلمة المرور غير صحيحة"
                    }
                End If

                ' تحديث آخر تسجيل دخول
                user.LastLoginDate = DateTime.Now
                Await _context.SaveChangesAsync()

                ' إنشاء جلسة جديدة
                Dim sessionToken = Await CreateUserSessionAsync(user.UserId)

                Return New LoginResult() With {
                    .Success = True,
                    .User = user,
                    .SessionToken = sessionToken
                }

            Catch ex As Exception
                Return New LoginResult() With {
                    .Success = False,
                    .ErrorMessage = "حدث خطأ أثناء تسجيل الدخول"
                }
            End Try
        End Function

        ''' <summary>
        ''' إنشاء جلسة مستخدم جديدة
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <returns>رمز الجلسة</returns>
        Private Async Function CreateUserSessionAsync(userId As Integer) As Task(Of String)
            Try
                ' إنهاء الجلسات النشطة السابقة
                Dim activeSessions = Await _context.UserSessions _
                    .Where(Function(s) s.UserId = userId AndAlso s.IsActive) _
                    .ToListAsync()

                For Each session In activeSessions
                    session.IsActive = False
                    session.EndTime = DateTime.Now
                Next

                ' إنشاء جلسة جديدة
                Dim sessionToken = Guid.NewGuid().ToString()
                Dim newSession As New UserSession() With {
                    .UserId = userId,
                    .SessionToken = sessionToken,
                    .StartTime = DateTime.Now,
                    .IsActive = True,
                    .IPAddress = GetLocalIPAddress()
                }

                _context.UserSessions.Add(newSession)
                Await _context.SaveChangesAsync()

                Return sessionToken

            Catch ex As Exception
                Return String.Empty
            End Try
        End Function

        ''' <summary>
        ''' تسجيل محاولة دخول فاشلة
        ''' </summary>
        ''' <param name="username">اسم المستخدم</param>
        Private Async Function LogFailedLoginAttemptAsync(username As String) As Task
            Try
                Dim auditLog As New AuditLog() With {
                    .UserId = 0, ' مستخدم غير معروف
                    .ActionType = "محاولة دخول فاشلة",
                    .TableName = "Users",
                    .AdditionalInfo = $"محاولة دخول فاشلة لاسم المستخدم: {username}",
                    .IPAddress = GetLocalIPAddress(),
                    .ActionDate = DateTime.Now
                }

                _context.AuditLogs.Add(auditLog)
                Await _context.SaveChangesAsync()

            Catch ex As Exception
                ' تجاهل أخطاء التسجيل
            End Try
        End Function

        ''' <summary>
        ''' تسجيل عمل المستخدم
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <param name="actionType">نوع العمل</param>
        ''' <param name="tableName">اسم الجدول</param>
        ''' <param name="recordId">معرف السجل</param>
        ''' <param name="oldValues">القيم القديمة</param>
        ''' <param name="newValues">القيم الجديدة</param>
        Public Async Function LogUserActionAsync(userId As Integer, actionType As String, tableName As String, 
                                                recordId As String, Optional oldValues As String = Nothing, 
                                                Optional newValues As String = Nothing) As Task
            Try
                Dim auditLog As New AuditLog() With {
                    .UserId = userId,
                    .ActionType = actionType,
                    .TableName = tableName,
                    .RecordId = recordId,
                    .OldValues = oldValues,
                    .NewValues = newValues,
                    .IPAddress = GetLocalIPAddress(),
                    .ActionDate = DateTime.Now
                }

                _context.AuditLogs.Add(auditLog)
                Await _context.SaveChangesAsync()

            Catch ex As Exception
                ' تجاهل أخطاء التسجيل
            End Try
        End Function

        ''' <summary>
        ''' الحصول على عنوان IP المحلي
        ''' </summary>
        ''' <returns>عنوان IP</returns>
        Private Function GetLocalIPAddress() As String
            Try
                Dim host = Dns.GetHostEntry(Dns.GetHostName())
                For Each ip In host.AddressList
                    If ip.AddressFamily = Net.Sockets.AddressFamily.InterNetwork Then
                        Return ip.ToString()
                    End If
                Next
                Return "127.0.0.1"
            Catch
                Return "127.0.0.1"
            End Try
        End Function

        ''' <summary>
        ''' تسجيل الخروج
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <param name="sessionToken">رمز الجلسة</param>
        Public Async Function LogoutAsync(userId As Integer, sessionToken As String) As Task
            Try
                ' إنهاء الجلسة النشطة
                Dim session = Await _context.UserSessions _
                    .FirstOrDefaultAsync(Function(s) s.UserId = userId AndAlso s.SessionToken = sessionToken AndAlso s.IsActive)

                If session IsNot Nothing Then
                    session.IsActive = False
                    session.EndTime = DateTime.Now
                    Await _context.SaveChangesAsync()
                End If

                ' تسجيل عملية تسجيل الخروج
                Await LogUserActionAsync(userId, "تسجيل خروج", "Users", userId.ToString())

            Catch ex As Exception
                ' تجاهل أخطاء تسجيل الخروج
            End Try
        End Function

        ''' <summary>
        ''' الحصول على جميع المستخدمين
        ''' </summary>
        ''' <returns>قائمة المستخدمين</returns>
        Public Async Function GetAllUsersAsync() As Task(Of List(Of User))
            Try
                Return Await _context.Users.Include(Function(u) u.UserGroup).Include(Function(u) u.Department) _
                    .OrderBy(Function(u) u.AccountName) _
                    .ToListAsync()
            Catch ex As Exception
                Return New List(Of User)()
            End Try
        End Function

        ''' <summary>
        ''' الحصول على المستخدمين النشطين
        ''' </summary>
        ''' <returns>قائمة المستخدمين النشطين</returns>
        Public Async Function GetActiveUsersAsync() As Task(Of List(Of User))
            Try
                Return Await _context.Users.Include(Function(u) u.UserGroup).Include(Function(u) u.Department) _
                    .Where(Function(u) u.IsActive) _
                    .OrderBy(Function(u) u.AccountName) _
                    .ToListAsync()
            Catch ex As Exception
                Return New List(Of User)()
            End Try
        End Function

        ''' <summary>
        ''' إضافة مستخدم جديد
        ''' </summary>
        ''' <param name="user">بيانات المستخدم</param>
        ''' <returns>True إذا تم الإضافة بنجاح</returns>
        Public Async Function AddUserAsync(user As User) As Task(Of Boolean)
            Try
                ' التحقق من عدم وجود مستخدم بنفس اسم المستخدم
                Dim existingUser = Await _context.Users _
                    .FirstOrDefaultAsync(Function(u) u.Username = user.Username)

                If existingUser IsNot Nothing Then
                    Return False
                End If

                ' تشفير كلمة المرور
                user.PasswordHash = User.HashPassword(user.PasswordHash)
                user.CreatedDate = DateTime.Now
                user.ModifiedDate = DateTime.Now

                _context.Users.Add(user)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Await LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "إضافة", "Users", user.UserId.ToString())
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تحديث بيانات المستخدم
        ''' </summary>
        ''' <param name="user">بيانات المستخدم</param>
        ''' <returns>True إذا تم التحديث بنجاح</returns>
        Public Async Function UpdateUserAsync(user As User) As Task(Of Boolean)
            Try
                Dim existingUser = Await _context.Users.FindAsync(user.UserId)
                If existingUser Is Nothing Then
                    Return False
                End If

                ' حفظ القيم القديمة للتسجيل
                Dim oldValues = $"Username: {existingUser.Username}, AccountName: {existingUser.AccountName}"

                ' تحديث البيانات
                existingUser.AccountName = user.AccountName
                existingUser.AccountType = user.AccountType
                existingUser.UserGroupId = user.UserGroupId
                existingUser.DepartmentId = user.DepartmentId
                existingUser.IsActive = user.IsActive
                existingUser.ModifiedDate = DateTime.Now
                existingUser.ModifiedBy = CurrentUserService.CurrentUserName

                ' تحديث كلمة المرور إذا تم تغييرها
                If Not String.IsNullOrEmpty(user.PasswordHash) AndAlso user.PasswordHash <> existingUser.PasswordHash Then
                    existingUser.PasswordHash = User.HashPassword(user.PasswordHash)
                End If

                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                Dim newValues = $"Username: {existingUser.Username}, AccountName: {existingUser.AccountName}"
                If CurrentUserService.CurrentUserId.HasValue Then
                    Await LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "تعديل", "Users", user.UserId.ToString(), oldValues, newValues)
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' حذف المستخدم
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <returns>True إذا تم الحذف بنجاح</returns>
        Public Async Function DeleteUserAsync(userId As Integer) As Task(Of Boolean)
            Try
                Dim user = Await _context.Users.FindAsync(userId)
                If user Is Nothing Then
                    Return False
                End If

                ' منع حذف المستخدم الحالي
                If CurrentUserService.CurrentUserId = userId Then
                    Return False
                End If

                ' حفظ البيانات للتسجيل
                Dim userInfo = $"Username: {user.Username}, AccountName: {user.AccountName}"

                _context.Users.Remove(user)
                Await _context.SaveChangesAsync()

                ' تسجيل العملية
                If CurrentUserService.CurrentUserId.HasValue Then
                    Await LogUserActionAsync(CurrentUserService.CurrentUserId.Value, "حذف", "Users", userId.ToString(), userInfo)
                End If

                Return True

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' التحقق من صحة جلسة المستخدم
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <param name="sessionToken">رمز الجلسة</param>
        ''' <returns>True إذا كانت الجلسة صحيحة</returns>
        Public Async Function ValidateSessionAsync(userId As Integer, sessionToken As String) As Task(Of Boolean)
            Try
                Dim session = Await _context.UserSessions _
                    .FirstOrDefaultAsync(Function(s) s.UserId = userId AndAlso s.SessionToken = sessionToken AndAlso s.IsActive)

                Return session IsNot Nothing

            Catch ex As Exception
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            _context?.Dispose()
        End Sub

    End Class

End Namespace
