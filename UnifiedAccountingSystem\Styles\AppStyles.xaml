<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Additional Custom Styles -->
    
    <!-- Sub Menu Button Style -->
    <Style x:Key="SubMenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="Padding" Value="30,0,20,0"/>
        <Setter Property="Margin" Value="0,1"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Foreground" Value="#555555"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F0F8FF"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Tree View Styles -->
    <Style x:Key="TreeViewStyle" TargetType="TreeView">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="5"/>
    </Style>

    <Style x:Key="TreeViewItemStyle" TargetType="TreeViewItem">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Padding" Value="5,2"/>
        <Setter Property="Margin" Value="0,1"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#E3F2FD"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Dialog Styles -->
    <Style x:Key="DialogStyle" TargetType="Window">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="WindowStartupLocation" Value="CenterOwner"/>
        <Setter Property="ResizeMode" Value="NoResize"/>
        <Setter Property="ShowInTaskbar" Value="False"/>
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
    </Style>

    <!-- Form Styles -->
    <Style x:Key="FormGridStyle" TargetType="Grid">
        <Setter Property="Margin" Value="20"/>
    </Style>

    <Style x:Key="FormRowStyle" TargetType="RowDefinition">
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="MinHeight" Value="50"/>
    </Style>

    <!-- Validation Styles -->
    <Style x:Key="ValidationTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource InputStyle}">
        <Style.Triggers>
            <Trigger Property="Validation.HasError" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Loading Styles -->
    <Style x:Key="LoadingStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignCircularProgressBar}">
        <Setter Property="Width" Value="50"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="IsIndeterminate" Value="True"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- Message Styles -->
    <Style x:Key="SuccessMessageStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelStyle}">
        <Setter Property="Foreground" Value="Green"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="ErrorMessageStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelStyle}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="WarningMessageStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelStyle}">
        <Setter Property="Foreground" Value="Orange"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Icon Styles -->
    <Style x:Key="IconStyle" TargetType="materialDesign:PackIcon">
        <Setter Property="Width" Value="20"/>
        <Setter Property="Height" Value="20"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- Toolbar Styles -->
    <Style x:Key="ToolbarStyle" TargetType="ToolBar">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="10,5"/>
    </Style>

    <!-- Search Box Style -->
    <Style x:Key="SearchBoxStyle" TargetType="TextBox" BasedOn="{StaticResource InputStyle}">
        <Setter Property="materialDesign:HintAssist.Hint" Value="البحث..."/>
        <Setter Property="materialDesign:TextFieldAssist.HasLeadingIcon" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.LeadingIcon" Value="Magnify"/>
        <Setter Property="Width" Value="250"/>
        <Setter Property="Margin" Value="10,0"/>
    </Style>

    <!-- Expander Style -->
    <Style x:Key="ExpanderStyle" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Margin" Value="0,2"/>
        <Setter Property="Background" Value="Transparent"/>
    </Style>

    <!-- Tab Control Style -->
    <Style x:Key="TabControlStyle" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>

    <!-- Group Box Style -->
    <Style x:Key="GroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- Numeric TextBox Style -->
    <Style x:Key="NumericTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource InputStyle}">
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="LeftToRight"/>
    </Style>

    <!-- Currency TextBox Style -->
    <Style x:Key="CurrencyTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource NumericTextBoxStyle}">
        <Setter Property="materialDesign:HintAssist.Suffix" Value="د.ع"/>
    </Style>

    <!-- Percentage TextBox Style -->
    <Style x:Key="PercentageTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource NumericTextBoxStyle}">
        <Setter Property="materialDesign:HintAssist.Suffix" Value="%"/>
    </Style>

</ResourceDictionary>
