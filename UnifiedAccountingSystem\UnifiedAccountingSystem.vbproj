<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B1C2D3E4-F5G6-7890-HIJK-LMNOPQRSTUVW}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>UnifiedAccountingSystem.Application</StartupObject>
    <RootNamespace>UnifiedAccountingSystem</RootNamespace>
    <AssemblyName>UnifiedAccountingSystem</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Custom</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>UnifiedAccountingSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>UnifiedAccountingSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=6.2.10.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.6.2.10\lib\net462\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="MaterialDesignThemes.Wpf, Version=4.9.0.0, Culture=neutral, PublicKeyToken=df2a72020bd7962a, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignThemes.4.9.0\lib\net462\MaterialDesignThemes.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="MaterialDesignColors, Version=2.1.4.0, Culture=neutral, PublicKeyToken=df2a72020bd7962a, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignColors.2.1.4\lib\net462\MaterialDesignColors.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
    <Import Include="System.Windows" />
    <Import Include="System.Windows.Controls" />
    <Import Include="System.Windows.Data" />
    <Import Include="System.Windows.Documents" />
    <Import Include="System.Windows.Input" />
    <Import Include="System.Windows.Media" />
    <Import Include="System.Windows.Media.Imaging" />
    <Import Include="System.Windows.Navigation" />
    <Import Include="System.Windows.Shapes" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="Application.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Application.xaml.vb">
      <DependentUpon>Application.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <!-- Views Code-Behind -->
    <Compile Include="Views\MainWindow.xaml.vb">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\LoginWindow.xaml.vb">
      <DependentUpon>LoginWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ChartOfAccountsWindow.xaml.vb">
      <DependentUpon>ChartOfAccountsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ChartOfAccountsSetupDialog.xaml.vb">
      <DependentUpon>ChartOfAccountsSetupDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\AddEditAccountDialog.xaml.vb">
      <DependentUpon>AddEditAccountDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\TrialBalanceWindow.xaml.vb">
      <DependentUpon>TrialBalanceWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ExpenseManagementWindow.xaml.vb">
      <DependentUpon>ExpenseManagementWindow.xaml</DependentUpon>
    </Compile>

    <!-- ViewModels -->
    <Compile Include="ViewModels\MainViewModel.vb" />
    <Compile Include="ViewModels\MainWindowViewModel.vb" />
    <Compile Include="ViewModels\LoginViewModel.vb" />
    <Compile Include="ViewModels\ChartOfAccountsViewModel.vb" />
    <Compile Include="ViewModels\ChartOfAccountsSetupViewModel.vb" />
    <Compile Include="ViewModels\AddEditAccountViewModel.vb" />
    <Compile Include="ViewModels\TrialBalanceViewModel.vb" />
    <Compile Include="ViewModels\ExpenseManagementViewModel.vb" />

    <!-- Models -->
    <Compile Include="Models\UserModels.vb" />
    <Compile Include="Models\OrganizationModels.vb" />
    <Compile Include="Models\AccountingModels.vb" />
    <Compile Include="Models\EmployeeModels.vb" />
    <Compile Include="Models\PayrollModels.vb" />
    <Compile Include="Models\ExpenseModels.vb" />

    <!-- Data -->
    <Compile Include="Data\AccountingDbContext.vb" />

    <!-- Services -->
    <Compile Include="Services\UserService.vb" />
    <Compile Include="Services\EmployeeService.vb" />
    <Compile Include="Services\PayrollService.vb" />
    <Compile Include="Services\AccountingService.vb" />
    <Compile Include="Services\ReportService.vb" />
    <Compile Include="Services\DatabaseService.vb" />
    <Compile Include="Services\ChartOfAccountsService.vb" />
    <Compile Include="Services\ChartOfAccountsImportService.vb" />
    <Compile Include="Services\DepartmentService.vb" />
    <Compile Include="Services\ExpenseService.vb" />

    <!-- Utilities -->
    <Compile Include="Utilities\RelayCommand.vb" />
    <Compile Include="Utilities\ViewModelBase.vb" />
    <Compile Include="Utilities\Converters.vb" />
  </ItemGroup>
  <ItemGroup>
    <!-- XAML Pages -->
    <Page Include="Views\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\LoginWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ChartOfAccountsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ChartOfAccountsSetupDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddEditAccountDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\TrialBalanceWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ExpenseManagementWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ExpenseItemDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\BankAccountSetupDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Styles\AppStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Icons\" />
    <Folder Include="UserControls\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>
