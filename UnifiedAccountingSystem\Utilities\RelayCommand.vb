Imports System.Windows.Input

Namespace Utilities

    ''' <summary>
    ''' تنفيذ ICommand للاستخدام في MVVM
    ''' </summary>
    Public Class RelayCommand
        Implements ICommand

        Private ReadOnly _execute As Action
        Private ReadOnly _canExecute As Func(Of Boolean)

        ''' <summary>
        ''' منشئ الأمر
        ''' </summary>
        ''' <param name="execute">الإجراء المطلوب تنفيذه</param>
        ''' <param name="canExecute">دالة التحقق من إمكانية التنفيذ</param>
        Public Sub New(execute As Action, Optional canExecute As Func(Of Boolean) = Nothing)
            _execute = execute ?? throw New ArgumentNullException(NameOf(execute))
            _canExecute = canExecute
        End Sub

        ''' <summary>
        ''' حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Event CanExecuteChanged As EventHandler Implements ICommand.CanExecuteChanged

        ''' <summary>
        ''' التحقق من إمكانية تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        ''' <returns>True إذا كان يمكن تنفيذ الأمر</returns>
        Public Function CanExecute(parameter As Object) As Boolean Implements ICommand.CanExecute
            Return _canExecute?.Invoke() <> False
        End Function

        ''' <summary>
        ''' تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        Public Sub Execute(parameter As Object) Implements ICommand.Execute
            _execute()
        End Sub

        ''' <summary>
        ''' إثارة حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Sub RaiseCanExecuteChanged()
            RaiseEvent CanExecuteChanged(Me, EventArgs.Empty)
        End Sub

    End Class

    ''' <summary>
    ''' تنفيذ ICommand مع معامل
    ''' </summary>
    ''' <typeparam name="T">نوع المعامل</typeparam>
    Public Class RelayCommand(Of T)
        Implements ICommand

        Private ReadOnly _execute As Action(Of T)
        Private ReadOnly _canExecute As Func(Of T, Boolean)

        ''' <summary>
        ''' منشئ الأمر مع معامل
        ''' </summary>
        ''' <param name="execute">الإجراء المطلوب تنفيذه</param>
        ''' <param name="canExecute">دالة التحقق من إمكانية التنفيذ</param>
        Public Sub New(execute As Action(Of T), Optional canExecute As Func(Of T, Boolean) = Nothing)
            _execute = execute ?? throw New ArgumentNullException(NameOf(execute))
            _canExecute = canExecute
        End Sub

        ''' <summary>
        ''' حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Event CanExecuteChanged As EventHandler Implements ICommand.CanExecuteChanged

        ''' <summary>
        ''' التحقق من إمكانية تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        ''' <returns>True إذا كان يمكن تنفيذ الأمر</returns>
        Public Function CanExecute(parameter As Object) As Boolean Implements ICommand.CanExecute
            If _canExecute Is Nothing Then
                Return True
            End If

            If parameter Is Nothing AndAlso GetType(T).IsValueType Then
                Return _canExecute(Nothing)
            End If

            If TypeOf parameter Is T Then
                Return _canExecute(DirectCast(parameter, T))
            End If

            Return False
        End Function

        ''' <summary>
        ''' تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        Public Sub Execute(parameter As Object) Implements ICommand.Execute
            If parameter Is Nothing AndAlso GetType(T).IsValueType Then
                _execute(Nothing)
                Return
            End If

            If TypeOf parameter Is T Then
                _execute(DirectCast(parameter, T))
            End If
        End Sub

        ''' <summary>
        ''' إثارة حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Sub RaiseCanExecuteChanged()
            RaiseEvent CanExecuteChanged(Me, EventArgs.Empty)
        End Sub

    End Class

    ''' <summary>
    ''' أمر غير متزامن
    ''' </summary>
    Public Class AsyncRelayCommand
        Implements ICommand

        Private ReadOnly _execute As Func(Of Task)
        Private ReadOnly _canExecute As Func(Of Boolean)
        Private _isExecuting As Boolean

        ''' <summary>
        ''' منشئ الأمر غير المتزامن
        ''' </summary>
        ''' <param name="execute">الإجراء غير المتزامن</param>
        ''' <param name="canExecute">دالة التحقق من إمكانية التنفيذ</param>
        Public Sub New(execute As Func(Of Task), Optional canExecute As Func(Of Boolean) = Nothing)
            _execute = execute ?? throw New ArgumentNullException(NameOf(execute))
            _canExecute = canExecute
        End Sub

        ''' <summary>
        ''' حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Event CanExecuteChanged As EventHandler Implements ICommand.CanExecuteChanged

        ''' <summary>
        ''' التحقق من إمكانية تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        ''' <returns>True إذا كان يمكن تنفيذ الأمر</returns>
        Public Function CanExecute(parameter As Object) As Boolean Implements ICommand.CanExecute
            Return Not _isExecuting AndAlso (_canExecute?.Invoke() <> False)
        End Function

        ''' <summary>
        ''' تنفيذ الأمر غير المتزامن
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        Public Async Sub Execute(parameter As Object) Implements ICommand.Execute
            If _isExecuting Then Return

            _isExecuting = True
            RaiseCanExecuteChanged()

            Try
                Await _execute()
            Finally
                _isExecuting = False
                RaiseCanExecuteChanged()
            End Try
        End Sub

        ''' <summary>
        ''' إثارة حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Sub RaiseCanExecuteChanged()
            RaiseEvent CanExecuteChanged(Me, EventArgs.Empty)
        End Sub

        ''' <summary>
        ''' حالة التنفيذ
        ''' </summary>
        Public ReadOnly Property IsExecuting As Boolean
            Get
                Return _isExecuting
            End Get
        End Property

    End Class

    ''' <summary>
    ''' أمر غير متزامن مع معامل
    ''' </summary>
    ''' <typeparam name="T">نوع المعامل</typeparam>
    Public Class AsyncRelayCommand(Of T)
        Implements ICommand

        Private ReadOnly _execute As Func(Of T, Task)
        Private ReadOnly _canExecute As Func(Of T, Boolean)
        Private _isExecuting As Boolean

        ''' <summary>
        ''' منشئ الأمر غير المتزامن مع معامل
        ''' </summary>
        ''' <param name="execute">الإجراء غير المتزامن</param>
        ''' <param name="canExecute">دالة التحقق من إمكانية التنفيذ</param>
        Public Sub New(execute As Func(Of T, Task), Optional canExecute As Func(Of T, Boolean) = Nothing)
            _execute = execute ?? throw New ArgumentNullException(NameOf(execute))
            _canExecute = canExecute
        End Sub

        ''' <summary>
        ''' حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Event CanExecuteChanged As EventHandler Implements ICommand.CanExecuteChanged

        ''' <summary>
        ''' التحقق من إمكانية تنفيذ الأمر
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        ''' <returns>True إذا كان يمكن تنفيذ الأمر</returns>
        Public Function CanExecute(parameter As Object) As Boolean Implements ICommand.CanExecute
            If _isExecuting Then Return False

            If _canExecute Is Nothing Then Return True

            If parameter Is Nothing AndAlso GetType(T).IsValueType Then
                Return _canExecute(Nothing)
            End If

            If TypeOf parameter Is T Then
                Return _canExecute(DirectCast(parameter, T))
            End If

            Return False
        End Function

        ''' <summary>
        ''' تنفيذ الأمر غير المتزامن
        ''' </summary>
        ''' <param name="parameter">المعامل</param>
        Public Async Sub Execute(parameter As Object) Implements ICommand.Execute
            If _isExecuting Then Return

            _isExecuting = True
            RaiseCanExecuteChanged()

            Try
                If parameter Is Nothing AndAlso GetType(T).IsValueType Then
                    Await _execute(Nothing)
                ElseIf TypeOf parameter Is T Then
                    Await _execute(DirectCast(parameter, T))
                End If
            Finally
                _isExecuting = False
                RaiseCanExecuteChanged()
            End Try
        End Sub

        ''' <summary>
        ''' إثارة حدث تغيير إمكانية التنفيذ
        ''' </summary>
        Public Sub RaiseCanExecuteChanged()
            RaiseEvent CanExecuteChanged(Me, EventArgs.Empty)
        End Sub

        ''' <summary>
        ''' حالة التنفيذ
        ''' </summary>
        Public ReadOnly Property IsExecuting As Boolean
            Get
                Return _isExecuting
            End Get
        End Property

    End Class

End Namespace
