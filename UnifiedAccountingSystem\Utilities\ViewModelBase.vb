Imports System.ComponentModel
Imports System.Runtime.CompilerServices

Namespace Utilities

    ''' <summary>
    ''' الفئة الأساسية لجميع ViewModels
    ''' </summary>
    Public MustInherit Class ViewModelBase
        Implements INotifyPropertyChanged

        ''' <summary>
        ''' حدث تغيير الخاصية
        ''' </summary>
        Public Event PropertyChanged As PropertyChangedEventHandler Implements INotifyPropertyChanged.PropertyChanged

        ''' <summary>
        ''' إثارة حدث تغيير الخاصية
        ''' </summary>
        ''' <param name="propertyName">اسم الخاصية</param>
        Protected Overridable Sub OnPropertyChanged(<CallerMemberName> Optional propertyName As String = Nothing)
            RaiseEvent PropertyChanged(Me, New PropertyChangedEventArgs(propertyName))
        End Sub

        ''' <summary>
        ''' تعيين قيمة الخاصية مع إثارة الحدث
        ''' </summary>
        ''' <typeparam name="T">نوع البيانات</typeparam>
        ''' <param name="field">المتغير المرجعي</param>
        ''' <param name="value">القيمة الجديدة</param>
        ''' <param name="propertyName">اسم الخاصية</param>
        ''' <returns>True إذا تم تغيير القيمة</returns>
        Protected Function SetProperty(Of T)(ByRef field As T, value As T, <CallerMemberName> Optional propertyName As String = Nothing) As Boolean
            If EqualityComparer(Of T).Default.Equals(field, value) Then
                Return False
            End If

            field = value
            OnPropertyChanged(propertyName)
            Return True
        End Function

        ''' <summary>
        ''' تعيين قيمة الخاصية مع تنفيذ إجراء إضافي
        ''' </summary>
        ''' <typeparam name="T">نوع البيانات</typeparam>
        ''' <param name="field">المتغير المرجعي</param>
        ''' <param name="value">القيمة الجديدة</param>
        ''' <param name="onChanged">الإجراء المطلوب تنفيذه عند التغيير</param>
        ''' <param name="propertyName">اسم الخاصية</param>
        ''' <returns>True إذا تم تغيير القيمة</returns>
        Protected Function SetProperty(Of T)(ByRef field As T, value As T, onChanged As Action, <CallerMemberName> Optional propertyName As String = Nothing) As Boolean
            If SetProperty(field, value, propertyName) Then
                onChanged?.Invoke()
                Return True
            End If
            Return False
        End Function

        ''' <summary>
        ''' التحقق من صحة البيانات
        ''' </summary>
        ''' <returns>True إذا كانت البيانات صحيحة</returns>
        Public Overridable Function Validate() As Boolean
            Return True
        End Function

        ''' <summary>
        ''' رسائل الأخطاء
        ''' </summary>
        Private _errors As New Dictionary(Of String, List(Of String))

        ''' <summary>
        ''' إضافة خطأ للخاصية
        ''' </summary>
        ''' <param name="propertyName">اسم الخاصية</param>
        ''' <param name="error">رسالة الخطأ</param>
        Protected Sub AddError(propertyName As String, [error] As String)
            If Not _errors.ContainsKey(propertyName) Then
                _errors(propertyName) = New List(Of String)()
            End If

            If Not _errors(propertyName).Contains([error]) Then
                _errors(propertyName).Add([error])
                OnPropertyChanged("HasErrors")
                OnPropertyChanged("Errors")
            End If
        End Sub

        ''' <summary>
        ''' إزالة خطأ من الخاصية
        ''' </summary>
        ''' <param name="propertyName">اسم الخاصية</param>
        ''' <param name="error">رسالة الخطأ</param>
        Protected Sub RemoveError(propertyName As String, [error] As String)
            If _errors.ContainsKey(propertyName) AndAlso _errors(propertyName).Contains([error]) Then
                _errors(propertyName).Remove([error])
                If _errors(propertyName).Count = 0 Then
                    _errors.Remove(propertyName)
                End If
                OnPropertyChanged("HasErrors")
                OnPropertyChanged("Errors")
            End If
        End Sub

        ''' <summary>
        ''' مسح جميع الأخطاء للخاصية
        ''' </summary>
        ''' <param name="propertyName">اسم الخاصية</param>
        Protected Sub ClearErrors(propertyName As String)
            If _errors.ContainsKey(propertyName) Then
                _errors.Remove(propertyName)
                OnPropertyChanged("HasErrors")
                OnPropertyChanged("Errors")
            End If
        End Sub

        ''' <summary>
        ''' مسح جميع الأخطاء
        ''' </summary>
        Protected Sub ClearAllErrors()
            _errors.Clear()
            OnPropertyChanged("HasErrors")
            OnPropertyChanged("Errors")
        End Sub

        ''' <summary>
        ''' التحقق من وجود أخطاء
        ''' </summary>
        Public ReadOnly Property HasErrors As Boolean
            Get
                Return _errors.Count > 0
            End Get
        End Property

        ''' <summary>
        ''' قائمة الأخطاء
        ''' </summary>
        Public ReadOnly Property Errors As Dictionary(Of String, List(Of String))
            Get
                Return _errors
            End Get
        End Property

        ''' <summary>
        ''' الحصول على أخطاء خاصية معينة
        ''' </summary>
        ''' <param name="propertyName">اسم الخاصية</param>
        ''' <returns>قائمة الأخطاء</returns>
        Public Function GetErrors(propertyName As String) As List(Of String)
            If _errors.ContainsKey(propertyName) Then
                Return _errors(propertyName)
            End If
            Return New List(Of String)()
        End Function

        ''' <summary>
        ''' حالة التحميل
        ''' </summary>
        Private _isLoading As Boolean
        Public Property IsLoading As Boolean
            Get
                Return _isLoading
            End Get
            Set(value As Boolean)
                SetProperty(_isLoading, value)
            End Set
        End Property

        ''' <summary>
        ''' حالة الانشغال
        ''' </summary>
        Private _isBusy As Boolean
        Public Property IsBusy As Boolean
            Get
                Return _isBusy
            End Get
            Set(value As Boolean)
                SetProperty(_isBusy, value)
            End Set
        End Property

        ''' <summary>
        ''' رسالة الحالة
        ''' </summary>
        Private _statusMessage As String
        Public Property StatusMessage As String
            Get
                Return _statusMessage
            End Get
            Set(value As String)
                SetProperty(_statusMessage, value)
            End Set
        End Property

        ''' <summary>
        ''' عنوان الصفحة
        ''' </summary>
        Private _title As String
        Public Property Title As String
            Get
                Return _title
            End Get
            Set(value As String)
                SetProperty(_title, value)
            End Set
        End Property

        ''' <summary>
        ''' تنفيذ إجراء مع معالجة الأخطاء
        ''' </summary>
        ''' <param name="action">الإجراء المطلوب تنفيذه</param>
        ''' <param name="errorMessage">رسالة الخطأ الافتراضية</param>
        Protected Async Function ExecuteAsync(action As Func(Of Task), Optional errorMessage As String = "حدث خطأ أثناء تنفيذ العملية") As Task
            Try
                IsLoading = True
                IsBusy = True
                ClearAllErrors()
                Await action()
            Catch ex As Exception
                AddError("General", If(String.IsNullOrEmpty(errorMessage), ex.Message, errorMessage))
                ' يمكن إضافة تسجيل الخطأ هنا
            Finally
                IsLoading = False
                IsBusy = False
            End Try
        End Function

        ''' <summary>
        ''' تنفيذ إجراء مع معالجة الأخطاء وإرجاع نتيجة
        ''' </summary>
        ''' <typeparam name="T">نوع النتيجة</typeparam>
        ''' <param name="func">الدالة المطلوب تنفيذها</param>
        ''' <param name="errorMessage">رسالة الخطأ الافتراضية</param>
        ''' <returns>النتيجة أو القيمة الافتراضية</returns>
        Protected Async Function ExecuteAsync(Of T)(func As Func(Of Task(Of T)), Optional errorMessage As String = "حدث خطأ أثناء تنفيذ العملية") As Task(Of T)
            Try
                IsLoading = True
                IsBusy = True
                ClearAllErrors()
                Return Await func()
            Catch ex As Exception
                AddError("General", If(String.IsNullOrEmpty(errorMessage), ex.Message, errorMessage))
                Return Nothing
            Finally
                IsLoading = False
                IsBusy = False
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Overridable Sub Dispose()
            ' يمكن للفئات المشتقة تنفيذ تنظيف إضافي
        End Sub

    End Class

End Namespace
