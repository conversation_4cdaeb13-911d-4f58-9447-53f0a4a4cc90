Imports System.Collections.ObjectModel
Imports System.Windows.Input
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لنافذة إضافة/تعديل الحسابات
    ''' </summary>
    Public Class AddEditAccountViewModel
        Inherits ViewModelBase

        Private ReadOnly _accountService As ChartOfAccountsService
        Private _isEditMode As Boolean
        Private _originalAccount As ChartOfAccount

        ' الخصائص الأساسية
        Private _accountCode As String
        Private _accountName As String
        Private _selectedAccountType As String
        Private _selectedAccountNature As String
        Private _selectedParentAccount As ParentAccountItem
        Private _isActive As Boolean = True
        Private _isAnalytical As Boolean = False
        Private _notes As String
        Private _createdDate As DateTime
        Private _createdBy As String

        ' المجموعات
        Private _accountTypes As ObservableCollection(Of String)
        Private _accountNatures As ObservableCollection(Of String)
        Private _parentAccounts As ObservableCollection(Of ParentAccountItem)
        Private _validationErrors As ObservableCollection(Of String)

        Public Sub New(Optional account As ChartOfAccount = Nothing)
            _accountService = New ChartOfAccountsService()

            ' تهيئة المجموعات
            InitializeCollections()

            ' تحديد وضع التحرير
            _isEditMode = account IsNot Nothing
            _originalAccount = account

            If _isEditMode Then
                LoadAccountData(account)
            Else
                SetDefaultValues()
            End If

            ' إنشاء الأوامر
            InitializeCommands()

            ' تحميل البيانات الأولية
            LoadInitialDataAsync()
        End Sub

        #Region "Properties"

        Public ReadOnly Property WindowTitle As String
            Get
                Return If(_isEditMode, "تعديل حساب", "إضافة حساب جديد")
            End Get
        End Property

        Public ReadOnly Property IsEditMode As Boolean
            Get
                Return _isEditMode
            End Get
        End Property

        Public ReadOnly Property CanEditAccountCode As Boolean
            Get
                Return Not _isEditMode
            End Get
        End Property

        Public Property AccountCode As String
            Get
                Return _accountCode
            End Get
            Set(value As String)
                If SetProperty(_accountCode, value) Then
                    UpdateAccountCodePreview()
                    ValidateForm()
                End If
            End Set
        End Property

        Public Property AccountName As String
            Get
                Return _accountName
            End Get
            Set(value As String)
                If SetProperty(_accountName, value) Then
                    ValidateForm()
                End If
            End Set
        End Property

        Public Property SelectedAccountType As String
            Get
                Return _selectedAccountType
            End Get
            Set(value As String)
                If SetProperty(_selectedAccountType, value) Then
                    UpdateAccountNatureBasedOnType()
                    ValidateForm()
                End If
            End Set
        End Property

        Public Property SelectedAccountNature As String
            Get
                Return _selectedAccountNature
            End Get
            Set(value As String)
                If SetProperty(_selectedAccountNature, value) Then
                    ValidateForm()
                End If
            End Set
        End Property

        Public Property SelectedParentAccount As ParentAccountItem
            Get
                Return _selectedParentAccount
            End Get
            Set(value As ParentAccountItem)
                If SetProperty(_selectedParentAccount, value) Then
                    ValidateForm()
                End If
            End Set
        End Property

        Public Property IsActive As Boolean
            Get
                Return _isActive
            End Get
            Set(value As Boolean)
                SetProperty(_isActive, value)
            End Set
        End Property

        Public Property IsAnalytical As Boolean
            Get
                Return _isAnalytical
            End Get
            Set(value As Boolean)
                SetProperty(_isAnalytical, value)
            End Set
        End Property

        Public Property Notes As String
            Get
                Return _notes
            End Get
            Set(value As String)
                SetProperty(_notes, value)
            End Set
        End Property

        Public Property CreatedDate As DateTime
            Get
                Return _createdDate
            End Get
            Set(value As DateTime)
                SetProperty(_createdDate, value)
            End Set
        End Property

        Public Property CreatedBy As String
            Get
                Return _createdBy
            End Get
            Set(value As String)
                SetProperty(_createdBy, value)
            End Set
        End Property

        ' المجموعات
        Public Property AccountTypes As ObservableCollection(Of String)
            Get
                Return _accountTypes
            End Get
            Set(value As ObservableCollection(Of String))
                SetProperty(_accountTypes, value)
            End Set
        End Property

        Public Property AccountNatures As ObservableCollection(Of String)
            Get
                Return _accountNatures
            End Get
            Set(value As ObservableCollection(Of String))
                SetProperty(_accountNatures, value)
            End Set
        End Property

        Public Property ParentAccounts As ObservableCollection(Of ParentAccountItem)
            Get
                Return _parentAccounts
            End Get
            Set(value As ObservableCollection(Of ParentAccountItem))
                SetProperty(_parentAccounts, value)
            End Set
        End Property

        Public Property ValidationErrors As ObservableCollection(Of String)
            Get
                Return _validationErrors
            End Get
            Set(value As ObservableCollection(Of String))
                SetProperty(_validationErrors, value)
                OnPropertyChanged(NameOf(HasErrors))
            End Set
        End Property

        Public ReadOnly Property HasErrors As Boolean
            Get
                Return ValidationErrors IsNot Nothing AndAlso ValidationErrors.Count > 0
            End Get
        End Property

        Public ReadOnly Property CanSave As Boolean
            Get
                Return Not HasErrors AndAlso Not String.IsNullOrWhiteSpace(AccountCode) AndAlso Not String.IsNullOrWhiteSpace(AccountName)
            End Get
        End Property

        ' خصائص معاينة رمز الحساب
        Public ReadOnly Property ShowAccountCodePreview As Boolean
            Get
                Return Not String.IsNullOrEmpty(AccountCode)
            End Get
        End Property

        Public ReadOnly Property AccountLevel1 As String
            Get
                Return If(AccountCode?.Length >= 1, AccountCode.Substring(0, 1), "")
            End Get
        End Property

        Public ReadOnly Property AccountLevel2 As String
            Get
                Return If(AccountCode?.Length >= 2, AccountCode.Substring(1, 1), "")
            End Get
        End Property

        Public ReadOnly Property AccountLevel3 As String
            Get
                Return If(AccountCode?.Length >= 3, AccountCode.Substring(2, 1), "")
            End Get
        End Property

        Public ReadOnly Property AccountLevel4 As String
            Get
                Return If(AccountCode?.Length >= 4, AccountCode.Substring(3, 1), "")
            End Get
        End Property

        Public ReadOnly Property AccountLevel5 As String
            Get
                Return If(AccountCode?.Length >= 5, AccountCode.Substring(4, 1), "")
            End Get
        End Property

        Public ReadOnly Property AccountLevel6 As String
            Get
                Return If(AccountCode?.Length >= 6, AccountCode.Substring(5, 1), "")
            End Get
        End Property

        #End Region

        #Region "Commands"

        Public Property SaveCommand As ICommand
        Public Property CancelCommand As ICommand
        Public Property ApplyCommand As ICommand

        Private Sub InitializeCommands()
            SaveCommand = New AsyncRelayCommand(AddressOf SaveAsync, Function() CanSave)
            CancelCommand = New RelayCommand(AddressOf Cancel)
            ApplyCommand = New AsyncRelayCommand(AddressOf ApplyAsync, Function() CanSave)
        End Sub

        #End Region

        #Region "Methods"

        Private Sub InitializeCollections()
            AccountTypes = New ObservableCollection(Of String) From {
                "إيرادات", "نفقات", "موجودات", "مطلوبات", "حقوق الملكية", "عام"
            }

            AccountNatures = New ObservableCollection(Of String) From {
                "مدين", "دائن"
            }

            ParentAccounts = New ObservableCollection(Of ParentAccountItem)()
            ValidationErrors = New ObservableCollection(Of String)()
        End Sub

        Private Sub SetDefaultValues()
            SelectedAccountType = "عام"
            SelectedAccountNature = "مدين"
            CreatedDate = DateTime.Now
            CreatedBy = CurrentUserService.CurrentUserName
        End Sub

        Private Sub LoadAccountData(account As ChartOfAccount)
            AccountCode = account.AccountCode
            AccountName = account.AccountName
            SelectedAccountType = account.AccountType
            SelectedAccountNature = account.AccountNature
            IsActive = account.IsActive
            IsAnalytical = account.IsAnalytical
            Notes = account.Notes
            CreatedDate = account.CreatedDate
            CreatedBy = account.CreatedBy

            ' تحديد الحساب الأب
            If account.ParentAccountId.HasValue Then
                ' سيتم تحديده بعد تحميل قائمة الحسابات الأب
            End If
        End Sub

        Private Async Function LoadInitialDataAsync() As Task
            Try
                ' تحميل قائمة الحسابات الأب
                Dim accounts = Await _accountService.GetAllAccountsAsync()

                ParentAccounts.Clear()
                ParentAccounts.Add(New ParentAccountItem() With {.AccountId = Nothing, .DisplayText = "-- بدون حساب أب --"})

                For Each account In accounts.Where(Function(a) a.IsActive AndAlso (Not _isEditMode OrElse a.AccountId <> _originalAccount.AccountId))
                    ParentAccounts.Add(New ParentAccountItem() With {
                        .AccountId = account.AccountId,
                        .DisplayText = $"{account.AccountCode} - {account.AccountName}"
                    })
                Next

                ' تحديد الحساب الأب في وضع التحرير
                If _isEditMode AndAlso _originalAccount.ParentAccountId.HasValue Then
                    SelectedParentAccount = ParentAccounts.FirstOrDefault(Function(p) p.AccountId = _originalAccount.ParentAccountId)
                Else
                    SelectedParentAccount = ParentAccounts.FirstOrDefault()
                End If

            Catch ex As Exception
                ' معالجة الخطأ
            End Try
        End Function

        Private Sub UpdateAccountCodePreview()
            OnPropertyChanged(NameOf(ShowAccountCodePreview))
            OnPropertyChanged(NameOf(AccountLevel1))
            OnPropertyChanged(NameOf(AccountLevel2))
            OnPropertyChanged(NameOf(AccountLevel3))
            OnPropertyChanged(NameOf(AccountLevel4))
            OnPropertyChanged(NameOf(AccountLevel5))
            OnPropertyChanged(NameOf(AccountLevel6))
        End Sub

        Private Sub UpdateAccountNatureBasedOnType()
            If Not String.IsNullOrEmpty(SelectedAccountType) AndAlso Not String.IsNullOrEmpty(AccountCode) Then
                Dim firstDigit = AccountCode.Substring(0, 1)

                Select Case firstDigit
                    Case "1" ' الإيرادات
                        SelectedAccountNature = "دائن"
                    Case "2" ' النفقات
                        SelectedAccountNature = "مدين"
                    Case "3" ' الموجودات
                        SelectedAccountNature = "مدين"
                    Case "4" ' المطلوبات
                        SelectedAccountNature = "دائن"
                    Case Else
                        ' تحديد حسب نوع الحساب
                        If SelectedAccountType.Contains("إيراد") Then
                            SelectedAccountNature = "دائن"
                        Else
                            SelectedAccountNature = "مدين"
                        End If
                End Select
            End If
        End Sub

        Private Sub ValidateForm()
            ValidationErrors.Clear()

            ' التحقق من رمز الحساب
            If String.IsNullOrWhiteSpace(AccountCode) Then
                ValidationErrors.Add("رمز الحساب مطلوب")
            ElseIf Not IsValidAccountCode(AccountCode) Then
                ValidationErrors.Add("رمز الحساب يجب أن يحتوي على أرقام فقط")
            End If

            ' التحقق من اسم الحساب
            If String.IsNullOrWhiteSpace(AccountName) Then
                ValidationErrors.Add("اسم الحساب مطلوب")
            End If

            ' التحقق من نوع الحساب
            If String.IsNullOrWhiteSpace(SelectedAccountType) Then
                ValidationErrors.Add("نوع الحساب مطلوب")
            End If

            ' التحقق من طبيعة الحساب
            If String.IsNullOrWhiteSpace(SelectedAccountNature) Then
                ValidationErrors.Add("طبيعة الحساب مطلوبة")
            End If

            OnPropertyChanged(NameOf(CanSave))
        End Sub

        Private Function IsValidAccountCode(code As String) As Boolean
            Return Not String.IsNullOrEmpty(code) AndAlso code.All(AddressOf Char.IsDigit)
        End Function

        Private Async Function SaveAsync() As Task
            If Await SaveAccountAsync() Then
                ' إغلاق النافذة
                CloseDialog(True)
            End If
        End Function

        Private Async Function ApplyAsync() As Task
            Await SaveAccountAsync()
        End Function

        Private Sub Cancel()
            CloseDialog(False)
        End Sub

        Private Async Function SaveAccountAsync() As Task(Of Boolean)
            Try
                Dim account As ChartOfAccount

                If _isEditMode Then
                    account = _originalAccount
                    account.AccountName = AccountName
                    account.AccountType = SelectedAccountType
                    account.AccountNature = SelectedAccountNature
                    account.IsActive = IsActive
                    account.IsAnalytical = IsAnalytical
                    account.Notes = Notes
                    account.ParentAccountId = SelectedParentAccount?.AccountId
                    account.ModifiedDate = DateTime.Now
                    account.ModifiedBy = CurrentUserService.CurrentUserName

                    Return Await _accountService.UpdateAccountAsync(account)
                Else
                    account = New ChartOfAccount() With {
                        .AccountCode = AccountCode,
                        .AccountName = AccountName,
                        .AccountType = SelectedAccountType,
                        .AccountNature = SelectedAccountNature,
                        .IsActive = IsActive,
                        .IsAnalytical = IsAnalytical,
                        .Notes = Notes,
                        .ParentAccountId = SelectedParentAccount?.AccountId
                    }

                    Return Await _accountService.AddAccountAsync(account)
                End If

            Catch ex As Exception
                ValidationErrors.Add($"خطأ في حفظ البيانات: {ex.Message}")
                Return False
            End Try
        End Function

        Private Sub CloseDialog(result As Boolean)
            ' إغلاق النافذة مع النتيجة
            ' سيتم تنفيذ هذا في Code-behind
            RaiseEvent CloseRequested(result)
        End Sub

        ''' <summary>
        ''' حدث طلب إغلاق النافذة
        ''' </summary>
        Public Event CloseRequested(result As Boolean)

        #End Region

        Public Overrides Sub Dispose()
            _accountService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' عنصر الحساب الأب
    ''' </summary>
    Public Class ParentAccountItem
        Public Property AccountId As Integer?
        Public Property DisplayText As String
    End Class

End Namespace
