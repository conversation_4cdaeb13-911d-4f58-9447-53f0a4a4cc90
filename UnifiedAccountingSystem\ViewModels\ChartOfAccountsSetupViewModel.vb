Imports System.Windows.Input
Imports Microsoft.Win32
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لنافذة إعداد دليل الحسابات
    ''' </summary>
    Public Class ChartOfAccountsSetupViewModel
        Inherits ViewModelBase

        Private ReadOnly _accountService As ChartOfAccountsService
        Private ReadOnly _importService As ChartOfAccountsImportService

        Private _isProcessing As Boolean

        Public Sub New()
            _accountService = New ChartOfAccountsService()
            _importService = New ChartOfAccountsImportService()

            ' إنشاء الأوامر
            InitializeCommands()

            Title = "إعداد دليل الحسابات"
        End Sub

        #Region "Properties"

        Public Property IsProcessing As Boolean
            Get
                Return _isProcessing
            End Get
            Set(value As Boolean)
                SetProperty(_isProcessing, value)
            End Set
        End Property

        #End Region

        #Region "Commands"

        Public Property CreateDefaultChartCommand As ICommand
        Public Property ImportFromExcelCommand As ICommand
        Public Property DownloadSampleCommand As ICommand
        Public Property ContinueManuallyCommand As ICommand
        Public Property CloseCommand As ICommand

        Private Sub InitializeCommands()
            CreateDefaultChartCommand = New AsyncRelayCommand(AddressOf CreateDefaultChartAsync)
            ImportFromExcelCommand = New AsyncRelayCommand(AddressOf ImportFromExcelAsync)
            DownloadSampleCommand = New RelayCommand(AddressOf DownloadSample)
            ContinueManuallyCommand = New RelayCommand(AddressOf ContinueManually)
            CloseCommand = New RelayCommand(AddressOf CloseDialog)
        End Sub

        #End Region

        #Region "Methods"

        ''' <summary>
        ''' إنشاء دليل الحسابات الافتراضي
        ''' </summary>
        Private Async Function CreateDefaultChartAsync() As Task
            Await ExecuteAsync(Async Function()
                StatusMessage = "جاري إنشاء دليل الحسابات الافتراضي..."
                IsProcessing = True

                Dim success = Await _accountService.CreateDefaultChartOfAccountsAsync()

                If success Then
                    StatusMessage = "تم إنشاء دليل الحسابات الافتراضي بنجاح"

                    MessageBox.Show(
                        "تم إنشاء دليل الحسابات الافتراضي بنجاح!" & vbNewLine &
                        "يحتوي الدليل على الحسابات الأساسية المطلوبة للنظام المحاسبي العراقي." & vbNewLine &
                        "يمكنك الآن إضافة وتعديل الحسابات حسب احتياجاتك.",
                        "إنشاء ناجح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    )

                    ' إغلاق النافذة
                    CloseDialog()
                Else
                    StatusMessage = "فشل في إنشاء دليل الحسابات الافتراضي"
                    MessageBox.Show(
                        "فشل في إنشاء دليل الحسابات الافتراضي." & vbNewLine &
                        "قد يكون السبب وجود حسابات مسبقاً في النظام.",
                        "خطأ في الإنشاء",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    )
                End If

                IsProcessing = False
                Return success

            End Function, "خطأ في إنشاء دليل الحسابات الافتراضي")
        End Function

        ''' <summary>
        ''' استيراد دليل الحسابات من Excel
        ''' </summary>
        Private Async Function ImportFromExcelAsync() As Task
            Dim openFileDialog As New OpenFileDialog() With {
                .Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                .Title = "اختيار ملف Excel لاستيراد دليل الحسابات"
            }

            If openFileDialog.ShowDialog() = True Then
                Await ExecuteAsync(Async Function()
                    StatusMessage = "جاري استيراد دليل الحسابات من Excel..."
                    IsProcessing = True

                    Dim result = Await _importService.ImportFromExcelAsync(openFileDialog.FileName)

                    If result.Success Then
                        StatusMessage = $"تم استيراد {result.SuccessCount} حساب بنجاح"

                        MessageBox.Show(
                            $"تم الاستيراد بنجاح!" & vbNewLine &
                            $"الحسابات المستوردة: {result.SuccessCount}" & vbNewLine &
                            $"الحسابات المتجاهلة: {result.SkippedCount}" & vbNewLine &
                            "يمكنك الآن مراجعة وتعديل الحسابات من خلال إدارة دليل الحسابات.",
                            "استيراد ناجح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information
                        )

                        ' إغلاق النافذة
                        CloseDialog()
                    Else
                        StatusMessage = $"فشل في الاستيراد: {result.ErrorMessage}"
                        MessageBox.Show(
                            $"فشل في استيراد ملف Excel:" & vbNewLine &
                            result.ErrorMessage,
                            "خطأ في الاستيراد",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        )
                    End If

                    IsProcessing = False
                    Return result.Success

                End Function, "خطأ في استيراد ملف Excel")
            End If
        End Function

        ''' <summary>
        ''' تحميل ملف Excel نموذجي
        ''' </summary>
        Private Sub DownloadSample()
            Dim saveFileDialog As New SaveFileDialog() With {
                .Filter = "Excel Files (*.xlsx)|*.xlsx",
                .Title = "حفظ الملف النموذجي",
                .FileName = "نموذج_دليل_الحسابات.xlsx"
            }

            If saveFileDialog.ShowDialog() = True Then
                Try
                    StatusMessage = "جاري إنشاء الملف النموذجي..."

                    Dim success = _importService.CreateSampleExcelFile(saveFileDialog.FileName)

                    If success Then
                        StatusMessage = "تم إنشاء الملف النموذجي بنجاح"

                        Dim result = MessageBox.Show(
                            "تم إنشاء الملف النموذجي بنجاح!" & vbNewLine &
                            "يحتوي الملف على تنسيق دليل الحسابات وبيانات نموذجية." & vbNewLine &
                            "هل تريد فتح مجلد الملف؟",
                            "إنشاء ناجح",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Information
                        )

                        If result = MessageBoxResult.Yes Then
                            ' فتح مجلد الملف
                            Process.Start("explorer.exe", $"/select,""{saveFileDialog.FileName}""")
                        End If
                    Else
                        StatusMessage = "فشل في إنشاء الملف النموذجي"
                        MessageBox.Show(
                            "فشل في إنشاء الملف النموذجي." & vbNewLine &
                            "تأكد من صحة مسار الحفظ وأن لديك صلاحيات الكتابة.",
                            "خطأ",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        )
                    End If

                Catch ex As Exception
                    StatusMessage = $"خطأ في إنشاء الملف النموذجي: {ex.Message}"
                    MessageBox.Show(
                        $"حدث خطأ أثناء إنشاء الملف النموذجي:" & vbNewLine &
                        ex.Message,
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    )
                End Try
            End If
        End Sub

        ''' <summary>
        ''' المتابعة يدوياً
        ''' </summary>
        Private Sub ContinueManually()
            Dim result = MessageBox.Show(
                "هل أنت متأكد من المتابعة بدون إنشاء دليل حسابات افتراضي؟" & vbNewLine &
                "ستحتاج إلى إضافة جميع الحسابات يدوياً." & vbNewLine &
                "يمكنك دائماً إنشاء الدليل الافتراضي لاحقاً إذا كان النظام فارغاً.",
                "تأكيد المتابعة",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            )

            If result = MessageBoxResult.Yes Then
                StatusMessage = "تم اختيار الإنشاء اليدوي"
                CloseDialog()
            End If
        End Sub

        ''' <summary>
        ''' إغلاق النافذة
        ''' </summary>
        Private Sub CloseDialog()
            RaiseEvent CloseRequested()
        End Sub

        ''' <summary>
        ''' حدث طلب إغلاق النافذة
        ''' </summary>
        Public Event CloseRequested()

        #End Region

        Public Overrides Sub Dispose()
            _accountService?.Dispose()
            _importService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

End Namespace
