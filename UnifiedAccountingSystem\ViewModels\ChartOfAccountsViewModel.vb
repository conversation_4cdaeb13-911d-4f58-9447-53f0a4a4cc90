Imports System.Collections.ObjectModel
Imports System.Windows.Input
Imports System.Windows.Media
Imports Microsoft.Win32
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لدليل الحسابات
    ''' </summary>
    Public Class ChartOfAccountsViewModel
        Inherits ViewModelBase

        Private ReadOnly _accountService As ChartOfAccountsService
        Private ReadOnly _importService As ChartOfAccountsImportService

        ' الخصائص الأساسية
        Private _rootAccounts As ObservableCollection(Of ChartOfAccountViewModel)
        Private _selectedAccount As ChartOfAccountViewModel
        Private _searchText As String
        Private _lastUpdateTime As DateTime

        Public Sub New()
            _accountService = New ChartOfAccountsService()
            _importService = New ChartOfAccountsImportService()

            ' تهيئة المجموعات
            RootAccounts = New ObservableCollection(Of ChartOfAccountViewModel)()

            ' إنشاء الأوامر
            InitializeCommands()

            ' تحميل البيانات الأولية
            LoadAccountsAsync()

            Title = "دليل الحسابات"
        End Sub

        #Region "Properties"

        Public Property RootAccounts As ObservableCollection(Of ChartOfAccountViewModel)
            Get
                Return _rootAccounts
            End Get
            Set(value As ObservableCollection(Of ChartOfAccountViewModel))
                SetProperty(_rootAccounts, value)
                UpdateCounts()
            End Set
        End Property

        Public Property SelectedAccount As ChartOfAccountViewModel
            Get
                Return _selectedAccount
            End Get
            Set(value As ChartOfAccountViewModel)
                If SetProperty(_selectedAccount, value) Then
                    OnPropertyChanged(NameOf(HasSelectedAccount))
                End If
            End Set
        End Property

        Public ReadOnly Property HasSelectedAccount As Boolean
            Get
                Return SelectedAccount IsNot Nothing
            End Get
        End Property

        Public Property SearchText As String
            Get
                Return _searchText
            End Get
            Set(value As String)
                If SetProperty(_searchText, value) Then
                    FilterAccounts()
                End If
            End Set
        End Property

        Public Property LastUpdateTime As DateTime
            Get
                Return _lastUpdateTime
            End Get
            Set(value As DateTime)
                SetProperty(_lastUpdateTime, value)
            End Set
        End Property

        ' خصائص الإحصائيات
        Private _totalAccountsCount As Integer
        Private _activeAccountsCount As Integer

        Public Property TotalAccountsCount As Integer
            Get
                Return _totalAccountsCount
            End Get
            Set(value As Integer)
                SetProperty(_totalAccountsCount, value)
            End Set
        End Property

        Public Property ActiveAccountsCount As Integer
            Get
                Return _activeAccountsCount
            End Get
            Set(value As Integer)
                SetProperty(_activeAccountsCount, value)
            End Set
        End Property

        #End Region

        #Region "Commands"

        Public Property AddAccountCommand As ICommand
        Public Property EditAccountCommand As ICommand
        Public Property DeleteAccountCommand As ICommand
        Public Property ImportFromExcelCommand As ICommand
        Public Property ExportToExcelCommand As ICommand
        Public Property CreateSampleFileCommand As ICommand
        Public Property CloseCommand As ICommand

        Private Sub InitializeCommands()
            AddAccountCommand = New RelayCommand(AddressOf AddAccount)
            EditAccountCommand = New RelayCommand(AddressOf EditAccount, Function() HasSelectedAccount)
            DeleteAccountCommand = New AsyncRelayCommand(AddressOf DeleteAccountAsync, Function() HasSelectedAccount)
            ImportFromExcelCommand = New AsyncRelayCommand(AddressOf ImportFromExcelAsync)
            ExportToExcelCommand = New AsyncRelayCommand(AddressOf ExportToExcelAsync)
            CreateSampleFileCommand = New RelayCommand(AddressOf CreateSampleFile)
            CloseCommand = New RelayCommand(AddressOf CloseWindow)
        End Sub

        #End Region

        #Region "Methods"

        Private Async Function LoadAccountsAsync() As Task
            Await ExecuteAsync(Async Function()
                StatusMessage = "جاري تحميل دليل الحسابات..."

                Dim accounts = Await _accountService.GetAllAccountsAsync()

                ' بناء شجرة الحسابات
                BuildAccountTree(accounts)

                LastUpdateTime = DateTime.Now
                StatusMessage = $"تم تحميل {TotalAccountsCount} حساب"

                Return True
            End Function, "خطأ في تحميل دليل الحسابات")
        End Function

        Private Sub BuildAccountTree(accounts As List(Of ChartOfAccount))
            RootAccounts.Clear()

            ' الحصول على الحسابات الجذرية (بدون حساب أب)
            Dim rootAccounts = accounts.Where(Function(a) Not a.ParentAccountId.HasValue).OrderBy(Function(a) a.AccountCode).ToList()

            For Each rootAccount In rootAccounts
                Dim rootViewModel = New ChartOfAccountViewModel(rootAccount)
                BuildChildAccounts(rootViewModel, accounts)
                Me.RootAccounts.Add(rootViewModel)
            Next

            UpdateCounts()
        End Sub

        Private Sub BuildChildAccounts(parentViewModel As ChartOfAccountViewModel, allAccounts As List(Of ChartOfAccount))
            Dim childAccounts = allAccounts.Where(Function(a) a.ParentAccountId = parentViewModel.AccountId).OrderBy(Function(a) a.AccountCode).ToList()

            For Each childAccount In childAccounts
                Dim childViewModel = New ChartOfAccountViewModel(childAccount)
                BuildChildAccounts(childViewModel, allAccounts)
                parentViewModel.ChildAccounts.Add(childViewModel)
            Next
        End Sub

        Private Sub UpdateCounts()
            TotalAccountsCount = CountAllAccounts(RootAccounts)
            ActiveAccountsCount = CountActiveAccounts(RootAccounts)
        End Sub

        Private Function CountAllAccounts(accounts As ObservableCollection(Of ChartOfAccountViewModel)) As Integer
            Dim count = accounts.Count
            For Each account In accounts
                count += CountAllAccounts(account.ChildAccounts)
            Next
            Return count
        End Function

        Private Function CountActiveAccounts(accounts As ObservableCollection(Of ChartOfAccountViewModel)) As Integer
            Dim count = accounts.Count(Function(a) a.IsActive)
            For Each account In accounts
                count += CountActiveAccounts(account.ChildAccounts)
            Next
            Return count
        End Function

        Private Sub FilterAccounts()
            ' تطبيق التصفية حسب النص المدخل
            If String.IsNullOrWhiteSpace(SearchText) Then
                ' إظهار جميع الحسابات
                ShowAllAccounts(RootAccounts)
            Else
                ' تصفية الحسابات
                FilterAccountsByText(RootAccounts, SearchText.ToLower())
            End If
        End Sub

        Private Sub ShowAllAccounts(accounts As ObservableCollection(Of ChartOfAccountViewModel))
            For Each account In accounts
                account.IsVisible = True
                ShowAllAccounts(account.ChildAccounts)
            Next
        End Sub

        Private Function FilterAccountsByText(accounts As ObservableCollection(Of ChartOfAccountViewModel), searchText As String) As Boolean
            Dim hasVisibleChild = False

            For Each account In accounts
                Dim matchesSearch = account.AccountName.ToLower().Contains(searchText) OrElse account.AccountCode.Contains(searchText)
                Dim hasVisibleChildren = FilterAccountsByText(account.ChildAccounts, searchText)

                account.IsVisible = matchesSearch OrElse hasVisibleChildren
                If account.IsVisible Then
                    hasVisibleChild = True
                End If
            Next

            Return hasVisibleChild
        End Function

        Private Sub AddAccount()
            Try
                Dim dialog As New AddEditAccountDialog()
                If dialog.ShowDialog() = True AndAlso dialog.DialogResult Then
                    ' تحديث قائمة الحسابات
                    LoadAccountsAsync()
                    StatusMessage = "تم إضافة الحساب بنجاح"
                End If
            Catch ex As Exception
                StatusMessage = $"خطأ في فتح نافذة إضافة الحساب: {ex.Message}"
            End Try
        End Sub

        Private Sub EditAccount()
            If SelectedAccount IsNot Nothing Then
                Try
                    ' إنشاء كائن ChartOfAccount من ViewModel
                    Dim account As New ChartOfAccount() With {
                        .AccountId = SelectedAccount.AccountId,
                        .AccountCode = SelectedAccount.AccountCode,
                        .AccountName = SelectedAccount.AccountName,
                        .AccountType = SelectedAccount.AccountType,
                        .AccountNature = SelectedAccount.AccountNature,
                        .IsActive = SelectedAccount.IsActive,
                        .IsAnalytical = SelectedAccount.IsAnalytical,
                        .Notes = SelectedAccount.Notes,
                        .CreatedDate = SelectedAccount.CreatedDate,
                        .CreatedBy = SelectedAccount.CreatedBy
                    }

                    Dim dialog As New AddEditAccountDialog(account)
                    If dialog.ShowDialog() = True AndAlso dialog.DialogResult Then
                        ' تحديث قائمة الحسابات
                        LoadAccountsAsync()
                        StatusMessage = "تم تحديث الحساب بنجاح"
                    End If
                Catch ex As Exception
                    StatusMessage = $"خطأ في فتح نافذة تعديل الحساب: {ex.Message}"
                End Try
            End If
        End Sub

        Private Async Function DeleteAccountAsync() As Task
            If SelectedAccount IsNot Nothing Then
                ' تأكيد الحذف
                Dim result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب: {SelectedAccount.AccountName}؟{vbNewLine}سيتم حذف جميع الحسابات الفرعية أيضاً.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                )

                If result = MessageBoxResult.Yes Then
                    Await ExecuteAsync(Async Function()
                        Dim success = Await _accountService.DeleteAccountAsync(SelectedAccount.AccountId)
                        If success Then
                            Await LoadAccountsAsync()
                            SelectedAccount = Nothing
                            StatusMessage = "تم حذف الحساب بنجاح"
                        Else
                            StatusMessage = "فشل في حذف الحساب"
                        End If
                        Return success
                    End Function, "خطأ في حذف الحساب")
                End If
            End If
        End Function

        Private Async Function ImportFromExcelAsync() As Task
            Dim openFileDialog As New OpenFileDialog() With {
                .Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                .Title = "اختيار ملف Excel لاستيراد دليل الحسابات"
            }

            If openFileDialog.ShowDialog() = True Then
                Await ExecuteAsync(Async Function()
                    StatusMessage = "جاري استيراد دليل الحسابات من Excel..."

                    Dim result = Await _importService.ImportFromExcelAsync(openFileDialog.FileName)

                    If result.Success Then
                        Await LoadAccountsAsync()
                        StatusMessage = $"تم استيراد {result.SuccessCount} حساب بنجاح، تم تجاهل {result.SkippedCount} حساب مكرر"

                        MessageBox.Show(
                            $"تم الاستيراد بنجاح!{vbNewLine}الحسابات المستوردة: {result.SuccessCount}{vbNewLine}الحسابات المتجاهلة: {result.SkippedCount}",
                            "نتيجة الاستيراد",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information
                        )
                    Else
                        StatusMessage = $"فشل في الاستيراد: {result.ErrorMessage}"
                        MessageBox.Show(result.ErrorMessage, "خطأ في الاستيراد", MessageBoxButton.OK, MessageBoxImage.Error)
                    End If

                    Return result.Success
                End Function, "خطأ في استيراد ملف Excel")
            End If
        End Function

        Private Async Function ExportToExcelAsync() As Task
            Dim saveFileDialog As New SaveFileDialog() With {
                .Filter = "Excel Files (*.xlsx)|*.xlsx",
                .Title = "حفظ دليل الحسابات كملف Excel",
                .FileName = $"دليل_الحسابات_{DateTime.Now:yyyyMMdd}.xlsx"
            }

            If saveFileDialog.ShowDialog() = True Then
                Await ExecuteAsync(Async Function()
                    StatusMessage = "جاري تصدير دليل الحسابات إلى Excel..."

                    ' تنفيذ التصدير
                    Await Task.Delay(1000) ' محاكاة العملية

                    StatusMessage = "تم تصدير دليل الحسابات بنجاح"
                    MessageBox.Show("تم تصدير دليل الحسابات بنجاح!", "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information)

                    Return True
                End Function, "خطأ في تصدير ملف Excel")
            End If
        End Function

        Private Sub CreateSampleFile()
            Dim saveFileDialog As New SaveFileDialog() With {
                .Filter = "Excel Files (*.xlsx)|*.xlsx",
                .Title = "حفظ الملف النموذجي",
                .FileName = "نموذج_دليل_الحسابات.xlsx"
            }

            If saveFileDialog.ShowDialog() = True Then
                Try
                    Dim success = _importService.CreateSampleExcelFile(saveFileDialog.FileName)
                    If success Then
                        StatusMessage = "تم إنشاء الملف النموذجي بنجاح"
                        MessageBox.Show("تم إنشاء الملف النموذجي بنجاح!", "إنشاء ناجح", MessageBoxButton.OK, MessageBoxImage.Information)
                    Else
                        StatusMessage = "فشل في إنشاء الملف النموذجي"
                        MessageBox.Show("فشل في إنشاء الملف النموذجي", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
                    End If
                Catch ex As Exception
                    StatusMessage = $"خطأ في إنشاء الملف النموذجي: {ex.Message}"
                    MessageBox.Show(ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
                End Try
            End If
        End Sub

        Private Sub CloseWindow()
            ' إغلاق النافذة
            RaiseEvent CloseRequested()
        End Sub

        ''' <summary>
        ''' حدث طلب إغلاق النافذة
        ''' </summary>
        Public Event CloseRequested()

        #End Region

        Public Overrides Sub Dispose()
            _accountService?.Dispose()
            _importService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' ViewModel لعنصر الحساب في الشجرة
    ''' </summary>
    Public Class ChartOfAccountViewModel
        Inherits ViewModelBase

        Private _account As ChartOfAccount
        Private _childAccounts As ObservableCollection(Of ChartOfAccountViewModel)
        Private _isVisible As Boolean = True

        Public Sub New(account As ChartOfAccount)
            _account = account
            _childAccounts = New ObservableCollection(Of ChartOfAccountViewModel)()
        End Sub

        Public ReadOnly Property AccountId As Integer
            Get
                Return _account.AccountId
            End Get
        End Property

        Public ReadOnly Property AccountCode As String
            Get
                Return _account.AccountCode
            End Get
        End Property

        Public ReadOnly Property AccountName As String
            Get
                Return _account.AccountName
            End Get
        End Property

        Public ReadOnly Property AccountType As String
            Get
                Return _account.AccountType
            End Get
        End Property

        Public ReadOnly Property AccountNature As String
            Get
                Return _account.AccountNature
            End Get
        End Property

        Public ReadOnly Property IsActive As Boolean
            Get
                Return _account.IsActive
            End Get
        End Property

        Public ReadOnly Property IsAnalytical As Boolean
            Get
                Return _account.IsAnalytical
            End Get
        End Property

        Public ReadOnly Property Notes As String
            Get
                Return _account.Notes
            End Get
        End Property

        Public ReadOnly Property CreatedDate As DateTime
            Get
                Return _account.CreatedDate
            End Get
        End Property

        Public ReadOnly Property CreatedBy As String
            Get
                Return _account.CreatedBy
            End Get
        End Property

        Public Property ChildAccounts As ObservableCollection(Of ChartOfAccountViewModel)
            Get
                Return _childAccounts
            End Get
            Set(value As ObservableCollection(Of ChartOfAccountViewModel))
                SetProperty(_childAccounts, value)
            End Set
        End Property

        Public Property IsVisible As Boolean
            Get
                Return _isVisible
            End Get
            Set(value As Boolean)
                SetProperty(_isVisible, value)
            End Set
        End Property

        ' خصائص العرض
        Public ReadOnly Property AccountColor As Brush
            Get
                Select Case AccountNature
                    Case "مدين"
                        Return Brushes.Blue
                    Case "دائن"
                        Return Brushes.Green
                    Case Else
                        Return Brushes.Gray
                End Select
            End Get
        End Property

        Public ReadOnly Property NatureColor As Brush
            Get
                Select Case AccountNature
                    Case "مدين"
                        Return Brushes.LightBlue
                    Case "دائن"
                        Return Brushes.LightGreen
                    Case Else
                        Return Brushes.LightGray
                End Select
            End Get
        End Property

        Public ReadOnly Property AnalyticalIcon As String
            Get
                Return If(IsAnalytical, "CheckCircle", "Circle")
            End Get
        End Property

        Public ReadOnly Property AnalyticalColor As Brush
            Get
                Return If(IsAnalytical, Brushes.Green, Brushes.Gray)
            End Get
        End Property

        Public ReadOnly Property AnalyticalText As String
            Get
                Return If(IsAnalytical, "نعم", "لا")
            End Get
        End Property

        Public ReadOnly Property StatusIcon As String
            Get
                Return If(IsActive, "CheckCircle", "Cancel")
            End Get
        End Property

        Public ReadOnly Property StatusColor As Brush
            Get
                Return If(IsActive, Brushes.Green, Brushes.Red)
            End Get
        End Property

        Public ReadOnly Property StatusText As String
            Get
                Return If(IsActive, "نشط", "غير نشط")
            End Get
        End Property

        Public ReadOnly Property HasNotes As Boolean
            Get
                Return Not String.IsNullOrEmpty(Notes)
            End Get
        End Property

    End Class

End Namespace
