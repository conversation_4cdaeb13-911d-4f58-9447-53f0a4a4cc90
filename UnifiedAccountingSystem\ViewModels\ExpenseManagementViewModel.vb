Imports System.Collections.ObjectModel
Imports System.Windows.Input
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لإدارة المصروفات والتخصيصات المالية
    ''' </summary>
    Public Class ExpenseManagementViewModel
        Inherits ViewModelBase

        Private ReadOnly _expenseService As ExpenseService
        Private ReadOnly _departmentService As DepartmentService

        ' الخصائص الأساسية
        Private _expenseItems As ObservableCollection(Of ExpenseItemViewModel)
        Private _selectedItem As ExpenseItemViewModel
        Private _departments As ObservableCollection(Of Department)
        Private _fiscalYears As ObservableCollection(Of Integer)
        Private _months As ObservableCollection(Of MonthItem)

        ' خصائص التصفية
        Private _selectedFiscalYear As Integer
        Private _selectedMonth As MonthItem
        Private _selectedDepartment As Department
        Private _searchText As String
        Private _filterFormType As String
        Private _filterExpenseType As String
        Private _filterChapter As String
        Private _filterArticle As String

        ' خصائص المعلومات
        Private _treasuryType As String = "التشغيلية"
        Private _sectionNumber As String = "13"
        Private _divisionNumber As String = "1"
        Private _province As String = "بغداد"
        Private _lastUpdateTime As DateTime

        Public Sub New()
            _expenseService = New ExpenseService()
            _departmentService = New DepartmentService()

            ' تهيئة المجموعات
            ExpenseItems = New ObservableCollection(Of ExpenseItemViewModel)()
            Departments = New ObservableCollection(Of Department)()
            FiscalYears = New ObservableCollection(Of Integer)()
            Months = New ObservableCollection(Of MonthItem)()

            ' إنشاء الأوامر
            InitializeCommands()

            ' تحميل البيانات الأولية
            LoadInitialData()

            Title = "إدارة المصروفات والتخصيصات المالية"
        End Sub

        #Region "Properties"

        Public Property ExpenseItems As ObservableCollection(Of ExpenseItemViewModel)
            Get
                Return _expenseItems
            End Get
            Set(value As ObservableCollection(Of ExpenseItemViewModel))
                SetProperty(_expenseItems, value)
            End Set
        End Property

        Public Property SelectedItem As ExpenseItemViewModel
            Get
                Return _selectedItem
            End Get
            Set(value As ExpenseItemViewModel)
                If SetProperty(_selectedItem, value) Then
                    OnPropertyChanged(NameOf(HasSelectedItem))
                End If
            End Set
        End Property

        Public ReadOnly Property HasSelectedItem As Boolean
            Get
                Return SelectedItem IsNot Nothing
            End Get
        End Property

        Public Property Departments As ObservableCollection(Of Department)
            Get
                Return _departments
            End Get
            Set(value As ObservableCollection(Of Department))
                SetProperty(_departments, value)
            End Set
        End Property

        Public Property FiscalYears As ObservableCollection(Of Integer)
            Get
                Return _fiscalYears
            End Get
            Set(value As ObservableCollection(Of Integer))
                SetProperty(_fiscalYears, value)
            End Set
        End Property

        Public Property Months As ObservableCollection(Of MonthItem)
            Get
                Return _months
            End Get
            Set(value As ObservableCollection(Of MonthItem))
                SetProperty(_months, value)
            End Set
        End Property

        Public Property SelectedFiscalYear As Integer
            Get
                Return _selectedFiscalYear
            End Get
            Set(value As Integer)
                SetProperty(_selectedFiscalYear, value)
            End Set
        End Property

        Public Property SelectedMonth As MonthItem
            Get
                Return _selectedMonth
            End Get
            Set(value As MonthItem)
                SetProperty(_selectedMonth, value)
            End Set
        End Property

        Public Property SelectedDepartment As Department
            Get
                Return _selectedDepartment
            End Get
            Set(value As Department)
                SetProperty(_selectedDepartment, value)
            End Set
        End Property

        Public Property SearchText As String
            Get
                Return _searchText
            End Get
            Set(value As String)
                SetProperty(_searchText, value)
            End Set
        End Property

        Public Property FilterFormType As String
            Get
                Return _filterFormType
            End Get
            Set(value As String)
                SetProperty(_filterFormType, value)
            End Set
        End Property

        Public Property FilterExpenseType As String
            Get
                Return _filterExpenseType
            End Get
            Set(value As String)
                SetProperty(_filterExpenseType, value)
            End Set
        End Property

        Public Property FilterChapter As String
            Get
                Return _filterChapter
            End Get
            Set(value As String)
                SetProperty(_filterChapter, value)
            End Set
        End Property

        Public Property FilterArticle As String
            Get
                Return _filterArticle
            End Get
            Set(value As String)
                SetProperty(_filterArticle, value)
            End Set
        End Property

        Public Property TreasuryType As String
            Get
                Return _treasuryType
            End Get
            Set(value As String)
                SetProperty(_treasuryType, value)
            End Set
        End Property

        Public Property SectionNumber As String
            Get
                Return _sectionNumber
            End Get
            Set(value As String)
                SetProperty(_sectionNumber, value)
            End Set
        End Property

        Public Property DivisionNumber As String
            Get
                Return _divisionNumber
            End Get
            Set(value As String)
                SetProperty(_divisionNumber, value)
            End Set
        End Property

        Public Property Province As String
            Get
                Return _province
            End Get
            Set(value As String)
                SetProperty(_province, value)
            End Set
        End Property

        Public Property LastUpdateTime As DateTime
            Get
                Return _lastUpdateTime
            End Get
            Set(value As DateTime)
                SetProperty(_lastUpdateTime, value)
            End Set
        End Property

        Public ReadOnly Property TotalItemsCount As Integer
            Get
                Return If(ExpenseItems?.Count, 0)
            End Get
        End Property

        Public ReadOnly Property TotalAllocation As Decimal
            Get
                Return If(ExpenseItems?.Sum(Function(x) x.AnnualAllocation), 0)
            End Get
        End Property

        #End Region

        #Region "Commands"

        Public Property SearchCommand As ICommand
        Public Property ClearFiltersCommand As ICommand
        Public Property AddNewItemCommand As ICommand
        Public Property EditItemCommand As ICommand
        Public Property DeleteItemCommand As ICommand
        Public Property ExportToExcelCommand As ICommand
        Public Property PrintCommand As ICommand
        Public Property CloseCommand As ICommand

        Private Sub InitializeCommands()
            SearchCommand = New AsyncRelayCommand(AddressOf SearchAsync)
            ClearFiltersCommand = New RelayCommand(AddressOf ClearFilters)
            AddNewItemCommand = New RelayCommand(AddressOf AddNewItem)
            EditItemCommand = New RelayCommand(AddressOf EditItem, Function() HasSelectedItem)
            DeleteItemCommand = New AsyncRelayCommand(AddressOf DeleteItemAsync, Function() HasSelectedItem)
            ExportToExcelCommand = New AsyncRelayCommand(AddressOf ExportToExcelAsync)
            PrintCommand = New RelayCommand(AddressOf Print)
            CloseCommand = New RelayCommand(AddressOf CloseWindow)
        End Sub

        #End Region

        #Region "Methods"

        Private Async Sub LoadInitialData()
            Await ExecuteAsync(Async Function()
                ' تحميل السنوات المالية
                For year As Integer = 2020 To DateTime.Now.Year + 2
                    FiscalYears.Add(year)
                Next
                SelectedFiscalYear = DateTime.Now.Year

                ' تحميل الأشهر
                Dim monthNames() As String = {
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                }

                For i As Integer = 1 To 12
                    Months.Add(New MonthItem() With {.Value = i, .Name = monthNames(i - 1)})
                Next
                SelectedMonth = Months.FirstOrDefault(Function(m) m.Value = DateTime.Now.Month)

                ' تحميل الدوائر
                Dim departments = Await _departmentService.GetAllDepartmentsAsync()
                For Each dept In departments
                    Me.Departments.Add(dept)
                Next

                ' تحميل البيانات
                Await LoadExpenseDataAsync()

                LastUpdateTime = DateTime.Now
                StatusMessage = "تم تحميل البيانات بنجاح"

                Return True
            End Function, "خطأ في تحميل البيانات الأولية")
        End Sub

        Private Async Function LoadExpenseDataAsync() As Task
            Try
                IsLoading = True
                StatusMessage = "جاري تحميل بيانات المصروفات..."

                Dim expenseData = Await _expenseService.GetExpenseDataAsync(
                    SelectedFiscalYear,
                    If(SelectedMonth?.Value, 0),
                    SelectedDepartment?.DepartmentId
                )

                ExpenseItems.Clear()
                For Each item In expenseData
                    ExpenseItems.Add(New ExpenseItemViewModel(item))
                Next

                OnPropertyChanged(NameOf(TotalItemsCount))
                OnPropertyChanged(NameOf(TotalAllocation))

                StatusMessage = $"تم تحميل {ExpenseItems.Count} بند مصروف"

            Catch ex As Exception
                StatusMessage = "خطأ في تحميل بيانات المصروفات"
                ' يمكن إضافة تسجيل الخطأ هنا
            Finally
                IsLoading = False
            End Try
        End Function

        Private Async Function SearchAsync() As Task
            Await LoadExpenseDataAsync()
        End Function

        Private Sub ClearFilters()
            SearchText = String.Empty
            FilterFormType = String.Empty
            FilterExpenseType = String.Empty
            FilterChapter = String.Empty
            FilterArticle = String.Empty
            SelectedDepartment = Nothing
        End Sub

        Private Sub AddNewItem()
            ' فتح نافذة إضافة بند جديد
            StatusMessage = "فتح نافذة إضافة بند جديد..."
        End Sub

        Private Sub EditItem()
            If SelectedItem IsNot Nothing Then
                ' فتح نافذة تعديل البند المحدد
                StatusMessage = $"تعديل البند: {SelectedItem.Description}"
            End If
        End Sub

        Private Async Function DeleteItemAsync() As Task
            If SelectedItem IsNot Nothing Then
                ' تأكيد الحذف
                Dim result = MessageBox.Show(
                    $"هل أنت متأكد من حذف البند: {SelectedItem.Description}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                )

                If result = MessageBoxResult.Yes Then
                    Await ExecuteAsync(Async Function()
                        Dim success = Await _expenseService.DeleteBudgetAllocationAsync(SelectedItem.AllocationId)
                        If success Then
                            ExpenseItems.Remove(SelectedItem)
                            SelectedItem = Nothing
                            StatusMessage = "تم حذف البند بنجاح"
                            OnPropertyChanged(NameOf(TotalItemsCount))
                            OnPropertyChanged(NameOf(TotalAllocation))
                        Else
                            StatusMessage = "فشل في حذف البند"
                        End If
                        Return success
                    End Function, "خطأ في حذف البند")
                End If
            End If
        End Function

        Private Async Function ExportToExcelAsync() As Task
            StatusMessage = "جاري تصدير البيانات إلى Excel..."
            ' تنفيذ تصدير Excel
            Await Task.Delay(1000) ' محاكاة العملية
            StatusMessage = "تم تصدير البيانات بنجاح"
        End Function

        Private Sub Print()
            StatusMessage = "جاري تحضير التقرير للطباعة..."
            ' تنفيذ الطباعة
        End Sub

        Private Sub CloseWindow()
            ' إغلاق النافذة
            RaiseEvent CloseRequested()
        End Sub

        ''' <summary>
        ''' حدث طلب إغلاق النافذة
        ''' </summary>
        Public Event CloseRequested()

        #End Region

        Public Overrides Sub Dispose()
            _expenseService?.Dispose()
            _departmentService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' ViewModel لعنصر المصروف
    ''' </summary>
    Public Class ExpenseItemViewModel
        Inherits ViewModelBase

        Private _budgetAllocation As BudgetAllocation
        Private _monthlyExpense As MonthlyExpense

        Public Sub New(budgetAllocation As BudgetAllocation, Optional monthlyExpense As MonthlyExpense = Nothing)
            _budgetAllocation = budgetAllocation
            _monthlyExpense = monthlyExpense
        End Sub

        Public ReadOnly Property AllocationId As Integer
            Get
                Return _budgetAllocation.AllocationId
            End Get
        End Property

        Public ReadOnly Property FormType As String
            Get
                Return _budgetAllocation.FormType
            End Get
        End Property

        Public ReadOnly Property ExpenseType As String
            Get
                Return _budgetAllocation.ExpenseType
            End Get
        End Property

        Public ReadOnly Property Chapter As String
            Get
                Return _budgetAllocation.Chapter
            End Get
        End Property

        Public ReadOnly Property Article As String
            Get
                Return _budgetAllocation.Article
            End Get
        End Property

        Public ReadOnly Property ItemType As String
            Get
                Return _budgetAllocation.ItemType
            End Get
        End Property

        Public ReadOnly Property ItemDetails As String
            Get
                Return _budgetAllocation.ItemDetails
            End Get
        End Property

        Public ReadOnly Property Description As String
            Get
                Return _budgetAllocation.Description
            End Get
        End Property

        Public ReadOnly Property AnnualAllocation As Decimal
            Get
                Return _budgetAllocation.AnnualAllocation
            End Get
        End Property

        Public ReadOnly Property PreviousMonthFils As Decimal
            Get
                Return If(_monthlyExpense?.PreviousMonthFils, 0)
            End Get
        End Property

        Public ReadOnly Property PreviousMonthDinars As Decimal
            Get
                Return If(_monthlyExpense?.PreviousMonthDinars, 0)
            End Get
        End Property

        Public ReadOnly Property CurrentMonthFils As Decimal
            Get
                Return If(_monthlyExpense?.CurrentMonthFils, 0)
            End Get
        End Property

        Public ReadOnly Property CurrentMonthDinars As Decimal
            Get
                Return If(_monthlyExpense?.CurrentMonthDinars, 0)
            End Get
        End Property

        Public ReadOnly Property GrandTotal As Decimal
            Get
                Return If(_monthlyExpense?.GrandTotal, 0)
            End Get
        End Property

        Public ReadOnly Property SpendingPercentage As Decimal
            Get
                Return _budgetAllocation.SpendingPercentage
            End Get
        End Property

    End Class

    ''' <summary>
    ''' عنصر الشهر
    ''' </summary>
    Public Class MonthItem
        Public Property Value As Integer
        Public Property Name As String
    End Class

End Namespace
