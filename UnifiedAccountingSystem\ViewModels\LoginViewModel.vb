Imports System.Windows.Input
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لنافذة تسجيل الدخول
    ''' </summary>
    Public Class LoginViewModel
        Inherits ViewModelBase

        Private ReadOnly _userService As UserService

        ' الخصائص
        Private _username As String
        Private _password As String
        Private _rememberMe As Boolean
        Private _errorMessage As String
        Private _hasError As Boolean

        ' الأحداث
        Public Event LoginSuccessful As EventHandler
        Public Event LoginFailed As EventHandler

        ''' <summary>
        ''' منشئ ViewModel
        ''' </summary>
        Public Sub New()
            _userService = New UserService()
            
            ' إنشاء الأوامر
            LoginCommand = New AsyncRelayCommand(AddressOf LoginAsync, AddressOf CanLogin)
            
            ' تعيين العنوان
            Title = "تسجيل الدخول"
        End Sub

        ''' <summary>
        ''' اسم المستخدم
        ''' </summary>
        Public Property Username As String
            Get
                Return _username
            End Get
            Set(value As String)
                If SetProperty(_username, value) Then
                    ClearError()
                    LoginCommand.RaiseCanExecuteChanged()
                End If
            End Set
        End Property

        ''' <summary>
        ''' كلمة المرور
        ''' </summary>
        Public Property Password As String
            Get
                Return _password
            End Get
            Set(value As String)
                If SetProperty(_password, value) Then
                    ClearError()
                    LoginCommand.RaiseCanExecuteChanged()
                End If
            End Set
        End Property

        ''' <summary>
        ''' تذكرني
        ''' </summary>
        Public Property RememberMe As Boolean
            Get
                Return _rememberMe
            End Get
            Set(value As Boolean)
                SetProperty(_rememberMe, value)
            End Set
        End Property

        ''' <summary>
        ''' رسالة الخطأ
        ''' </summary>
        Public Property ErrorMessage As String
            Get
                Return _errorMessage
            End Get
            Set(value As String)
                If SetProperty(_errorMessage, value) Then
                    HasError = Not String.IsNullOrEmpty(value)
                End If
            End Set
        End Property

        ''' <summary>
        ''' وجود خطأ
        ''' </summary>
        Public Property HasError As Boolean
            Get
                Return _hasError
            End Get
            Set(value As Boolean)
                SetProperty(_hasError, value)
            End Set
        End Property

        ''' <summary>
        ''' أمر تسجيل الدخول
        ''' </summary>
        Public Property LoginCommand As AsyncRelayCommand

        ''' <summary>
        ''' التحقق من إمكانية تسجيل الدخول
        ''' </summary>
        ''' <returns>True إذا كان يمكن تسجيل الدخول</returns>
        Public Function CanLogin() As Boolean
            Return Not String.IsNullOrWhiteSpace(Username) AndAlso 
                   Not String.IsNullOrWhiteSpace(Password) AndAlso 
                   Not IsLoading
        End Function

        ''' <summary>
        ''' تسجيل الدخول غير المتزامن
        ''' </summary>
        Private Async Function LoginAsync() As Task
            Try
                IsLoading = True
                ClearError()
                
                ' محاولة تسجيل الدخول
                Dim loginResult = Await _userService.LoginAsync(Username, Password)
                
                If loginResult.Success Then
                    ' حفظ معلومات المستخدم الحالي
                    CurrentUserService.SetCurrentUser(loginResult.User)
                    
                    ' تسجيل عملية تسجيل الدخول
                    Await _userService.LogUserActionAsync(loginResult.User.UserId, "تسجيل دخول", "Users", Nothing)
                    
                    ' إثارة حدث نجاح تسجيل الدخول
                    RaiseEvent LoginSuccessful(Me, EventArgs.Empty)
                Else
                    ' عرض رسالة الخطأ
                    ErrorMessage = loginResult.ErrorMessage
                    
                    ' إثارة حدث فشل تسجيل الدخول
                    RaiseEvent LoginFailed(Me, EventArgs.Empty)
                End If
                
            Catch ex As Exception
                ErrorMessage = "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى."
                RaiseEvent LoginFailed(Me, EventArgs.Empty)
                
                ' تسجيل الخطأ
                Console.WriteLine($"خطأ في تسجيل الدخول: {ex.Message}")
                
            Finally
                IsLoading = False
            End Try
        End Function

        ''' <summary>
        ''' مسح رسالة الخطأ
        ''' </summary>
        Private Sub ClearError()
            ErrorMessage = String.Empty
            HasError = False
        End Sub

        ''' <summary>
        ''' التحقق من صحة البيانات
        ''' </summary>
        Public Overrides Function Validate() As Boolean
            ClearAllErrors()
            
            If String.IsNullOrWhiteSpace(Username) Then
                AddError(NameOf(Username), "اسم المستخدم مطلوب")
            End If
            
            If String.IsNullOrWhiteSpace(Password) Then
                AddError(NameOf(Password), "كلمة المرور مطلوبة")
            ElseIf Password.Length < 3 Then
                AddError(NameOf(Password), "كلمة المرور يجب أن تكون 3 أحرف على الأقل")
            End If
            
            Return Not HasErrors
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Overrides Sub Dispose()
            _userService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' نتيجة تسجيل الدخول
    ''' </summary>
    Public Class LoginResult
        Public Property Success As Boolean
        Public Property User As User
        Public Property ErrorMessage As String
        Public Property RequiresPasswordChange As Boolean
        Public Property SessionToken As String
    End Class

    ''' <summary>
    ''' خدمة المستخدم الحالي
    ''' </summary>
    Public Class CurrentUserService
        Private Shared _currentUser As User
        Private Shared _sessionToken As String

        ''' <summary>
        ''' المستخدم الحالي
        ''' </summary>
        Public Shared ReadOnly Property CurrentUser As User
            Get
                Return _currentUser
            End Get
        End Property

        ''' <summary>
        ''' رمز الجلسة
        ''' </summary>
        Public Shared ReadOnly Property SessionToken As String
            Get
                Return _sessionToken
            End Get
        End Property

        ''' <summary>
        ''' تعيين المستخدم الحالي
        ''' </summary>
        ''' <param name="user">المستخدم</param>
        ''' <param name="sessionToken">رمز الجلسة</param>
        Public Shared Sub SetCurrentUser(user As User, Optional sessionToken As String = Nothing)
            _currentUser = user
            _sessionToken = sessionToken
        End Sub

        ''' <summary>
        ''' مسح المستخدم الحالي
        ''' </summary>
        Public Shared Sub ClearCurrentUser()
            _currentUser = Nothing
            _sessionToken = Nothing
        End Sub

        ''' <summary>
        ''' التحقق من تسجيل الدخول
        ''' </summary>
        Public Shared ReadOnly Property IsLoggedIn As Boolean
            Get
                Return _currentUser IsNot Nothing
            End Get
        End Property

        ''' <summary>
        ''' التحقق من كون المستخدم مدير
        ''' </summary>
        Public Shared ReadOnly Property IsAdmin As Boolean
            Get
                Return _currentUser IsNot Nothing AndAlso _currentUser.IsAdmin
            End Get
        End Property

        ''' <summary>
        ''' اسم المستخدم الحالي
        ''' </summary>
        Public Shared ReadOnly Property CurrentUserName As String
            Get
                Return If(_currentUser?.AccountName, "غير معروف")
            End Get
        End Property

        ''' <summary>
        ''' معرف المستخدم الحالي
        ''' </summary>
        Public Shared ReadOnly Property CurrentUserId As Integer?
            Get
                Return _currentUser?.UserId
            End Get
        End Property

    End Class

End Namespace
