Imports System.Windows.Input
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services

Namespace ViewModels

    ''' <summary>
    ''' ViewModel للنافذة الرئيسية
    ''' </summary>
    Public Class MainWindowViewModel
        Inherits ViewModelBase

        Private ReadOnly _accountService As ChartOfAccountsService
        Private _currentView As Object
        Private _showWelcomeScreen As Boolean = True
        Private _currentUserName As String
        Private _currentDateTime As String

        Public Sub New()
            _accountService = New ChartOfAccountsService()

            ' تهيئة البيانات الأولية
            InitializeData()

            ' إنشاء الأوامر
            InitializeCommands()

            ' بدء مؤقت التاريخ والوقت
            StartDateTimeTimer()

            Title = "النظام المحاسبي الموحد"
        End Sub

        #Region "Properties"

        Public Property CurrentView As Object
            Get
                Return _currentView
            End Get
            Set(value As Object)
                If SetProperty(_currentView, value) Then
                    ShowWelcomeScreen = value Is Nothing
                End If
            End Set
        End Property

        Public Property ShowWelcomeScreen As Boolean
            Get
                Return _showWelcomeScreen
            End Get
            Set(value As Boolean)
                SetProperty(_showWelcomeScreen, value)
            End Set
        End Property

        Public Property CurrentUserName As String
            Get
                Return _currentUserName
            End Get
            Set(value As String)
                SetProperty(_currentUserName, value)
            End Set
        End Property

        Public Property CurrentDateTime As String
            Get
                Return _currentDateTime
            End Get
            Set(value As String)
                SetProperty(_currentDateTime, value)
            End Set
        End Property

        #End Region

        #Region "Commands"

        ' إدارة المستخدمين
        Public Property OpenUserGroupsCommand As ICommand
        Public Property OpenUsersCommand As ICommand
        Public Property OpenPermissionsCommand As ICommand

        ' الإعدادات والتهيئة
        Public Property OpenOrganizationCommand As ICommand
        Public Property OpenDepartmentsCommand As ICommand
        Public Property OpenCurrenciesCommand As ICommand
        Public Property OpenFinalAccountsCommand As ICommand
        Public Property OpenAccountingPeriodsCommand As ICommand
        Public Property OpenBackupCommand As ICommand
        Public Property OpenConnectionSettingsCommand As ICommand

        ' شؤون الموظفين
        Public Property OpenSectionsCommand As ICommand
        Public Property OpenDivisionsCommand As ICommand
        Public Property OpenJobTitlesCommand As ICommand
        Public Property OpenQualificationsCommand As ICommand
        Public Property OpenJobGradesCommand As ICommand
        Public Property OpenStagesCommand As ICommand
        Public Property OpenEmployeesCommand As ICommand
        Public Property ImportEmployeesCommand As ICommand

        ' الحسابات
        Public Property OpenChartOfAccountsCommand As ICommand
        Public Property OpenCashBoxesCommand As ICommand
        Public Property OpenBanksCommand As ICommand
        Public Property OpenBankBranchesCommand As ICommand
        Public Property OpenBankAccountsCommand As ICommand
        Public Property OpenOpeningBalancesCommand As ICommand
        Public Property OpenPaymentVouchersCommand As ICommand
        Public Property OpenReceiptVouchersCommand As ICommand
        Public Property OpenJournalEntriesCommand As ICommand

        ' الرواتب
        Public Property OpenPayrollCalculationCommand As ICommand
        Public Property OpenPayrollReportsCommand As ICommand
        Public Property OpenPayslipsCommand As ICommand
        Public Property OpenAllowancesDeductionsCommand As ICommand
        Public Property OpenPayrollSettingsCommand As ICommand

        ' المصروفات الفعلية
        Public Property OpenExpenseManagementCommand As ICommand
        Public Property OpenBudgetAllocationsCommand As ICommand
        Public Property OpenExpenseReportsCommand As ICommand
        Public Property OpenTrialBalanceCommand As ICommand

        ' التقارير
        Public Property OpenGeneralJournalReportCommand As ICommand
        Public Property OpenAccountStatementCommand As ICommand
        Public Property OpenAccountBalancesReportCommand As ICommand
        Public Property OpenVouchersReportCommand As ICommand
        Public Property OpenOpeningBalancesReportCommand As ICommand
        Public Property OpenJournalEntriesReportCommand As ICommand
        Public Property OpenReportsCommand As ICommand

        ' عام
        Public Property OpenAboutCommand As ICommand
        Public Property LogoutCommand As ICommand

        Private Sub InitializeCommands()
            ' إدارة المستخدمين
            OpenUserGroupsCommand = New RelayCommand(AddressOf OpenUserGroups)
            OpenUsersCommand = New RelayCommand(AddressOf OpenUsers)
            OpenPermissionsCommand = New RelayCommand(AddressOf OpenPermissions)

            ' الإعدادات والتهيئة
            OpenOrganizationCommand = New RelayCommand(AddressOf OpenOrganization)
            OpenDepartmentsCommand = New RelayCommand(AddressOf OpenDepartments)
            OpenCurrenciesCommand = New RelayCommand(AddressOf OpenCurrencies)
            OpenFinalAccountsCommand = New RelayCommand(AddressOf OpenFinalAccounts)
            OpenAccountingPeriodsCommand = New RelayCommand(AddressOf OpenAccountingPeriods)
            OpenBackupCommand = New RelayCommand(AddressOf OpenBackup)
            OpenConnectionSettingsCommand = New RelayCommand(AddressOf OpenConnectionSettings)

            ' شؤون الموظفين
            OpenSectionsCommand = New RelayCommand(AddressOf OpenSections)
            OpenDivisionsCommand = New RelayCommand(AddressOf OpenDivisions)
            OpenJobTitlesCommand = New RelayCommand(AddressOf OpenJobTitles)
            OpenQualificationsCommand = New RelayCommand(AddressOf OpenQualifications)
            OpenJobGradesCommand = New RelayCommand(AddressOf OpenJobGrades)
            OpenStagesCommand = New RelayCommand(AddressOf OpenStages)
            OpenEmployeesCommand = New RelayCommand(AddressOf OpenEmployees)
            ImportEmployeesCommand = New RelayCommand(AddressOf ImportEmployees)

            ' الحسابات
            OpenChartOfAccountsCommand = New AsyncRelayCommand(AddressOf OpenChartOfAccountsAsync)
            OpenCashBoxesCommand = New RelayCommand(AddressOf OpenCashBoxes)
            OpenBanksCommand = New RelayCommand(AddressOf OpenBanks)
            OpenBankBranchesCommand = New RelayCommand(AddressOf OpenBankBranches)
            OpenBankAccountsCommand = New RelayCommand(AddressOf OpenBankAccounts)
            OpenOpeningBalancesCommand = New RelayCommand(AddressOf OpenOpeningBalances)
            OpenPaymentVouchersCommand = New RelayCommand(AddressOf OpenPaymentVouchers)
            OpenReceiptVouchersCommand = New RelayCommand(AddressOf OpenReceiptVouchers)
            OpenJournalEntriesCommand = New RelayCommand(AddressOf OpenJournalEntries)

            ' الرواتب
            OpenPayrollCalculationCommand = New RelayCommand(AddressOf OpenPayrollCalculation)
            OpenPayrollReportsCommand = New RelayCommand(AddressOf OpenPayrollReports)
            OpenPayslipsCommand = New RelayCommand(AddressOf OpenPayslips)
            OpenAllowancesDeductionsCommand = New RelayCommand(AddressOf OpenAllowancesDeductions)
            OpenPayrollSettingsCommand = New RelayCommand(AddressOf OpenPayrollSettings)

            ' المصروفات الفعلية
            OpenExpenseManagementCommand = New RelayCommand(AddressOf OpenExpenseManagement)
            OpenBudgetAllocationsCommand = New RelayCommand(AddressOf OpenBudgetAllocations)
            OpenExpenseReportsCommand = New RelayCommand(AddressOf OpenExpenseReports)
            OpenTrialBalanceCommand = New RelayCommand(AddressOf OpenTrialBalance)

            ' التقارير
            OpenGeneralJournalReportCommand = New RelayCommand(AddressOf OpenGeneralJournalReport)
            OpenAccountStatementCommand = New RelayCommand(AddressOf OpenAccountStatement)
            OpenAccountBalancesReportCommand = New RelayCommand(AddressOf OpenAccountBalancesReport)
            OpenVouchersReportCommand = New RelayCommand(AddressOf OpenVouchersReport)
            OpenOpeningBalancesReportCommand = New RelayCommand(AddressOf OpenOpeningBalancesReport)
            OpenJournalEntriesReportCommand = New RelayCommand(AddressOf OpenJournalEntriesReport)
            OpenReportsCommand = New RelayCommand(AddressOf OpenReports)

            ' عام
            OpenAboutCommand = New RelayCommand(AddressOf OpenAbout)
            LogoutCommand = New RelayCommand(AddressOf Logout)
        End Sub

        #End Region

        #Region "Methods"

        Private Sub InitializeData()
            CurrentUserName = CurrentUserService.CurrentUserName
            UpdateDateTime()
        End Sub

        Private Sub StartDateTimeTimer()
            Dim timer As New System.Windows.Threading.DispatcherTimer()
            timer.Interval = TimeSpan.FromSeconds(1)
            AddHandler timer.Tick, Sub() UpdateDateTime()
            timer.Start()
        End Sub

        Private Sub UpdateDateTime()
            CurrentDateTime = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss")
        End Sub

        ' تنفيذ الأوامر
        Private Sub OpenUserGroups()
            StatusMessage = "فتح إدارة مجموعات المستخدمين..."
        End Sub

        Private Sub OpenUsers()
            StatusMessage = "فتح إدارة حسابات المستخدمين..."
        End Sub

        Private Sub OpenPermissions()
            StatusMessage = "فتح إدارة الصلاحيات..."
        End Sub

        Private Sub OpenOrganization()
            StatusMessage = "فتح بيانات المؤسسة..."
        End Sub

        Private Sub OpenDepartments()
            StatusMessage = "فتح بيانات الدائرة..."
        End Sub

        Private Sub OpenCurrencies()
            StatusMessage = "فتح دليل العملات..."
        End Sub

        Private Sub OpenFinalAccounts()
            StatusMessage = "فتح الحسابات الختامية..."
        End Sub

        Private Sub OpenAccountingPeriods()
            StatusMessage = "فتح الفترة المحاسبية..."
        End Sub

        Private Sub OpenBackup()
            StatusMessage = "فتح النسخ الاحتياطي..."
        End Sub

        Private Sub OpenConnectionSettings()
            StatusMessage = "فتح إعدادات الاتصال..."
        End Sub

        Private Sub OpenSections()
            StatusMessage = "فتح دليل الأقسام..."
        End Sub

        Private Sub OpenDivisions()
            StatusMessage = "فتح دليل الشعب..."
        End Sub

        Private Sub OpenJobTitles()
            StatusMessage = "فتح دليل العناوين الوظيفية..."
        End Sub

        Private Sub OpenQualifications()
            StatusMessage = "فتح دليل الشهادات..."
        End Sub

        Private Sub OpenJobGrades()
            StatusMessage = "فتح دليل الدرجات الوظيفية..."
        End Sub

        Private Sub OpenStages()
            StatusMessage = "فتح دليل المراحل..."
        End Sub

        Private Sub OpenEmployees()
            StatusMessage = "فتح إدارة الموظفين..."
        End Sub

        Private Sub ImportEmployees()
            StatusMessage = "فتح استيراد الموظفين من Excel..."
        End Sub

        Private Async Function OpenChartOfAccountsAsync() As Task
            Try
                StatusMessage = "فتح دليل الحسابات..."

                ' التحقق من وجود حسابات في النظام
                Dim accountsCount = Await _accountService.GetAllAccountsAsync()
                
                If accountsCount.Count = 0 Then
                    ' عرض نافذة إعداد دليل الحسابات
                    Dim setupDialog As New ChartOfAccountsSetupDialog()
                    setupDialog.ShowDialog()
                    
                    ' إعادة التحقق من وجود الحسابات
                    accountsCount = Await _accountService.GetAllAccountsAsync()
                End If

                If accountsCount.Count > 0 Then
                    ' فتح نافذة دليل الحسابات
                    Dim chartWindow As New ChartOfAccountsWindow()
                    chartWindow.Show()
                    StatusMessage = "تم فتح دليل الحسابات"
                Else
                    StatusMessage = "لم يتم إنشاء دليل الحسابات"
                End If

            Catch ex As Exception
                StatusMessage = $"خطأ في فتح دليل الحسابات: {ex.Message}"
            End Try
        End Function

        Private Sub OpenCashBoxes()
            StatusMessage = "فتح دليل الصناديق..."
        End Sub

        Private Sub OpenBanks()
            StatusMessage = "فتح دليل المصارف..."
        End Sub

        Private Sub OpenBankBranches()
            StatusMessage = "فتح دليل فروع المصارف..."
        End Sub

        Private Sub OpenBankAccounts()
            StatusMessage = "فتح دليل الحسابات البنكية..."
        End Sub

        Private Sub OpenOpeningBalances()
            StatusMessage = "فتح الأرصدة الافتتاحية..."
        End Sub

        Private Sub OpenPaymentVouchers()
            StatusMessage = "فتح سندات الصرف..."
        End Sub

        Private Sub OpenReceiptVouchers()
            StatusMessage = "فتح سندات القبض..."
        End Sub

        Private Sub OpenJournalEntries()
            StatusMessage = "فتح قيود اليومية..."
        End Sub

        Private Sub OpenPayrollCalculation()
            StatusMessage = "فتح احتساب الرواتب..."
        End Sub

        Private Sub OpenPayrollReports()
            StatusMessage = "فتح كشوفات الرواتب..."
        End Sub

        Private Sub OpenPayslips()
            StatusMessage = "فتح قسائم الرواتب..."
        End Sub

        Private Sub OpenAllowancesDeductions()
            StatusMessage = "فتح المخصصات والاستقطاعات..."
        End Sub

        Private Sub OpenPayrollSettings()
            StatusMessage = "فتح إعدادات الرواتب..."
        End Sub

        Private Sub OpenExpenseManagement()
            Try
                StatusMessage = "فتح إدارة المصروفات..."
                Dim expenseWindow As New ExpenseManagementWindow()
                expenseWindow.Show()
                StatusMessage = "تم فتح إدارة المصروفات"
            Catch ex As Exception
                StatusMessage = $"خطأ في فتح إدارة المصروفات: {ex.Message}"
            End Try
        End Sub

        Private Sub OpenBudgetAllocations()
            StatusMessage = "فتح التخصيصات المالية..."
        End Sub

        Private Sub OpenExpenseReports()
            StatusMessage = "فتح تقارير المصروفات..."
        End Sub

        Private Sub OpenTrialBalance()
            Try
                StatusMessage = "فتح ميزان المراجعة..."
                Dim trialBalanceWindow As New TrialBalanceWindow()
                trialBalanceWindow.Show()
                StatusMessage = "تم فتح ميزان المراجعة"
            Catch ex As Exception
                StatusMessage = $"خطأ في فتح ميزان المراجعة: {ex.Message}"
            End Try
        End Sub

        Private Sub OpenGeneralJournalReport()
            StatusMessage = "فتح تقرير اليومية العامة..."
        End Sub

        Private Sub OpenAccountStatement()
            StatusMessage = "فتح كشف حساب..."
        End Sub

        Private Sub OpenAccountBalancesReport()
            StatusMessage = "فتح تقرير أرصدة الحسابات..."
        End Sub

        Private Sub OpenVouchersReport()
            StatusMessage = "فتح تقرير سندات الصرف والقبض..."
        End Sub

        Private Sub OpenOpeningBalancesReport()
            StatusMessage = "فتح تقرير الأرصدة الافتتاحية..."
        End Sub

        Private Sub OpenJournalEntriesReport()
            StatusMessage = "فتح تقرير القيود اليومية..."
        End Sub

        Private Sub OpenReports()
            StatusMessage = "فتح التقارير..."
        End Sub

        Private Sub OpenAbout()
            StatusMessage = "فتح حول البرنامج..."
        End Sub

        Private Sub Logout()
            Dim result = MessageBox.Show(
                "هل أنت متأكد من تسجيل الخروج؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            )

            If result = MessageBoxResult.Yes Then
                ' إغلاق النافذة الحالية
                Application.Current.MainWindow.Close()
                
                ' فتح نافذة تسجيل الدخول
                Dim loginWindow As New LoginWindow()
                Application.Current.MainWindow = loginWindow
                loginWindow.Show()
            End If
        End Sub

        #End Region

        Public Overrides Sub Dispose()
            _accountService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

End Namespace
