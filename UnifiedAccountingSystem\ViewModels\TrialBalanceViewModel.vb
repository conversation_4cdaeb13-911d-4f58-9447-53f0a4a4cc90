Imports System.Collections.ObjectModel
Imports System.Windows.Input
Imports System.Windows.Media
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لميزان المراجعة
    ''' </summary>
    Public Class TrialBalanceViewModel
        Inherits ViewModelBase

        Private ReadOnly _expenseService As ExpenseService

        ' الخصائص الأساسية
        Private _trialBalanceItems As ObservableCollection(Of TrialBalanceItemViewModel)
        Private _fiscalYears As ObservableCollection(Of Integer)
        Private _months As ObservableCollection(Of MonthItem)
        Private _selectedFiscalYear As Integer
        Private _selectedMonth As MonthItem
        Private _searchText As String
        Private _lastUpdateTime As DateTime

        Public Sub New()
            _expenseService = New ExpenseService()

            ' تهيئة المجموعات
            TrialBalanceItems = New ObservableCollection(Of TrialBalanceItemViewModel)()
            FiscalYears = New ObservableCollection(Of Integer)()
            Months = New ObservableCollection(Of MonthItem)()

            ' إنشاء الأوامر
            InitializeCommands()

            ' تحميل البيانات الأولية
            LoadInitialData()

            Title = "ميزان المراجعة"
        End Sub

        #Region "Properties"

        Public Property TrialBalanceItems As ObservableCollection(Of TrialBalanceItemViewModel)
            Get
                Return _trialBalanceItems
            End Get
            Set(value As ObservableCollection(Of TrialBalanceItemViewModel))
                SetProperty(_trialBalanceItems, value)
                UpdateTotals()
            End Set
        End Property

        Public Property FiscalYears As ObservableCollection(Of Integer)
            Get
                Return _fiscalYears
            End Get
            Set(value As ObservableCollection(Of Integer))
                SetProperty(_fiscalYears, value)
            End Set
        End Property

        Public Property Months As ObservableCollection(Of MonthItem)
            Get
                Return _months
            End Get
            Set(value As ObservableCollection(Of MonthItem))
                SetProperty(_months, value)
            End Set
        End Property

        Public Property SelectedFiscalYear As Integer
            Get
                Return _selectedFiscalYear
            End Get
            Set(value As Integer)
                SetProperty(_selectedFiscalYear, value)
            End Set
        End Property

        Public Property SelectedMonth As MonthItem
            Get
                Return _selectedMonth
            End Get
            Set(value As MonthItem)
                SetProperty(_selectedMonth, value)
            End Set
        End Property

        Public Property SearchText As String
            Get
                Return _searchText
            End Get
            Set(value As String)
                SetProperty(_searchText, value)
                FilterTrialBalance()
            End Set
        End Property

        Public Property LastUpdateTime As DateTime
            Get
                Return _lastUpdateTime
            End Get
            Set(value As DateTime)
                SetProperty(_lastUpdateTime, value)
            End Set
        End Property

        ' خصائص الإجماليات
        Private _totalOpeningDebit As Decimal
        Private _totalOpeningCredit As Decimal
        Private _totalClosingDebit As Decimal
        Private _totalClosingCredit As Decimal

        Public Property TotalOpeningDebit As Decimal
            Get
                Return _totalOpeningDebit
            End Get
            Set(value As Decimal)
                SetProperty(_totalOpeningDebit, value)
            End Set
        End Property

        Public Property TotalOpeningCredit As Decimal
            Get
                Return _totalOpeningCredit
            End Get
            Set(value As Decimal)
                SetProperty(_totalOpeningCredit, value)
            End Set
        End Property

        Public Property TotalClosingDebit As Decimal
            Get
                Return _totalClosingDebit
            End Get
            Set(value As Decimal)
                SetProperty(_totalClosingDebit, value)
            End Set
        End Property

        Public Property TotalClosingCredit As Decimal
            Get
                Return _totalClosingCredit
            End Get
            Set(value As Decimal)
                SetProperty(_totalClosingCredit, value)
            End Set
        End Property

        Public ReadOnly Property TotalAccountsCount As Integer
            Get
                Return If(TrialBalanceItems?.Count, 0)
            End Get
        End Property

        Public ReadOnly Property BalanceStatus As String
            Get
                If TotalClosingDebit = TotalClosingCredit Then
                    Return "متوازن"
                Else
                    Return "غير متوازن"
                End If
            End Get
        End Property

        Public ReadOnly Property BalanceStatusColor As Brush
            Get
                If TotalClosingDebit = TotalClosingCredit Then
                    Return Brushes.Green
                Else
                    Return Brushes.Red
                End If
            End Get
        End Property

        #End Region

        #Region "Commands"

        Public Property GenerateTrialBalanceCommand As ICommand
        Public Property RefreshCommand As ICommand
        Public Property ExportToExcelCommand As ICommand
        Public Property PrintCommand As ICommand
        Public Property CloseCommand As ICommand

        Private Sub InitializeCommands()
            GenerateTrialBalanceCommand = New AsyncRelayCommand(AddressOf GenerateTrialBalanceAsync)
            RefreshCommand = New AsyncRelayCommand(AddressOf RefreshAsync)
            ExportToExcelCommand = New AsyncRelayCommand(AddressOf ExportToExcelAsync)
            PrintCommand = New RelayCommand(AddressOf Print)
            CloseCommand = New RelayCommand(AddressOf CloseWindow)
        End Sub

        #End Region

        #Region "Methods"

        Private Async Sub LoadInitialData()
            Await ExecuteAsync(Async Function()
                ' تحميل السنوات المالية
                For year As Integer = 2020 To DateTime.Now.Year + 2
                    FiscalYears.Add(year)
                Next
                SelectedFiscalYear = DateTime.Now.Year

                ' تحميل الأشهر
                Dim monthNames() As String = {
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                }

                For i As Integer = 1 To 12
                    Months.Add(New MonthItem() With {.Value = i, .Name = monthNames(i - 1)})
                Next
                SelectedMonth = Months.FirstOrDefault(Function(m) m.Value = DateTime.Now.Month)

                ' تحميل ميزان المراجعة
                Await LoadTrialBalanceAsync()

                LastUpdateTime = DateTime.Now
                StatusMessage = "تم تحميل البيانات بنجاح"

                Return True
            End Function, "خطأ في تحميل البيانات الأولية")
        End Sub

        Private Async Function LoadTrialBalanceAsync() As Task
            Try
                IsLoading = True
                StatusMessage = "جاري تحميل ميزان المراجعة..."

                Dim trialBalanceData = Await _expenseService.GetTrialBalanceAsync(
                    SelectedFiscalYear,
                    If(SelectedMonth?.Value, DateTime.Now.Month)
                )

                TrialBalanceItems.Clear()
                For Each item In trialBalanceData
                    TrialBalanceItems.Add(New TrialBalanceItemViewModel(item))
                Next

                UpdateTotals()
                OnPropertyChanged(NameOf(TotalAccountsCount))
                OnPropertyChanged(NameOf(BalanceStatus))
                OnPropertyChanged(NameOf(BalanceStatusColor))

                StatusMessage = $"تم تحميل {TrialBalanceItems.Count} حساب"

            Catch ex As Exception
                StatusMessage = "خطأ في تحميل ميزان المراجعة"
            Finally
                IsLoading = False
            End Try
        End Function

        Private Sub UpdateTotals()
            If TrialBalanceItems IsNot Nothing Then
                TotalOpeningDebit = TrialBalanceItems.Sum(Function(x) x.OpeningDebitBalance)
                TotalOpeningCredit = TrialBalanceItems.Sum(Function(x) x.OpeningCreditBalance)
                TotalClosingDebit = TrialBalanceItems.Sum(Function(x) x.ClosingDebitBalance)
                TotalClosingCredit = TrialBalanceItems.Sum(Function(x) x.ClosingCreditBalance)
            End If
        End Sub

        Private Sub FilterTrialBalance()
            ' تطبيق التصفية حسب النص المدخل
            If String.IsNullOrWhiteSpace(SearchText) Then
                ' إظهار جميع العناصر
                For Each item In TrialBalanceItems
                    item.IsVisible = True
                Next
            Else
                ' تصفية العناصر
                For Each item In TrialBalanceItems
                    item.IsVisible = item.AccountName.Contains(SearchText) OrElse item.AccountCode.Contains(SearchText)
                Next
            End If
        End Sub

        Private Async Function GenerateTrialBalanceAsync() As Task
            Await ExecuteAsync(Async Function()
                StatusMessage = "جاري إنشاء ميزان المراجعة..."

                Dim success = Await _expenseService.GenerateTrialBalanceAsync(
                    SelectedFiscalYear,
                    If(SelectedMonth?.Value, DateTime.Now.Month)
                )

                If success Then
                    Await LoadTrialBalanceAsync()
                    LastUpdateTime = DateTime.Now
                    StatusMessage = "تم إنشاء ميزان المراجعة بنجاح"
                Else
                    StatusMessage = "فشل في إنشاء ميزان المراجعة"
                End If

                Return success
            End Function, "خطأ في إنشاء ميزان المراجعة")
        End Function

        Private Async Function RefreshAsync() As Task
            Await LoadTrialBalanceAsync()
            LastUpdateTime = DateTime.Now
        End Function

        Private Async Function ExportToExcelAsync() As Task
            StatusMessage = "جاري تصدير البيانات إلى Excel..."
            ' تنفيذ تصدير Excel
            Await Task.Delay(1000) ' محاكاة العملية
            StatusMessage = "تم تصدير البيانات بنجاح"
        End Function

        Private Sub Print()
            StatusMessage = "جاري تحضير التقرير للطباعة..."
            ' تنفيذ الطباعة
        End Sub

        Private Sub CloseWindow()
            ' إغلاق النافذة
        End Sub

        #End Region

        Public Overrides Sub Dispose()
            _expenseService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' ViewModel لعنصر ميزان المراجعة
    ''' </summary>
    Public Class TrialBalanceItemViewModel
        Inherits ViewModelBase

        Private _trialBalance As TrialBalance
        Private _isVisible As Boolean = True

        Public Sub New(trialBalance As TrialBalance)
            _trialBalance = trialBalance
        End Sub

        Public ReadOnly Property AccountCode As String
            Get
                Return _trialBalance.Account?.AccountCode
            End Get
        End Property

        Public ReadOnly Property AccountName As String
            Get
                Return _trialBalance.Account?.AccountName
            End Get
        End Property

        Public ReadOnly Property OpeningDebitBalance As Decimal
            Get
                Return _trialBalance.OpeningDebitBalance
            End Get
        End Property

        Public ReadOnly Property OpeningCreditBalance As Decimal
            Get
                Return _trialBalance.OpeningCreditBalance
            End Get
        End Property

        Public ReadOnly Property DebitMovement As Decimal
            Get
                Return _trialBalance.DebitMovement
            End Get
        End Property

        Public ReadOnly Property CreditMovement As Decimal
            Get
                Return _trialBalance.CreditMovement
            End Get
        End Property

        Public ReadOnly Property ClosingDebitBalance As Decimal
            Get
                Return _trialBalance.ClosingDebitBalance
            End Get
        End Property

        Public ReadOnly Property ClosingCreditBalance As Decimal
            Get
                Return _trialBalance.ClosingCreditBalance
            End Get
        End Property

        Public Property IsVisible As Boolean
            Get
                Return _isVisible
            End Get
            Set(value As Boolean)
                SetProperty(_isVisible, value)
            End Set
        End Property

    End Class

End Namespace
