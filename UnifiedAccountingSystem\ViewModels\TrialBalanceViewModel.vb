Imports System.Collections.ObjectModel
Imports System.Windows.Input
Imports System.Windows.Media
Imports UnifiedAccountingSystem.Utilities
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Namespace ViewModels

    ''' <summary>
    ''' ViewModel لميزان المراجعة
    ''' </summary>
    Public Class TrialBalanceViewModel
        Inherits ViewModelBase

        Private ReadOnly _expenseService As ExpenseService

        ' الخصائص الأساسية
        Private _trialBalanceItems As ObservableCollection(Of TrialBalanceItemViewModel)
        Private _fiscalYears As ObservableCollection(Of Integer)
        Private _months As ObservableCollection(Of MonthItem)
        Private _treasuryTypes As ObservableCollection(Of TreasuryTypeItem)
        Private _selectedFiscalYear As Integer
        Private _selectedFromMonth As MonthItem
        Private _selectedToMonth As MonthItem
        Private _selectedTreasuryType As TreasuryTypeItem
        Private _bankAccountNumber As String
        Private _searchText As String
        Private _lastUpdateTime As DateTime
        Private _organizationName As String = "دائرة الطب الرياضي"

        Public Sub New()
            _expenseService = New ExpenseService()

            ' تهيئة المجموعات
            TrialBalanceItems = New ObservableCollection(Of TrialBalanceItemViewModel)()
            FiscalYears = New ObservableCollection(Of Integer)()
            Months = New ObservableCollection(Of MonthItem)()
            TreasuryTypes = New ObservableCollection(Of TreasuryTypeItem)()

            ' إنشاء الأوامر
            InitializeCommands()

            ' تحميل البيانات الأولية
            LoadInitialData()

            Title = "ميزان المراجعة"
        End Sub

        #Region "Properties"

        Public Property TrialBalanceItems As ObservableCollection(Of TrialBalanceItemViewModel)
            Get
                Return _trialBalanceItems
            End Get
            Set(value As ObservableCollection(Of TrialBalanceItemViewModel))
                SetProperty(_trialBalanceItems, value)
                UpdateTotals()
            End Set
        End Property

        Public Property FiscalYears As ObservableCollection(Of Integer)
            Get
                Return _fiscalYears
            End Get
            Set(value As ObservableCollection(Of Integer))
                SetProperty(_fiscalYears, value)
            End Set
        End Property

        Public Property Months As ObservableCollection(Of MonthItem)
            Get
                Return _months
            End Get
            Set(value As ObservableCollection(Of MonthItem))
                SetProperty(_months, value)
            End Set
        End Property

        Public Property TreasuryTypes As ObservableCollection(Of TreasuryTypeItem)
            Get
                Return _treasuryTypes
            End Get
            Set(value As ObservableCollection(Of TreasuryTypeItem))
                SetProperty(_treasuryTypes, value)
            End Set
        End Property

        Public Property SelectedFiscalYear As Integer
            Get
                Return _selectedFiscalYear
            End Get
            Set(value As Integer)
                SetProperty(_selectedFiscalYear, value)
            End Set
        End Property

        Public Property SelectedFromMonth As MonthItem
            Get
                Return _selectedFromMonth
            End Get
            Set(value As MonthItem)
                SetProperty(_selectedFromMonth, value)
            End Set
        End Property

        Public Property SelectedToMonth As MonthItem
            Get
                Return _selectedToMonth
            End Get
            Set(value As MonthItem)
                SetProperty(_selectedToMonth, value)
            End Set
        End Property

        Public Property SelectedTreasuryType As TreasuryTypeItem
            Get
                Return _selectedTreasuryType
            End Get
            Set(value As TreasuryTypeItem)
                SetProperty(_selectedTreasuryType, value)
            End Set
        End Property

        Public Property BankAccountNumber As String
            Get
                Return _bankAccountNumber
            End Get
            Set(value As String)
                SetProperty(_bankAccountNumber, value)
            End Set
        End Property

        Public Property OrganizationName As String
            Get
                Return _organizationName
            End Get
            Set(value As String)
                SetProperty(_organizationName, value)
            End Set
        End Property

        Public Property SearchText As String
            Get
                Return _searchText
            End Get
            Set(value As String)
                SetProperty(_searchText, value)
                FilterTrialBalance()
            End Set
        End Property

        Public Property LastUpdateTime As DateTime
            Get
                Return _lastUpdateTime
            End Get
            Set(value As DateTime)
                SetProperty(_lastUpdateTime, value)
            End Set
        End Property

        ' خصائص الإجماليات
        Private _totalOpeningDebit As Decimal
        Private _totalOpeningCredit As Decimal
        Private _totalClosingDebit As Decimal
        Private _totalClosingCredit As Decimal

        Public Property TotalOpeningDebit As Decimal
            Get
                Return _totalOpeningDebit
            End Get
            Set(value As Decimal)
                SetProperty(_totalOpeningDebit, value)
            End Set
        End Property

        Public Property TotalOpeningCredit As Decimal
            Get
                Return _totalOpeningCredit
            End Get
            Set(value As Decimal)
                SetProperty(_totalOpeningCredit, value)
            End Set
        End Property

        Public Property TotalClosingDebit As Decimal
            Get
                Return _totalClosingDebit
            End Get
            Set(value As Decimal)
                SetProperty(_totalClosingDebit, value)
            End Set
        End Property

        Public Property TotalClosingCredit As Decimal
            Get
                Return _totalClosingCredit
            End Get
            Set(value As Decimal)
                SetProperty(_totalClosingCredit, value)
            End Set
        End Property

        Public ReadOnly Property TotalAccountsCount As Integer
            Get
                Return If(TrialBalanceItems?.Count, 0)
            End Get
        End Property

        Public ReadOnly Property BalanceStatus As String
            Get
                If TotalClosingDebit = TotalClosingCredit Then
                    Return "متوازن"
                Else
                    Return "غير متوازن"
                End If
            End Get
        End Property

        Public ReadOnly Property BalanceStatusColor As Brush
            Get
                If TotalClosingDebit = TotalClosingCredit Then
                    Return Brushes.Green
                Else
                    Return Brushes.Red
                End If
            End Get
        End Property

        #End Region

        #Region "Commands"

        Public Property GenerateTrialBalanceCommand As ICommand
        Public Property RefreshCommand As ICommand
        Public Property ExportToExcelCommand As ICommand
        Public Property PrintCommand As ICommand
        Public Property CloseCommand As ICommand

        Private Sub InitializeCommands()
            GenerateTrialBalanceCommand = New AsyncRelayCommand(AddressOf GenerateTrialBalanceAsync)
            RefreshCommand = New AsyncRelayCommand(AddressOf RefreshAsync)
            ExportToExcelCommand = New AsyncRelayCommand(AddressOf ExportToExcelAsync)
            PrintCommand = New RelayCommand(AddressOf Print)
            CloseCommand = New RelayCommand(AddressOf CloseWindow)
        End Sub

        #End Region

        #Region "Methods"

        Private Async Sub LoadInitialData()
            Await ExecuteAsync(Async Function()
                ' تحميل السنوات المالية
                For year As Integer = 2020 To DateTime.Now.Year + 2
                    FiscalYears.Add(year)
                Next
                SelectedFiscalYear = DateTime.Now.Year

                ' تحميل الأشهر
                Dim monthNames() As String = {
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                }

                For i As Integer = 1 To 12
                    Months.Add(New MonthItem() With {.Value = i, .Name = monthNames(i - 1)})
                Next
                SelectedFromMonth = Months.FirstOrDefault(Function(m) m.Value = 1) ' يناير
                SelectedToMonth = Months.FirstOrDefault(Function(m) m.Value = DateTime.Now.Month)

                ' تحميل أنواع الخزينة
                TreasuryTypes.Add(New TreasuryTypeItem() With {.Value = "التشغيلية", .Name = "التشغيلية"})
                TreasuryTypes.Add(New TreasuryTypeItem() With {.Value = "الرواتب", .Name = "الخزينة الموجودة (الرواتب)"})
                SelectedTreasuryType = TreasuryTypes.FirstOrDefault()

                ' تحميل ميزان المراجعة
                Await LoadTrialBalanceAsync()

                LastUpdateTime = DateTime.Now
                StatusMessage = "تم تحميل البيانات بنجاح"

                Return True
            End Function, "خطأ في تحميل البيانات الأولية")
        End Sub

        Private Async Function LoadTrialBalanceAsync() As Task
            Try
                IsLoading = True
                StatusMessage = "جاري تحميل ميزان المراجعة..."

                Dim trialBalanceData = Await _expenseService.GetTrialBalanceAsync(
                    SelectedFiscalYear,
                    If(SelectedFromMonth?.Value, 1),
                    If(SelectedToMonth?.Value, DateTime.Now.Month),
                    If(SelectedTreasuryType?.Value, "التشغيلية"),
                    BankAccountNumber
                )

                TrialBalanceItems.Clear()
                For Each item In trialBalanceData
                    TrialBalanceItems.Add(New TrialBalanceItemViewModel(item))
                Next

                UpdateTotals()
                OnPropertyChanged(NameOf(TotalAccountsCount))
                OnPropertyChanged(NameOf(BalanceStatus))
                OnPropertyChanged(NameOf(BalanceStatusColor))

                StatusMessage = $"تم تحميل {TrialBalanceItems.Count} حساب"

            Catch ex As Exception
                StatusMessage = "خطأ في تحميل ميزان المراجعة"
            Finally
                IsLoading = False
            End Try
        End Function

        Private Sub UpdateTotals()
            If TrialBalanceItems IsNot Nothing Then
                TotalOpeningDebit = TrialBalanceItems.Sum(Function(x) x.OpeningDebitBalance)
                TotalOpeningCredit = TrialBalanceItems.Sum(Function(x) x.OpeningCreditBalance)
                TotalClosingDebit = TrialBalanceItems.Sum(Function(x) x.ClosingDebitBalance)
                TotalClosingCredit = TrialBalanceItems.Sum(Function(x) x.ClosingCreditBalance)
            End If
        End Sub

        Private Sub FilterTrialBalance()
            ' تطبيق التصفية حسب النص المدخل
            If String.IsNullOrWhiteSpace(SearchText) Then
                ' إظهار جميع العناصر
                For Each item In TrialBalanceItems
                    item.IsVisible = True
                Next
            Else
                ' تصفية العناصر
                For Each item In TrialBalanceItems
                    item.IsVisible = item.AccountName.Contains(SearchText) OrElse item.AccountCode.Contains(SearchText)
                Next
            End If
        End Sub

        Private Async Function GenerateTrialBalanceAsync() As Task
            Await ExecuteAsync(Async Function()
                StatusMessage = "جاري إنشاء ميزان المراجعة..."

                Dim success = Await _expenseService.GenerateTrialBalanceAsync(
                    SelectedFiscalYear,
                    If(SelectedFromMonth?.Value, 1),
                    If(SelectedToMonth?.Value, DateTime.Now.Month),
                    If(SelectedTreasuryType?.Value, "التشغيلية"),
                    BankAccountNumber
                )

                If success Then
                    Await LoadTrialBalanceAsync()
                    LastUpdateTime = DateTime.Now
                    StatusMessage = "تم إنشاء ميزان المراجعة بنجاح"
                Else
                    StatusMessage = "فشل في إنشاء ميزان المراجعة"
                End If

                Return success
            End Function, "خطأ في إنشاء ميزان المراجعة")
        End Function

        Private Async Function RefreshAsync() As Task
            Await LoadTrialBalanceAsync()
            LastUpdateTime = DateTime.Now
        End Function

        Private Async Function ExportToExcelAsync() As Task
            StatusMessage = "جاري تصدير البيانات إلى Excel..."
            ' تنفيذ تصدير Excel
            Await Task.Delay(1000) ' محاكاة العملية
            StatusMessage = "تم تصدير البيانات بنجاح"
        End Function

        Private Sub Print()
            StatusMessage = "جاري تحضير التقرير للطباعة..."
            ' تنفيذ الطباعة
        End Sub

        Private Sub CloseWindow()
            ' إغلاق النافذة
            RaiseEvent CloseRequested()
        End Sub

        ''' <summary>
        ''' حدث طلب إغلاق النافذة
        ''' </summary>
        Public Event CloseRequested()

        #End Region

        Public Overrides Sub Dispose()
            _expenseService?.Dispose()
            MyBase.Dispose()
        End Sub

    End Class

    ''' <summary>
    ''' ViewModel لعنصر ميزان المراجعة
    ''' </summary>
    Public Class TrialBalanceItemViewModel
        Inherits ViewModelBase

        Private _trialBalance As TrialBalance
        Private _isVisible As Boolean = True

        Public Sub New(trialBalance As TrialBalance)
            _trialBalance = trialBalance
        End Sub

        ' رقم الدليل الحسابي (مستويات 1-6)
        Public ReadOnly Property AccountLevel1 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 1 Then
                    Return code.Substring(0, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountLevel2 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 2 Then
                    Return code.Substring(1, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountLevel3 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 3 Then
                    Return code.Substring(2, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountLevel4 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 4 Then
                    Return code.Substring(3, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountLevel5 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 5 Then
                    Return code.Substring(4, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountLevel6 As String
            Get
                Dim code = _trialBalance.Account?.AccountCode
                If Not String.IsNullOrEmpty(code) AndAlso code.Length >= 6 Then
                    Return code.Substring(5, 1)
                End If
                Return ""
            End Get
        End Property

        Public ReadOnly Property AccountCode As String
            Get
                Return _trialBalance.Account?.AccountCode
            End Get
        End Property

        Public ReadOnly Property AccountName As String
            Get
                Return _trialBalance.Account?.AccountName
            End Get
        End Property

        ' معاملات الشهر الحالي
        Public ReadOnly Property CurrentMonthDebitFils As Decimal
            Get
                Return _trialBalance.CurrentMonthDebitFils
            End Get
        End Property

        Public ReadOnly Property CurrentMonthDebitDinars As Decimal
            Get
                Return _trialBalance.CurrentMonthDebitDinars
            End Get
        End Property

        Public ReadOnly Property CurrentMonthCreditFils As Decimal
            Get
                Return _trialBalance.CurrentMonthCreditFils
            End Get
        End Property

        Public ReadOnly Property CurrentMonthCreditDinars As Decimal
            Get
                Return _trialBalance.CurrentMonthCreditDinars
            End Get
        End Property

        ' المدور في الأشهر السابقة
        Public ReadOnly Property PreviousCarriedDebitFils As Decimal
            Get
                Return _trialBalance.PreviousCarriedDebitFils
            End Get
        End Property

        Public ReadOnly Property PreviousCarriedDebitDinars As Decimal
            Get
                Return _trialBalance.PreviousCarriedDebitDinars
            End Get
        End Property

        Public ReadOnly Property PreviousCarriedCreditFils As Decimal
            Get
                Return _trialBalance.PreviousCarriedCreditFils
            End Get
        End Property

        Public ReadOnly Property PreviousCarriedCreditDinars As Decimal
            Get
                Return _trialBalance.PreviousCarriedCreditDinars
            End Get
        End Property

        ' المجموع الكلي
        Public ReadOnly Property GrandTotalDebitFils As Decimal
            Get
                Return CurrentMonthDebitFils + PreviousCarriedDebitFils
            End Get
        End Property

        Public ReadOnly Property GrandTotalDebitDinars As Decimal
            Get
                Return CurrentMonthDebitDinars + PreviousCarriedDebitDinars
            End Get
        End Property

        Public ReadOnly Property GrandTotalCreditFils As Decimal
            Get
                Return CurrentMonthCreditFils + PreviousCarriedCreditFils
            End Get
        End Property

        Public ReadOnly Property GrandTotalCreditDinars As Decimal
            Get
                Return CurrentMonthCreditDinars + PreviousCarriedCreditDinars
            End Get
        End Property

        ' الرصيد لغاية الشهر الحالي
        Public ReadOnly Property BalanceType As String
            Get
                Return _trialBalance.BalanceType
            End Get
        End Property

        Public ReadOnly Property AbsoluteBalance As Decimal
            Get
                Return _trialBalance.AbsoluteBalance
            End Get
        End Property

        Public ReadOnly Property BalanceColor As Brush
            Get
                Select Case BalanceType
                    Case "مدين"
                        Return Brushes.Blue
                    Case "دائن"
                        Return Brushes.Green
                    Case Else
                        Return Brushes.Black
                End Select
            End Get
        End Property

        ' للتوافق مع الكود القديم
        Public ReadOnly Property OpeningDebitBalance As Decimal
            Get
                Return 0 ' يمكن حسابه من البيانات إذا لزم الأمر
            End Get
        End Property

        Public ReadOnly Property OpeningCreditBalance As Decimal
            Get
                Return 0 ' يمكن حسابه من البيانات إذا لزم الأمر
            End Get
        End Property

        Public ReadOnly Property ClosingDebitBalance As Decimal
            Get
                Return If(BalanceType = "مدين", AbsoluteBalance, 0)
            End Get
        End Property

        Public ReadOnly Property ClosingCreditBalance As Decimal
            Get
                Return If(BalanceType = "دائن", AbsoluteBalance, 0)
            End Get
        End Property

        Public Property IsVisible As Boolean
            Get
                Return _isVisible
            End Get
            Set(value As Boolean)
                SetProperty(_isVisible, value)
            End Set
        End Property

    End Class

    ''' <summary>
    ''' عنصر الشهر
    ''' </summary>
    Public Class MonthItem
        Public Property Value As Integer
        Public Property Name As String
    End Class

    ''' <summary>
    ''' عنصر نوع الخزينة
    ''' </summary>
    Public Class TreasuryTypeItem
        Public Property Value As String
        Public Property Name As String
    End Class

End Namespace
