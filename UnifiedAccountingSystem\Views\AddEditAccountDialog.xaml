<Window x:Class="UnifiedAccountingSystem.AddEditAccountDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل حساب"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="60">
                <StackPanel Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="AccountPlus" 
                                           Foreground="White" 
                                           Width="30" 
                                           Height="30"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding WindowTitle}"
                             Foreground="White"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="18"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Margin="15,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Form Content -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5"
                           Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- معلومات أساسية -->
                    <GroupBox Header="المعلومات الأساسية"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- رمز الحساب -->
                            <TextBox Grid.Row="0" Grid.Column="0"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="رمز الحساب *"
                                   Text="{Binding AccountCode, UpdateSourceTrigger=PropertyChanged}"
                                   IsEnabled="{Binding CanEditAccountCode}"/>

                            <!-- اسم الحساب -->
                            <TextBox Grid.Row="0" Grid.Column="1"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم الحساب *"
                                   Text="{Binding AccountName, UpdateSourceTrigger=PropertyChanged}"/>

                            <!-- نوع الحساب -->
                            <ComboBox Grid.Row="1" Grid.Column="0"
                                    Style="{StaticResource FormComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="نوع الحساب"
                                    ItemsSource="{Binding AccountTypes}"
                                    SelectedItem="{Binding SelectedAccountType}"/>

                            <!-- طبيعة الحساب -->
                            <ComboBox Grid.Row="1" Grid.Column="1"
                                    Style="{StaticResource FormComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="طبيعة الحساب"
                                    ItemsSource="{Binding AccountNatures}"
                                    SelectedItem="{Binding SelectedAccountNature}"/>

                            <!-- الحساب الأب -->
                            <ComboBox Grid.Row="2" Grid.Column="0"
                                    Style="{StaticResource FormComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="الحساب الأب"
                                    ItemsSource="{Binding ParentAccounts}"
                                    SelectedItem="{Binding SelectedParentAccount}"
                                    DisplayMemberPath="DisplayText"
                                    SelectedValuePath="AccountId"/>

                            <!-- حالة النشاط -->
                            <StackPanel Grid.Row="2" Grid.Column="1" 
                                      Orientation="Horizontal" 
                                      VerticalAlignment="Center"
                                      Margin="5,15,5,5">
                                <CheckBox Content="حساب نشط"
                                        FontFamily="{StaticResource ArabicFont}"
                                        FontSize="14"
                                        IsChecked="{Binding IsActive}"
                                        Style="{StaticResource MaterialDesignCheckBox}"/>
                                
                                <CheckBox Content="حساب تحليلي"
                                        FontFamily="{StaticResource ArabicFont}"
                                        FontSize="14"
                                        IsChecked="{Binding IsAnalytical}"
                                        Style="{StaticResource MaterialDesignCheckBox}"
                                        Margin="20,0,0,0"/>
                            </StackPanel>

                            <!-- ملاحظات -->
                            <TextBox Grid.Row="3" Grid.ColumnSpan="2"
                                   Style="{StaticResource MaterialDesignTextBox}"
                                   materialDesign:HintAssist.Hint="ملاحظات"
                                   FontFamily="{StaticResource ArabicFont}"
                                   FontSize="14"
                                   Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   Height="80"
                                   VerticalScrollBarVisibility="Auto"
                                   Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- معاينة رمز الحساب -->
                    <GroupBox Header="معاينة رمز الحساب"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10"
                            Visibility="{Binding ShowAccountCodePreview, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- عناوين المستويات -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="1" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="2" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="3" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="4" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="4" Text="5" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="5" Text="6" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="6" Text="اسم الحساب" FontWeight="Bold" Margin="10,5,5,5"/>

                            <!-- قيم المستويات -->
                            <Border Grid.Row="1" Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel1}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel2}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="2" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel3}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="3" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel4}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="4" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel5}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="5" BorderBrush="Gray" BorderThickness="1" Margin="2">
                                <TextBlock Text="{Binding AccountLevel6}" HorizontalAlignment="Center" Margin="5" FontSize="16" FontWeight="Bold"/>
                            </Border>
                            <TextBlock Grid.Row="1" Grid.Column="6" Text="{Binding AccountName}" Margin="10,5,5,5" FontSize="14" VerticalAlignment="Center"/>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات إضافية -->
                    <GroupBox Header="معلومات إضافية"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10"
                            Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" 
                                     Text="تاريخ الإنشاء:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1"
                                     Text="{Binding CreatedDate, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     Margin="5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0"
                                     Text="المنشئ:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1"
                                     Text="{Binding CreatedBy}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- رسائل التحقق -->
                    <Border Background="#FFEBEE"
                          BorderBrush="{StaticResource ErrorBrush}"
                          BorderThickness="1"
                          CornerRadius="5"
                          Padding="10"
                          Margin="0,10"
                          Visibility="{Binding HasErrors, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ItemsControl ItemsSource="{Binding ValidationErrors}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="0,2">
                                        <materialDesign:PackIcon Kind="AlertCircle" 
                                                               Foreground="{StaticResource ErrorBrush}" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                 FontFamily="{StaticResource ArabicFont}"
                                                 FontSize="12"
                                                 Foreground="{StaticResource ErrorBrush}"
                                                 Margin="5,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- Buttons -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Center"
                      Margin="20">
                <Button Content="حفظ"
                      Style="{StaticResource PrimaryButtonStyle}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding SaveCommand}"
                      IsEnabled="{Binding CanSave}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="حفظ" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Content="إلغاء"
                      Style="{StaticResource SecondaryButtonStyle}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding CancelCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إلغاء" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Content="تطبيق"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding ApplyCommand}"
                      IsEnabled="{Binding CanSave}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Check" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تطبيق" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>
        </materialDesign:Card>

    </Grid>
</Window>
