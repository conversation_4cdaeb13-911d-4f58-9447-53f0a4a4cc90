Imports UnifiedAccountingSystem.ViewModels
Imports UnifiedAccountingSystem.Models

''' <summary>
''' نافذة إضافة/تعديل الحسابات
''' </summary>
Public Class AddEditAccountDialog

    Private _viewModel As AddEditAccountViewModel
    Private _result As Boolean = False

    ''' <summary>
    ''' إنشاء نافذة إضافة حساب جديد
    ''' </summary>
    Public Sub New()
        InitializeComponent()

        _viewModel = New AddEditAccountViewModel()
        DataContext = _viewModel

        ' ربط حدث إغلاق النافذة
        AddHandler _viewModel.CloseRequested, AddressOf OnCloseRequested
    End Sub

    ''' <summary>
    ''' إنشاء نافذة تعديل حساب موجود
    ''' </summary>
    ''' <param name="account">الحساب المراد تعديله</param>
    Public Sub New(account As ChartOfAccount)
        InitializeComponent()

        _viewModel = New AddEditAccountViewModel(account)
        DataContext = _viewModel

        ' ربط حدث إغلاق النافذة
        AddHandler _viewModel.CloseRequested, AddressOf OnCloseRequested
    End Sub

    ''' <summary>
    ''' معالج حدث طلب إغلاق النافذة
    ''' </summary>
    ''' <param name="result">نتيجة النافذة</param>
    Private Sub OnCloseRequested(result As Boolean)
        _result = result
        Close()
    End Sub

    ''' <summary>
    ''' نتيجة النافذة
    ''' </summary>
    Public ReadOnly Property DialogResult As Boolean
        Get
            Return _result
        End Get
    End Property

    ''' <summary>
    ''' معالج إغلاق النافذة
    ''' </summary>
    ''' <param name="e">معاملات الحدث</param>
    Protected Overrides Sub OnClosed(e As EventArgs)
        _viewModel?.Dispose()
        MyBase.OnClosed(e)
    End Sub

    ''' <summary>
    ''' إغلاق النافذة مع النتيجة
    ''' </summary>
    ''' <param name="result">نتيجة النافذة</param>
    Public Sub CloseWithResult(result As Boolean)
        _result = result
        Close()
    End Sub

End Class
