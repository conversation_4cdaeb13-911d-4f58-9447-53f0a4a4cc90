<Window x:Class="UnifiedAccountingSystem.BankAccountSetupDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعداد أرقام الحسابات البنكية"
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="60">
                <StackPanel Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="Bank" 
                                           Foreground="White" 
                                           Width="30" 
                                           Height="30"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إعداد أرقام الحسابات البنكية"
                             Foreground="White"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="18"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Margin="15,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Form Content -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5"
                           Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- معلومات عامة -->
                    <TextBlock Text="يرجى إدخال أرقام الحسابات البنكية للخزينتين:"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="14"
                             Margin="0,0,0,20"
                             TextWrapping="Wrap"/>

                    <!-- حساب التشغيلية -->
                    <GroupBox Header="حساب التشغيلية (المصروفات العامة)"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="رقم الحساب البنكي للتشغيلية"
                                   Text="{Binding OperationalAccountNumber, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="1"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم البنك"
                                   Text="{Binding OperationalBankName, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="2"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم الفرع"
                                   Text="{Binding OperationalBranchName, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="3"
                                   Style="{StaticResource MaterialDesignTextBox}"
                                   materialDesign:HintAssist.Hint="ملاحظات"
                                   FontFamily="{StaticResource ArabicFont}"
                                   FontSize="14"
                                   Text="{Binding OperationalNotes, UpdateSourceTrigger=PropertyChanged}"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   Height="60"
                                   VerticalScrollBarVisibility="Auto"
                                   Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- حساب الرواتب -->
                    <GroupBox Header="حساب الخزينة الموجودة (الرواتب)"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="رقم الحساب البنكي للرواتب"
                                   Text="{Binding PayrollAccountNumber, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="1"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم البنك"
                                   Text="{Binding PayrollBankName, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="2"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم الفرع"
                                   Text="{Binding PayrollBranchName, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="3"
                                   Style="{StaticResource MaterialDesignTextBox}"
                                   materialDesign:HintAssist.Hint="ملاحظات"
                                   FontFamily="{StaticResource ArabicFont}"
                                   FontSize="14"
                                   Text="{Binding PayrollNotes, UpdateSourceTrigger=PropertyChanged}"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   Height="60"
                                   VerticalScrollBarVisibility="Auto"
                                   Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات إضافية -->
                    <GroupBox Header="معلومات إضافية"
                            Style="{StaticResource MaterialDesignGroupBox}"
                            FontFamily="{StaticResource ArabicFont}"
                            FontWeight="Bold"
                            Margin="0,10">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Row="0" Grid.Column="0"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="المحافظة"
                                   Text="{Binding Province, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="0" Grid.Column="1"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="اسم المؤسسة"
                                   Text="{Binding OrganizationName, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="1" Grid.Column="0"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="الباب"
                                   Text="{Binding SectionNumber, UpdateSourceTrigger=PropertyChanged}"/>

                            <TextBox Grid.Row="1" Grid.Column="1"
                                   Style="{StaticResource FormTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="القسم"
                                   Text="{Binding DivisionNumber, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>
                    </GroupBox>

                    <!-- رسائل التحقق -->
                    <Border Background="#FFEBEE"
                          BorderBrush="{StaticResource ErrorBrush}"
                          BorderThickness="1"
                          CornerRadius="5"
                          Padding="10"
                          Margin="0,10"
                          Visibility="{Binding HasErrors, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ItemsControl ItemsSource="{Binding ValidationErrors}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="0,2">
                                        <materialDesign:PackIcon Kind="AlertCircle" 
                                                               Foreground="{StaticResource ErrorBrush}" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                 FontFamily="{StaticResource ArabicFont}"
                                                 FontSize="12"
                                                 Foreground="{StaticResource ErrorBrush}"
                                                 Margin="5,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- Buttons -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Center"
                      Margin="20">
                <Button Content="حفظ"
                      Style="{StaticResource PrimaryButtonStyle}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding SaveCommand}"
                      IsEnabled="{Binding CanSave}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="حفظ" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Content="إلغاء"
                      Style="{StaticResource SecondaryButtonStyle}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding CancelCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إلغاء" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Content="استخدام القيم الافتراضية"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Width="150"
                      Margin="10,0"
                      Command="{Binding LoadDefaultsCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Restore" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="القيم الافتراضية" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>
        </materialDesign:Card>

    </Grid>
</Window>
