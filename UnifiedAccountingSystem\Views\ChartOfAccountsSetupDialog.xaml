<Window x:Class="UnifiedAccountingSystem.ChartOfAccountsSetupDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعداد دليل الحسابات"
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="60">
                <StackPanel Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="AccountTree" 
                                           Foreground="White" 
                                           Width="30" 
                                           Height="30"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إعداد دليل الحسابات"
                             Foreground="White"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="18"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Margin="15,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5"
                           Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- معلومات عامة -->
                    <TextBlock Text="مرحباً بك في إعداد دليل الحسابات"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="16"
                             FontWeight="Bold"
                             Margin="0,0,0,10"/>

                    <TextBlock Text="يمكنك إنشاء دليل الحسابات بإحدى الطرق التالية:"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="14"
                             Margin="0,0,0,20"
                             TextWrapping="Wrap"/>

                    <!-- خيارات الإعداد -->
                    <StackPanel>
                        
                        <!-- إنشاء دليل افتراضي -->
                        <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                           Margin="0,10"
                                           Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0"
                                                       Kind="AccountMultiple"
                                                       Width="40" Height="40"
                                                       Foreground="{StaticResource PrimaryBrush}"
                                                       VerticalAlignment="Center"/>

                                <StackPanel Grid.Column="1" Margin="15,0">
                                    <TextBlock Text="إنشاء دليل الحسابات الافتراضي"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="16"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="إنشاء دليل حسابات جاهز يحتوي على الحسابات الأساسية المطلوبة للنظام المحاسبي العراقي"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="12"
                                             Foreground="Gray"
                                             TextWrapping="Wrap"
                                             Margin="0,5,0,0"/>
                                </StackPanel>

                                <Button Grid.Column="2"
                                      Content="إنشاء"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      Command="{Binding CreateDefaultChartCommand}">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="إنشاء" FontFamily="{StaticResource ArabicFont}"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </Grid>
                        </materialDesign:Card>

                        <!-- استيراد من Excel -->
                        <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                           Margin="0,10"
                                           Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0"
                                                       Kind="FileExcel"
                                                       Width="40" Height="40"
                                                       Foreground="{StaticResource SuccessBrush}"
                                                       VerticalAlignment="Center"/>

                                <StackPanel Grid.Column="1" Margin="15,0">
                                    <TextBlock Text="استيراد من ملف Excel"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="16"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="استيراد دليل الحسابات من ملف Excel جاهز يحتوي على الحسابات المطلوبة"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="12"
                                             Foreground="Gray"
                                             TextWrapping="Wrap"
                                             Margin="0,5,0,0"/>
                                </StackPanel>

                                <Button Grid.Column="2"
                                      Content="استيراد"
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Background="{StaticResource SuccessBrush}"
                                      Foreground="White"
                                      Command="{Binding ImportFromExcelCommand}">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Upload" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="استيراد" FontFamily="{StaticResource ArabicFont}"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </Grid>
                        </materialDesign:Card>

                        <!-- إنشاء ملف نموذجي -->
                        <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                           Margin="0,10"
                                           Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0"
                                                       Kind="FileDocument"
                                                       Width="40" Height="40"
                                                       Foreground="{StaticResource InfoBrush}"
                                                       VerticalAlignment="Center"/>

                                <StackPanel Grid.Column="1" Margin="15,0">
                                    <TextBlock Text="تحميل ملف Excel نموذجي"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="16"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="تحميل ملف Excel نموذجي يحتوي على تنسيق دليل الحسابات لتعبئته وإعادة استيراده"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="12"
                                             Foreground="Gray"
                                             TextWrapping="Wrap"
                                             Margin="0,5,0,0"/>
                                </StackPanel>

                                <Button Grid.Column="2"
                                      Content="تحميل"
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Background="{StaticResource InfoBrush}"
                                      Foreground="White"
                                      Command="{Binding DownloadSampleCommand}">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Download" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="تحميل" FontFamily="{StaticResource ArabicFont}"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </Grid>
                        </materialDesign:Card>

                        <!-- إنشاء يدوي -->
                        <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                           Margin="0,10"
                                           Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0"
                                                       Kind="AccountEdit"
                                                       Width="40" Height="40"
                                                       Foreground="{StaticResource WarningBrush}"
                                                       VerticalAlignment="Center"/>

                                <StackPanel Grid.Column="1" Margin="15,0">
                                    <TextBlock Text="إنشاء يدوي"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="16"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="البدء بدليل حسابات فارغ وإضافة الحسابات يدوياً حسب الحاجة"
                                             FontFamily="{StaticResource ArabicFont}"
                                             FontSize="12"
                                             Foreground="Gray"
                                             TextWrapping="Wrap"
                                             Margin="0,5,0,0"/>
                                </StackPanel>

                                <Button Grid.Column="2"
                                      Content="متابعة"
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Background="{StaticResource WarningBrush}"
                                      Foreground="White"
                                      Command="{Binding ContinueManuallyCommand}">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ArrowRight" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="متابعة" FontFamily="{StaticResource ArabicFont}"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </Grid>
                        </materialDesign:Card>

                    </StackPanel>

                    <!-- معلومات إضافية -->
                    <Border Background="#E3F2FD"
                          BorderBrush="{StaticResource InfoBrush}"
                          BorderThickness="1"
                          CornerRadius="5"
                          Padding="15"
                          Margin="0,20,0,0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon Kind="Information" 
                                                       Foreground="{StaticResource InfoBrush}" 
                                                       Width="20" Height="20" 
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="معلومات مهمة"
                                         FontFamily="{StaticResource ArabicFont}"
                                         FontWeight="Bold"
                                         FontSize="14"
                                         Margin="10,0,0,0"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                            
                            <TextBlock FontFamily="{StaticResource ArabicFont}" FontSize="12" TextWrapping="Wrap">
                                <Run Text="• يمكنك تعديل وإضافة الحسابات لاحقاً من خلال إدارة دليل الحسابات"/>
                                <LineBreak/>
                                <Run Text="• الحسابات الافتراضية تتبع النظام المحاسبي العراقي"/>
                                <LineBreak/>
                                <Run Text="• يمكن استيراد الحسابات من ملف Excel بتنسيق محدد"/>
                                <LineBreak/>
                                <Run Text="• طبيعة الحسابات تحدد تلقائياً حسب رقم الحساب"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>

                    <!-- حالة العملية -->
                    <StackPanel Margin="0,20,0,0"
                              Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ProgressBar IsIndeterminate="True" 
                                   Style="{StaticResource MaterialDesignLinearProgressBar}"
                                   Margin="0,10"/>
                        <TextBlock Text="{Binding StatusMessage}"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="12"
                                 HorizontalAlignment="Center"
                                 Foreground="Gray"/>
                    </StackPanel>

                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- Buttons -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Center"
                      Margin="20">
                
                <Button Content="إغلاق"
                      Style="{StaticResource SecondaryButtonStyle}"
                      Width="120"
                      Margin="10,0"
                      Command="{Binding CloseCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إغلاق" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

            </StackPanel>
        </materialDesign:Card>

    </Grid>
</Window>
