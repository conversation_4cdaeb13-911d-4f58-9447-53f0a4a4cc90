<Window x:Class="UnifiedAccountingSystem.ChartOfAccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="دليل الحسابات"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="TreeViewItemStyle" TargetType="TreeViewItem" BasedOn="{StaticResource MaterialDesignTreeViewItem}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="IsExpanded" Value="True"/>
        </Style>

        <HierarchicalDataTemplate x:Key="AccountTemplate" ItemsSource="{Binding ChildAccounts}">
            <StackPanel Orientation="Horizontal" Margin="5">
                <materialDesign:PackIcon Kind="AccountBox" 
                                       Width="16" Height="16" 
                                       VerticalAlignment="Center"
                                       Foreground="{Binding AccountColor}"
                                       Margin="0,0,5,0"/>
                <TextBlock Text="{Binding AccountCode}" 
                         FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}"
                         Margin="0,0,10,0"/>
                <TextBlock Text="{Binding AccountName}" 
                         FontFamily="{StaticResource ArabicFont}"/>
                <TextBlock Text="{Binding AccountNature}" 
                         FontSize="11"
                         Foreground="Gray"
                         Margin="10,0,0,0"
                         FontStyle="Italic"/>
            </StackPanel>
        </HierarchicalDataTemplate>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="80">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" 
                          Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="AccountTree" 
                                           Foreground="White" 
                                           Width="40" 
                                           Height="40"
                                           VerticalAlignment="Center"/>
                    <StackPanel Margin="15,0,0,0" VerticalAlignment="Center">
                        <TextBlock Text="دليل الحسابات"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="20"
                                 FontWeight="Bold"/>
                        <TextBlock Text="إدارة شجرة الحسابات المحاسبية"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="12"
                                 Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="2"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Width="40" Height="40"
                      Margin="20,0"
                      Foreground="White"
                      Command="{Binding CloseCommand}"
                      ToolTip="إغلاق">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5">
            <ToolBar Style="{StaticResource MaterialDesignToolBar}"
                   Background="Transparent">
                
                <!-- إضافة حساب جديد -->
                <Button Content="إضافة حساب"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource SuccessBrush}"
                      Foreground="White"
                      Command="{Binding AddAccountCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة حساب" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <!-- تعديل حساب -->
                <Button Content="تعديل"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource WarningBrush}"
                      Foreground="White"
                      Command="{Binding EditAccountCommand}"
                      IsEnabled="{Binding HasSelectedAccount}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تعديل" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <!-- حذف حساب -->
                <Button Content="حذف"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource ErrorBrush}"
                      Foreground="White"
                      Command="{Binding DeleteAccountCommand}"
                      IsEnabled="{Binding HasSelectedAccount}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="حذف" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator/>

                <!-- استيراد من Excel -->
                <Button Content="استيراد من Excel"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource InfoBrush}"
                      Foreground="White"
                      Command="{Binding ImportFromExcelCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="استيراد من Excel" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <!-- تصدير إلى Excel -->
                <Button Content="تصدير إلى Excel"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource SecondaryBrush}"
                      Foreground="White"
                      Command="{Binding ExportToExcelCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Download" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير إلى Excel" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <!-- إنشاء ملف نموذجي -->
                <Button Content="ملف نموذجي"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Command="{Binding CreateSampleFileCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="ملف نموذجي" FontFamily="{StaticResource ArabicFont}"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator/>

                <!-- البحث -->
                <TextBox Style="{StaticResource MaterialDesignTextBox}"
                       materialDesign:HintAssist.Hint="البحث في الحسابات"
                       FontFamily="{StaticResource ArabicFont}"
                       Width="200"
                       Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>

                <Separator/>

                <!-- إحصائيات -->
                <TextBlock Text="إجمالي الحسابات: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding TotalAccountsCount}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>

                <TextBlock Text="الحسابات النشطة: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center" Margin="20,0,0,0"/>
                <TextBlock Text="{Binding ActiveAccountsCount}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>
            </ToolBar>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Tree View -->
            <materialDesign:Card Grid.Column="0" 
                               materialDesign:ShadowAssist.ShadowDepth="Depth1"
                               Margin="10,5,0,5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" 
                             Text="شجرة الحسابات"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="16"
                             FontWeight="Bold"
                             Foreground="{StaticResource PrimaryBrush}"
                             Margin="15,15,15,10"/>

                    <TreeView Grid.Row="1"
                            ItemsSource="{Binding RootAccounts}"
                            SelectedItemChanged="TreeView_SelectedItemChanged"
                            ItemTemplate="{StaticResource AccountTemplate}"
                            ItemContainerStyle="{StaticResource TreeViewItemStyle}"
                            Margin="10"/>
                </Grid>
            </materialDesign:Card>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" 
                        Width="5" 
                        HorizontalAlignment="Stretch" 
                        Background="#E0E0E0"/>

            <!-- Details Panel -->
            <materialDesign:Card Grid.Column="2" 
                               materialDesign:ShadowAssist.ShadowDepth="Depth1"
                               Margin="0,5,10,5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" 
                             Text="تفاصيل الحساب"
                             FontFamily="{StaticResource ArabicFont}"
                             FontSize="16"
                             FontWeight="Bold"
                             Foreground="{StaticResource PrimaryBrush}"
                             Margin="15,15,15,10"/>

                    <ScrollViewer Grid.Row="1" 
                                VerticalScrollBarVisibility="Auto"
                                Margin="15,0,15,15">
                        <StackPanel DataContext="{Binding SelectedAccount}">
                            
                            <!-- رمز الحساب -->
                            <TextBlock Text="رمز الحساب:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,10,0,5"/>
                            <TextBlock Text="{Binding AccountCode}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontSize="16"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     FontWeight="Bold"/>

                            <!-- اسم الحساب -->
                            <TextBlock Text="اسم الحساب:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"/>
                            <TextBlock Text="{Binding AccountName}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontSize="14"
                                     TextWrapping="Wrap"/>

                            <!-- نوع الحساب -->
                            <TextBlock Text="نوع الحساب:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"/>
                            <TextBlock Text="{Binding AccountType}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontSize="14"/>

                            <!-- طبيعة الحساب -->
                            <TextBlock Text="طبيعة الحساب:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"/>
                            <Border Background="{Binding NatureColor}"
                                  CornerRadius="10"
                                  Padding="8,4"
                                  HorizontalAlignment="Left">
                                <TextBlock Text="{Binding AccountNature}"
                                         FontFamily="{StaticResource ArabicFont}"
                                         FontSize="12"
                                         Foreground="White"
                                         FontWeight="Bold"/>
                            </Border>

                            <!-- حساب تحليلي -->
                            <TextBlock Text="حساب تحليلي:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="{Binding AnalyticalIcon}" 
                                                       Width="16" Height="16" 
                                                       Foreground="{Binding AnalyticalColor}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding AnalyticalText}"
                                         FontFamily="{StaticResource ArabicFont}"
                                         FontSize="14"
                                         Margin="5,0,0,0"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- الحالة -->
                            <TextBlock Text="الحالة:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="{Binding StatusIcon}" 
                                                       Width="16" Height="16" 
                                                       Foreground="{Binding StatusColor}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding StatusText}"
                                         FontFamily="{StaticResource ArabicFont}"
                                         FontSize="14"
                                         Margin="5,0,0,0"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- ملاحظات -->
                            <TextBlock Text="ملاحظات:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,15,0,5"
                                     Visibility="{Binding HasNotes, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            <TextBlock Text="{Binding Notes}"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontSize="12"
                                     TextWrapping="Wrap"
                                     Foreground="Gray"
                                     Visibility="{Binding HasNotes, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <!-- معلومات الإنشاء -->
                            <Separator Margin="0,20"/>
                            <TextBlock Text="معلومات الإنشاء:"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontWeight="Bold"
                                     Margin="0,10,0,5"/>
                            <TextBlock FontFamily="{StaticResource ArabicFont}" FontSize="11" Foreground="Gray">
                                <Run Text="تاريخ الإنشاء: "/>
                                <Run Text="{Binding CreatedDate, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}"/>
                                <LineBreak/>
                                <Run Text="المنشئ: "/>
                                <Run Text="{Binding CreatedBy}"/>
                            </TextBlock>

                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" 
                 Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" 
                         FontFamily="{StaticResource ArabicFont}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="آخر تحديث: " FontFamily="{StaticResource ArabicFont}"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

    </Grid>
</Window>
