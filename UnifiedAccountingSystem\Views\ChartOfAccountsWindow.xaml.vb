Imports UnifiedAccountingSystem.ViewModels

''' <summary>
''' نافذة دليل الحسابات
''' </summary>
Public Class ChartOfAccountsWindow

    Private _viewModel As ChartOfAccountsViewModel

    Public Sub New()
        InitializeComponent()
        
        _viewModel = New ChartOfAccountsViewModel()
        DataContext = _viewModel
    End Sub

    ''' <summary>
    ''' معالج تغيير العنصر المحدد في الشجرة
    ''' </summary>
    ''' <param name="sender">المرسل</param>
    ''' <param name="e">معاملات الحدث</param>
    Private Sub TreeView_SelectedItemChanged(sender As Object, e As RoutedPropertyChangedEventArgs(Of Object))
        If TypeOf e.NewValue Is ChartOfAccountViewModel Then
            _viewModel.SelectedAccount = DirectCast(e.NewValue, ChartOfAccountViewModel)
        End If
    End Sub

    ''' <summary>
    ''' تنظيف الموارد عند إغلاق النافذة
    ''' </summary>
    ''' <param name="e">معاملات الحدث</param>
    Protected Overrides Sub OnClosed(e As EventArgs)
        _viewModel?.Dispose()
        MyBase.OnClosed(e)
    End Sub

End Class
