<Window x:Class="UnifiedAccountingSystem.ExpenseManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة المصروفات والتخصيصات المالية"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="All"/>
            <Setter Property="HeadersVisibility" Value="All"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="80">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" 
                          Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Foreground="White" 
                                           Width="40" 
                                           Height="40"
                                           VerticalAlignment="Center"/>
                    <StackPanel Margin="15,0,0,0" VerticalAlignment="Center">
                        <TextBlock Text="إدارة المصروفات والتخصيصات المالية"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="20"
                                 FontWeight="Bold"/>
                        <TextBlock Text="ملحق جدول المصروفات النهائية على الموازنة الجارية"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="12"
                                 Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="2"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Width="40" Height="40"
                      Margin="20,0"
                      Foreground="White"
                      Command="{Binding CloseCommand}"
                      ToolTip="إغلاق">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Filter Panel -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5">
            <Expander Header="🔍 خيارات البحث والتصفية" 
                    FontFamily="{StaticResource ArabicFont}"
                    FontSize="14"
                    FontWeight="Bold"
                    IsExpanded="True">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Row 1 -->
                    <ComboBox Grid.Row="0" Grid.Column="0"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="السنة المالية"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="5"
                            ItemsSource="{Binding FiscalYears}"
                            SelectedItem="{Binding SelectedFiscalYear}"/>

                    <ComboBox Grid.Row="0" Grid.Column="1"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="الشهر"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="5"
                            ItemsSource="{Binding Months}"
                            SelectedItem="{Binding SelectedMonth}"
                            DisplayMemberPath="Name"
                            SelectedValuePath="Value"/>

                    <ComboBox Grid.Row="0" Grid.Column="2"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="الدائرة"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="5"
                            ItemsSource="{Binding Departments}"
                            SelectedItem="{Binding SelectedDepartment}"
                            DisplayMemberPath="DepartmentName"
                            SelectedValuePath="DepartmentId"/>

                    <TextBox Grid.Row="0" Grid.Column="3"
                           Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="البحث في البيان"
                           FontFamily="{StaticResource ArabicFont}"
                           Margin="5"
                           Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>

                    <!-- Row 2 -->
                    <TextBox Grid.Row="1" Grid.Column="0"
                           Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="نوع الاستمارة"
                           FontFamily="{StaticResource ArabicFont}"
                           Margin="5"
                           Text="{Binding FilterFormType, UpdateSourceTrigger=PropertyChanged}"/>

                    <TextBox Grid.Row="1" Grid.Column="1"
                           Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="نوع النفقة"
                           FontFamily="{StaticResource ArabicFont}"
                           Margin="5"
                           Text="{Binding FilterExpenseType, UpdateSourceTrigger=PropertyChanged}"/>

                    <TextBox Grid.Row="1" Grid.Column="2"
                           Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="الفصل"
                           FontFamily="{StaticResource ArabicFont}"
                           Margin="5"
                           Text="{Binding FilterChapter, UpdateSourceTrigger=PropertyChanged}"/>

                    <TextBox Grid.Row="1" Grid.Column="3"
                           Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="المادة"
                           FontFamily="{StaticResource ArabicFont}"
                           Margin="5"
                           Text="{Binding FilterArticle, UpdateSourceTrigger=PropertyChanged}"/>

                    <!-- Buttons -->
                    <StackPanel Grid.Row="0" Grid.Column="4" Grid.RowSpan="2"
                              Orientation="Vertical"
                              VerticalAlignment="Center"
                              Margin="10,0">
                        <Button Content="بحث"
                              Style="{StaticResource PrimaryButtonStyle}"
                              Margin="5"
                              Command="{Binding SearchCommand}"/>
                        <Button Content="مسح"
                              Style="{StaticResource SecondaryButtonStyle}"
                              Margin="5"
                              Command="{Binding ClearFiltersCommand}"/>
                    </StackPanel>

                    <!-- Summary Info -->
                    <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="5"
                              Orientation="Horizontal"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0">
                        <TextBlock Text="الخزينة: " FontFamily="{StaticResource ArabicFont}" FontWeight="Bold"/>
                        <TextBlock Text="{Binding TreasuryType}" FontFamily="{StaticResource ArabicFont}" Margin="5,0"/>
                        <TextBlock Text="الباب: " FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" Margin="20,0,0,0"/>
                        <TextBlock Text="{Binding SectionNumber}" FontFamily="{StaticResource ArabicFont}" Margin="5,0"/>
                        <TextBlock Text="القسم: " FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" Margin="20,0,0,0"/>
                        <TextBlock Text="{Binding DivisionNumber}" FontFamily="{StaticResource ArabicFont}" Margin="5,0"/>
                        <TextBlock Text="المحافظة: " FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" Margin="20,0,0,0"/>
                        <TextBlock Text="{Binding Province}" FontFamily="{StaticResource ArabicFont}" Margin="5,0"/>
                    </StackPanel>
                </Grid>
            </Expander>
        </materialDesign:Card>

        <!-- Main Content -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Toolbar -->
                <ToolBar Grid.Row="0" 
                       Style="{StaticResource MaterialDesignToolBar}"
                       Background="Transparent">
                    <Button Content="إضافة بند جديد"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource SuccessBrush}"
                          Foreground="White"
                          Command="{Binding AddNewItemCommand}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة بند جديد" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Content="تعديل"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource WarningBrush}"
                          Foreground="White"
                          Command="{Binding EditItemCommand}"
                          IsEnabled="{Binding HasSelectedItem}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تعديل" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Content="حذف"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource ErrorBrush}"
                          Foreground="White"
                          Command="{Binding DeleteItemCommand}"
                          IsEnabled="{Binding HasSelectedItem}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="حذف" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Separator/>

                    <Button Content="تصدير Excel"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource InfoBrush}"
                          Foreground="White"
                          Command="{Binding ExportToExcelCommand}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير Excel" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Content="طباعة"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource SecondaryBrush}"
                          Foreground="White"
                          Command="{Binding PrintCommand}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="طباعة" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Separator/>

                    <TextBlock Text="إجمالي البنود: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalItemsCount}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>
                    
                    <TextBlock Text="إجمالي التخصيص: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center" Margin="20,0,0,0"/>
                    <TextBlock Text="{Binding TotalAllocation, StringFormat='{}{0:N0} دينار'}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>
                </ToolBar>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1"
                        Style="{StaticResource DataGridStyle}"
                        ItemsSource="{Binding ExpenseItems}"
                        SelectedItem="{Binding SelectedItem}"
                        Margin="10">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="نوع الاستمارة" Binding="{Binding FormType}" Width="80"/>
                        <DataGridTextColumn Header="نوع النفقة" Binding="{Binding ExpenseType}" Width="80"/>
                        <DataGridTextColumn Header="الفصل" Binding="{Binding Chapter}" Width="60"/>
                        <DataGridTextColumn Header="المادة" Binding="{Binding Article}" Width="60"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding ItemType}" Width="60"/>
                        <DataGridTextColumn Header="تفاصيل النوع" Binding="{Binding ItemDetails}" Width="80"/>
                        <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="300"/>
                        
                        <DataGridTextColumn Header="مصروفات الشهر السابق (ف)" Binding="{Binding PreviousMonthFils, StringFormat='{}{0:N0}'}" Width="120"/>
                        <DataGridTextColumn Header="مصروفات الشهر السابق (دينار)" Binding="{Binding PreviousMonthDinars, StringFormat='{}{0:N0}'}" Width="140"/>
                        
                        <DataGridTextColumn Header="مصروفات الشهر الحالي (ف)" Binding="{Binding CurrentMonthFils, StringFormat='{}{0:N0}'}" Width="120"/>
                        <DataGridTextColumn Header="مصروفات الشهر الحالي (دينار)" Binding="{Binding CurrentMonthDinars, StringFormat='{}{0:N0}'}" Width="140"/>
                        
                        <DataGridTextColumn Header="المجموع الكلي (دينار)" Binding="{Binding GrandTotal, StringFormat='{}{0:N0}'}" Width="120"/>
                        <DataGridTextColumn Header="التخصيص السنوي (دينار)" Binding="{Binding AnnualAllocation, StringFormat='{}{0:N0}'}" Width="140"/>
                        
                        <DataGridTemplateColumn Header="نسبة الإنفاق" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ProgressBar Value="{Binding SpendingPercentage}" 
                                               Maximum="100" 
                                               Height="20"
                                               Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" 
                 Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" 
                         FontFamily="{StaticResource ArabicFont}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="آخر تحديث: " FontFamily="{StaticResource ArabicFont}"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

    </Grid>
</Window>
