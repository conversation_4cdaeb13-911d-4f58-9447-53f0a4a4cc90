<Window x:Class="UnifiedAccountingSystem.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - النظام المحاسبي الموحد"
        Height="600" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="LoginTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="FlowDirection" Value="RightToLeft"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
        </Style>

        <Style x:Key="LoginPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignPasswordBox}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="FlowDirection" Value="RightToLeft"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
        </Style>

        <Style x:Key="LoginButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="22"/>
        </Style>
    </Window.Resources>

    <Border Background="White" 
            CornerRadius="15"
            Effect="{materialDesign:ShadowAssist.ShadowDepth=Depth3}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" 
                    Background="{StaticResource PrimaryBrush}" 
                    CornerRadius="15,15,0,0"
                    Height="120">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Logo and Title -->
                    <StackPanel Grid.Column="0" 
                                VerticalAlignment="Center" 
                                HorizontalAlignment="Center"
                                Orientation="Vertical">
                        
                        <materialDesign:PackIcon Kind="AccountBalance" 
                                               Foreground="White" 
                                               Width="40" 
                                               Height="40"
                                               Margin="0,0,0,10"/>
                        
                        <TextBlock Text="النظام المحاسبي الموحد"
                                   Foreground="White"
                                   FontFamily="{StaticResource ArabicFont}"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        
                        <TextBlock Text="نظام إدارة الرواتب والمحاسبة"
                                   Foreground="White"
                                   FontFamily="{StaticResource ArabicFont}"
                                   FontSize="12"
                                   Opacity="0.8"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <!-- Close Button -->
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="30" Height="30"
                            Margin="10"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Right"
                            Foreground="White"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Login Form -->
            <StackPanel Grid.Row="1" 
                        Margin="40,30"
                        VerticalAlignment="Center">

                <!-- Welcome Message -->
                <TextBlock Text="مرحباً بك"
                           FontFamily="{StaticResource ArabicFont}"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="{StaticResource PrimaryBrush}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,10"/>

                <TextBlock Text="يرجى تسجيل الدخول للمتابعة"
                           FontFamily="{StaticResource ArabicFont}"
                           FontSize="14"
                           Foreground="Gray"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,30"/>

                <!-- Username -->
                <TextBox x:Name="txtUsername"
                         Style="{StaticResource LoginTextBoxStyle}"
                         materialDesign:HintAssist.Hint="اسم المستخدم"
                         Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Password -->
                <PasswordBox x:Name="txtPassword"
                             Style="{StaticResource LoginPasswordBoxStyle}"
                             materialDesign:HintAssist.Hint="كلمة المرور"
                             PasswordChanged="PasswordBox_PasswordChanged">
                    <PasswordBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                    </PasswordBox.InputBindings>
                </PasswordBox>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني"
                          FontFamily="{StaticResource ArabicFont}"
                          FontSize="14"
                          Margin="0,10"
                          IsChecked="{Binding RememberMe}"
                          Style="{StaticResource MaterialDesignCheckBox}"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           FontFamily="{StaticResource ArabicFont}"
                           FontSize="12"
                           Foreground="{StaticResource ErrorBrush}"
                           HorizontalAlignment="Center"
                           Margin="0,10"
                           TextWrapping="Wrap"
                           Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- Login Button -->
                <Button Content="تسجيل الدخول"
                        Style="{StaticResource LoginButtonStyle}"
                        Command="{Binding LoginCommand}"
                        IsEnabled="{Binding CanLogin}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Login" 
                                                   Width="20" Height="20" 
                                                   Margin="0,0,10,0"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="تسجيل الدخول" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                             Height="4"
                             Margin="0,10"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             Style="{StaticResource MaterialDesignLinearProgressBar}"/>

            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2" 
                    Background="#F5F5F5" 
                    CornerRadius="0,0,15,15"
                    Height="60">
                <StackPanel VerticalAlignment="Center" 
                            HorizontalAlignment="Center">
                    <TextBlock Text="النظام المحاسبي الموحد - الإصدار 1.0"
                               FontFamily="{StaticResource ArabicFont}"
                               FontSize="11"
                               Foreground="Gray"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="جميع الحقوق محفوظة © 2024"
                               FontFamily="{StaticResource ArabicFont}"
                               FontSize="10"
                               Foreground="Gray"
                               HorizontalAlignment="Center"
                               Margin="0,2,0,0"/>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</Window>
