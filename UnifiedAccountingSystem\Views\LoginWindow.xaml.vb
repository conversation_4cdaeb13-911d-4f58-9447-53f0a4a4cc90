Imports System.Windows
Imports System.Windows.Controls
Imports UnifiedAccountingSystem.ViewModels

''' <summary>
''' نافذة تسجيل الدخول
''' </summary>
Public Class LoginWindow
    Inherits Window

    Private ReadOnly _viewModel As LoginViewModel

    Public Sub New()
        InitializeComponent()
        
        ' إنشاء ViewModel وربطه بالنافذة
        _viewModel = New LoginViewModel()
        DataContext = _viewModel
        
        ' ربط الأحداث
        AddHandler _viewModel.LoginSuccessful, AddressOf OnLoginSuccessful
        AddHandler _viewModel.LoginFailed, AddressOf OnLoginFailed
        
        ' تركيز على مربع اسم المستخدم
        txtUsername.Focus()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير كلمة المرور
    ''' </summary>
    Private Sub PasswordBox_PasswordChanged(sender As Object, e As RoutedEventArgs)
        If _viewModel IsNot Nothing Then
            _viewModel.Password = txtPassword.Password
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث إغلاق النافذة
    ''' </summary>
    Private Sub CloseButton_Click(sender As Object, e As RoutedEventArgs)
        Application.Current.Shutdown()
    End Sub

    ''' <summary>
    ''' معالج حدث نجاح تسجيل الدخول
    ''' </summary>
    Private Sub OnLoginSuccessful(sender As Object, e As EventArgs)
        Try
            ' إخفاء نافذة تسجيل الدخول
            Me.Hide()
            
            ' فتح النافذة الرئيسية
            Dim mainWindow As New MainWindow()
            mainWindow.Show()
            
            ' إغلاق نافذة تسجيل الدخول
            Me.Close()
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح النافذة الرئيسية: {ex.Message}", 
                          "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث فشل تسجيل الدخول
    ''' </summary>
    Private Sub OnLoginFailed(sender As Object, e As EventArgs)
        ' مسح كلمة المرور
        txtPassword.Clear()
        
        ' تركيز على مربع اسم المستخدم
        txtUsername.Focus()
        txtUsername.SelectAll()
    End Sub

    ''' <summary>
    ''' معالج حدث تحميل النافذة
    ''' </summary>
    Private Sub LoginWindow_Loaded(sender As Object, e As RoutedEventArgs) Handles Me.Loaded
        Try
            ' تحميل إعدادات تسجيل الدخول المحفوظة
            LoadSavedCredentials()
            
        Catch ex As Exception
            ' تجاهل أخطاء تحميل الإعدادات
        End Try
    End Sub

    ''' <summary>
    ''' تحميل بيانات تسجيل الدخول المحفوظة
    ''' </summary>
    Private Sub LoadSavedCredentials()
        Try
            ' يمكن تحميل اسم المستخدم المحفوظ من الإعدادات
            Dim savedUsername As String = My.Settings.SavedUsername
            If Not String.IsNullOrEmpty(savedUsername) Then
                _viewModel.Username = savedUsername
                _viewModel.RememberMe = True
                
                ' تركيز على مربع كلمة المرور
                txtPassword.Focus()
            End If
            
        Catch ex As Exception
            ' تجاهل أخطاء تحميل الإعدادات
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث إغلاق النافذة
    ''' </summary>
    Private Sub LoginWindow_Closing(sender As Object, e As ComponentModel.CancelEventArgs) Handles Me.Closing
        Try
            ' حفظ إعدادات تسجيل الدخول إذا كان المستخدم يريد تذكرها
            If _viewModel.RememberMe AndAlso Not String.IsNullOrEmpty(_viewModel.Username) Then
                My.Settings.SavedUsername = _viewModel.Username
                My.Settings.Save()
            Else
                My.Settings.SavedUsername = String.Empty
                My.Settings.Save()
            End If
            
        Catch ex As Exception
            ' تجاهل أخطاء حفظ الإعدادات
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث الضغط على مفتاح
    ''' </summary>
    Private Sub LoginWindow_KeyDown(sender As Object, e As Input.KeyEventArgs) Handles Me.KeyDown
        ' إغلاق النافذة عند الضغط على Escape
        If e.Key = Input.Key.Escape Then
            Application.Current.Shutdown()
        End If
    End Sub

    ''' <summary>
    ''' تنظيف الموارد
    ''' </summary>
    Protected Overrides Sub OnClosed(e As EventArgs)
        ' إلغاء ربط الأحداث
        If _viewModel IsNot Nothing Then
            RemoveHandler _viewModel.LoginSuccessful, AddressOf OnLoginSuccessful
            RemoveHandler _viewModel.LoginFailed, AddressOf OnLoginFailed
            _viewModel.Dispose()
        End If
        
        MyBase.OnClosed(e)
    End Sub

End Class
