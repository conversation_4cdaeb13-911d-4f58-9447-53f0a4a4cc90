Imports UnifiedAccountingSystem.ViewModels

''' <summary>
''' النافذة الرئيسية للنظام
''' </summary>
Public Class MainWindow

    Private _viewModel As MainWindowViewModel

    Public Sub New()
        InitializeComponent()
        
        _viewModel = New MainWindowViewModel()
        DataContext = _viewModel
    End Sub

    ''' <summary>
    ''' تنظيف الموارد عند إغلاق النافذة
    ''' </summary>
    ''' <param name="e">معاملات الحدث</param>
    Protected Overrides Sub OnClosed(e As EventArgs)
        _viewModel?.Dispose()
        MyBase.OnClosed(e)
    End Sub

End Class
