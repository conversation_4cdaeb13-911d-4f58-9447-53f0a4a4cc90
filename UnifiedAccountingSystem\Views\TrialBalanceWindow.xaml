<Window x:Class="UnifiedAccountingSystem.TrialBalanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="ميزان المراجعة"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="TotalRowStyle" TargetType="DataGridRow">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Background" Value="#F0F8FF"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Background="{StaticResource PrimaryBrush}"
                           Margin="10,10,10,5">
            <Grid Height="80">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" 
                          Orientation="Horizontal" 
                          VerticalAlignment="Center"
                          Margin="20,0">
                    <materialDesign:PackIcon Kind="Scale" 
                                           Foreground="White" 
                                           Width="40" 
                                           Height="40"
                                           VerticalAlignment="Center"/>
                    <StackPanel Margin="15,0,0,0" VerticalAlignment="Center">
                        <TextBlock Text="ميزان المراجعة"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="20"
                                 FontWeight="Bold"/>
                        <TextBlock Text="كشف الأرصدة والحركات المحاسبية"
                                 Foreground="White"
                                 FontFamily="{StaticResource ArabicFont}"
                                 FontSize="12"
                                 Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="2"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Width="40" Height="40"
                      Margin="20,0"
                      Foreground="White"
                      Command="{Binding CloseCommand}"
                      ToolTip="إغلاق">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Filter Panel -->
        <materialDesign:Card Grid.Row="1" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <ComboBox Grid.Column="0"
                        Style="{StaticResource MaterialDesignComboBox}"
                        materialDesign:HintAssist.Hint="السنة المالية"
                        FontFamily="{StaticResource ArabicFont}"
                        Margin="5"
                        ItemsSource="{Binding FiscalYears}"
                        SelectedItem="{Binding SelectedFiscalYear}"/>

                <ComboBox Grid.Column="1"
                        Style="{StaticResource MaterialDesignComboBox}"
                        materialDesign:HintAssist.Hint="الشهر"
                        FontFamily="{StaticResource ArabicFont}"
                        Margin="5"
                        ItemsSource="{Binding Months}"
                        SelectedItem="{Binding SelectedMonth}"
                        DisplayMemberPath="Name"
                        SelectedValuePath="Value"/>

                <TextBox Grid.Column="2"
                       Style="{StaticResource MaterialDesignTextBox}"
                       materialDesign:HintAssist.Hint="البحث في الحسابات"
                       FontFamily="{StaticResource ArabicFont}"
                       Margin="5"
                       Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>

                <StackPanel Grid.Column="3" 
                          Orientation="Vertical"
                          Margin="10,0">
                    <Button Content="إنشاء ميزان المراجعة"
                          Style="{StaticResource PrimaryButtonStyle}"
                          Margin="5"
                          Command="{Binding GenerateTrialBalanceCommand}"/>
                    <Button Content="تحديث"
                          Style="{StaticResource SecondaryButtonStyle}"
                          Margin="5"
                          Command="{Binding RefreshCommand}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <ToolBar Grid.Row="0" 
                       Style="{StaticResource MaterialDesignToolBar}"
                       Background="Transparent">
                    <Button Content="تصدير Excel"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource InfoBrush}"
                          Foreground="White"
                          Command="{Binding ExportToExcelCommand}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير Excel" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Content="طباعة"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{StaticResource SecondaryBrush}"
                          Foreground="White"
                          Command="{Binding PrintCommand}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="طباعة" FontFamily="{StaticResource ArabicFont}"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Separator/>

                    <TextBlock Text="عدد الحسابات: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalAccountsCount}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>

                    <TextBlock Text="تاريخ آخر تحديث: " FontFamily="{StaticResource ArabicFont}" VerticalAlignment="Center" Margin="20,0,0,0"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}" FontFamily="{StaticResource ArabicFont}" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>
                </ToolBar>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1"
                        Style="{StaticResource MaterialDesignDataGrid}"
                        FontFamily="{StaticResource ArabicFont}"
                        FontSize="12"
                        ItemsSource="{Binding TrialBalanceItems}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        GridLinesVisibility="All"
                        HeadersVisibility="All"
                        Margin="10">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رمز الحساب" Binding="{Binding AccountCode}" Width="100"/>
                        <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="300"/>
                        
                        <DataGridTextColumn Header="الرصيد الافتتاحي مدين" Binding="{Binding OpeningDebitBalance, StringFormat='{}{0:N2}'}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="الرصيد الافتتاحي دائن" Binding="{Binding OpeningCreditBalance, StringFormat='{}{0:N2}'}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="حركة مدينة" Binding="{Binding DebitMovement, StringFormat='{}{0:N2}'}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="حركة دائنة" Binding="{Binding CreditMovement, StringFormat='{}{0:N2}'}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="الرصيد الختامي مدين" Binding="{Binding ClosingDebitBalance, StringFormat='{}{0:N2}'}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="الرصيد الختامي دائن" Binding="{Binding ClosingCreditBalance, StringFormat='{}{0:N2}'}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Summary Panel -->
        <materialDesign:Card Grid.Row="3" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth1"
                           Margin="10,5"
                           Background="#F8F9FA">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الأرصدة الافتتاحية المدينة" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalOpeningDebit, StringFormat='{}{0:N2} دينار'}" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الأرصدة الافتتاحية الدائنة" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalOpeningCredit, StringFormat='{}{0:N2} دينار'}" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Foreground="{StaticResource SuccessBrush}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الأرصدة الختامية المدينة" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalClosingDebit, StringFormat='{}{0:N2} دينار'}" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الأرصدة الختامية الدائنة" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalClosingCredit, StringFormat='{}{0:N2} دينار'}" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Foreground="{StaticResource SuccessBrush}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Status Bar -->
        <StatusBar Grid.Row="4" 
                 Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" 
                         FontFamily="{StaticResource ArabicFont}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="حالة التوازن: " FontFamily="{StaticResource ArabicFont}"/>
                    <TextBlock Text="{Binding BalanceStatus}" 
                             FontFamily="{StaticResource ArabicFont}" 
                             FontWeight="Bold"
                             Foreground="{Binding BalanceStatusColor}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

    </Grid>
</Window>
