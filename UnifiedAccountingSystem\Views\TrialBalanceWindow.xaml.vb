Imports UnifiedAccountingSystem.ViewModels

''' <summary>
''' نافذة ميزان المراجعة
''' </summary>
Public Class TrialBalanceWindow

    Private _viewModel As TrialBalanceViewModel

    Public Sub New()
        InitializeComponent()

        _viewModel = New TrialBalanceViewModel()
        DataContext = _viewModel

        ' ربط حدث إغلاق النافذة
        AddHandler _viewModel.CloseRequested, AddressOf OnCloseRequested
    End Sub

    ''' <summary>
    ''' معالج حدث طلب إغلاق النافذة
    ''' </summary>
    Private Sub OnCloseRequested()
        Close()
    End Sub

    ''' <summary>
    ''' تنظيف الموارد عند إغلاق النافذة
    ''' </summary>
    ''' <param name="e">معاملات الحدث</param>
    Protected Overrides Sub OnClosed(e As EventArgs)
        _viewModel?.Dispose()
        MyBase.OnClosed(e)
    End Sub

End Class
