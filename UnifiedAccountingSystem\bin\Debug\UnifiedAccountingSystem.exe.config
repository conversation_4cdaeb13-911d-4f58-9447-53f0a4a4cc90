<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>

  <connectionStrings>
    <!-- اتصال قاعدة البيانات المحلية -->
    <add name="AccountingConnectionString" 
         connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\UnifiedAccountingSystem.mdf;Integrated Security=True;Connect Timeout=30" 
         providerName="System.Data.SqlClient" />
    
    <!-- اتصال قاعدة البيانات عبر الشبكة - يمكن تفعيله عند الحاجة -->
    <!--
    <add name="AccountingConnectionString" 
         connectionString="Data Source=SERVER_NAME;Initial Catalog=UnifiedAccountingSystem;Integrated Security=True;Connect Timeout=30" 
         providerName="System.Data.SqlClient" />
    -->
  </connectionStrings>

  <appSettings>
    <!-- إعدادات التطبيق العامة -->
    <add key="SystemName" value="النظام المحاسبي الموحد" />
    <add key="SystemVersion" value="1.0.0" />
    <add key="DefaultLanguage" value="ar-IQ" />
    <add key="DefaultCulture" value="ar-IQ" />
    
    <!-- إعدادات الواجهة -->
    <add key="Theme" value="Light" />
    <add key="PrimaryColor" value="Blue" />
    <add key="AccentColor" value="LightBlue" />
    <add key="FontFamily" value="Segoe UI" />
    <add key="FontSize" value="14" />
    
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="BackupPath" value="C:\AccountingBackups\" />
    <add key="AutoBackupEnabled" value="true" />
    <add key="AutoBackupInterval" value="24" /> <!-- بالساعات -->
    <add key="BackupRetentionDays" value="30" />
    
    <!-- إعدادات التقارير -->
    <add key="ReportsPath" value="C:\AccountingReports\" />
    <add key="TempPath" value="C:\Temp\" />
    <add key="ExportFormats" value="PDF,Excel,Word" />
    
    <!-- إعدادات الأمان -->
    <add key="SessionTimeout" value="30" /> <!-- بالدقائق -->
    <add key="PasswordMinLength" value="6" />
    <add key="MaxLoginAttempts" value="3" />
    <add key="PasswordComplexity" value="false" />
    
    <!-- إعدادات الرواتب -->
    <add key="RetirementFundRate" value="10" /> <!-- نسبة صندوق التقاعد -->
    <add key="GovernmentContributionRate" value="15" /> <!-- نسبة مساهمة الدائرة -->
    <add key="IncomeTaxEnabled" value="true" />
    <add key="SocialSecurityEnabled" value="true" />
    
    <!-- إعدادات المحاسبة -->
    <add key="DefaultCurrency" value="IQD" />
    <add key="DecimalPlaces" value="2" />
    <add key="FiscalYearStart" value="1" /> <!-- شهر بداية السنة المالية -->
    <add key="AutoGenerateAccountCodes" value="true" />
    
    <!-- إعدادات استيراد البيانات -->
    <add key="ImportPath" value="C:\AccountingImports\" />
    <add key="AllowedFileTypes" value=".xlsx,.xls,.csv" />
    <add key="MaxFileSize" value="********" /> <!-- 10 MB -->
    
    <!-- إعدادات الطباعة -->
    <add key="DefaultPrinter" value="" />
    <add key="PrintOrientation" value="Portrait" />
    <add key="PaperSize" value="A4" />
    <add key="PrintMargins" value="20,20,20,20" />
  </appSettings>

  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>

  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
