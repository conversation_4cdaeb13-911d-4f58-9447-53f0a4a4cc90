from django.db import models
from core.models import BaseModel

# نماذج المحاسبة - سيتم إضافتها لاحقاً

class Account(BaseModel):
    """حساب مؤقت للاختبار"""
    name = models.CharField('اسم الحساب', max_length=100)
    
    class Meta:
        verbose_name = 'حساب'
        verbose_name_plural = 'الحسابات'
    
    def __str__(self):
        return self.name

class JournalEntry(BaseModel):
    """قيد يومية مؤقت للاختبار"""
    description = models.CharField('الوصف', max_length=200)
    amount = models.DecimalField('المبلغ', max_digits=15, decimal_places=3, default=0)
    
    class Meta:
        verbose_name = 'قيد يومية'
        verbose_name_plural = 'القيود اليومية'
    
    def __str__(self):
        return self.description
