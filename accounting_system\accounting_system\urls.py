"""
URLs الرئيسية للنظام المحاسبي المتكامل
Main URLs for Integrated Accounting System
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # Core app - الصفحة الرئيسية
    path('', include('core.urls')),
    
    # Users app - إدارة المستخدمين
    path('users/', include('users.urls')),
    
    # Settings app - الإعدادات والتهيئة
    path('settings/', include('settings_app.urls')),
    
    # HR app - شؤون الموظفين
    path('hr/', include('hr.urls')),
    
    # Accounting app - الحسابات
    path('accounting/', include('accounting.urls')),
    
    # Payroll app - الرواتب
    path('payroll/', include('payroll.urls')),
    
    # Reports app - التقارير
    path('reports/', include('reports.urls')),
    
    # Redirect root to dashboard
    path('', RedirectView.as_view(url='/dashboard/', permanent=False)),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Admin site customization
admin.site.site_header = "النظام المحاسبي المتكامل"
admin.site.site_title = "النظام المحاسبي"
admin.site.index_title = "لوحة التحكم"
