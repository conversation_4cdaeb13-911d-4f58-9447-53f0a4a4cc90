"""
معالجات السياق للنظام المحاسبي
Context processors for the accounting system
"""
from django.conf import settings
from .models import Notification, SystemSettings
from settings_app.models import Organization, AccountingPeriod


def system_info(request):
    """
    معلومات النظام العامة
    General system information
    """
    context = {
        'SYSTEM_NAME': 'النظام المحاسبي المتكامل',
        'SYSTEM_VERSION': '1.0.0',
        'SYSTEM_DEVELOPER': 'فريق التطوير المتقدم',
    }
    
    # إضافة معلومات المستخدم المسجل
    if request.user.is_authenticated:
        # الإشعارات غير المقروءة
        unread_notifications_count = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).count()
        
        context.update({
            'unread_notifications_count': unread_notifications_count,
        })
        
        # المؤسسة الحالية
        try:
            current_organization = Organization.objects.filter(is_active=True).first()
            context['current_organization'] = current_organization
        except:
            context['current_organization'] = None
        
        # الفترة المحاسبية الحالية
        try:
            current_period = AccountingPeriod.objects.filter(is_current=True).first()
            context['current_period'] = current_period
        except:
            context['current_period'] = None
    
    return context


def system_settings_context(request):
    """
    إعدادات النظام
    System settings
    """
    context = {}
    
    try:
        # جلب إعدادات النظام
        system_settings = SystemSettings.objects.filter(is_active=True)
        settings_dict = {setting.key: setting.value for setting in system_settings}
        context['system_settings'] = settings_dict
    except:
        context['system_settings'] = {}
    
    return context
