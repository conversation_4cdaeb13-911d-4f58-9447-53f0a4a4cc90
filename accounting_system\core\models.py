"""
النماذج الأساسية للنظام المحاسبي
Core models for the accounting system
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class BaseModel(models.Model):
    """
    النموذج الأساسي لجميع الجداول
    Base model for all tables
    """
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_created',
        verbose_name='أنشئ بواسطة'
    )
    updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='حُدث بواسطة'
    )
    is_active = models.BooleanField('نشط', default=True)
    notes = models.TextField('ملاحظات', blank=True, null=True)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = timezone.now()
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)


class SystemLog(BaseModel):
    """
    سجل أنشطة النظام
    System activity log
    """
    ACTION_CHOICES = [
        ('CREATE', 'إنشاء'),
        ('UPDATE', 'تحديث'),
        ('DELETE', 'حذف'),
        ('LOGIN', 'تسجيل دخول'),
        ('LOGOUT', 'تسجيل خروج'),
        ('BACKUP', 'نسخ احتياطي'),
        ('RESTORE', 'استرجاع'),
        ('EXPORT', 'تصدير'),
        ('IMPORT', 'استيراد'),
    ]
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        verbose_name='المستخدم'
    )
    action = models.CharField(
        'العملية', 
        max_length=20, 
        choices=ACTION_CHOICES
    )
    model_name = models.CharField('اسم الجدول', max_length=100)
    object_id = models.PositiveIntegerField('معرف الكائن', null=True, blank=True)
    description = models.TextField('الوصف')
    ip_address = models.GenericIPAddressField('عنوان IP', null=True, blank=True)
    user_agent = models.TextField('متصفح المستخدم', blank=True)
    
    class Meta:
        verbose_name = 'سجل النظام'
        verbose_name_plural = 'سجلات النظام'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.get_action_display()} - {self.model_name}"


class SystemSettings(models.Model):
    """
    إعدادات النظام العامة
    General system settings
    """
    key = models.CharField('المفتاح', max_length=100, unique=True)
    value = models.TextField('القيمة')
    description = models.TextField('الوصف', blank=True)
    is_active = models.BooleanField('نشط', default=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    
    class Meta:
        verbose_name = 'إعداد النظام'
        verbose_name_plural = 'إعدادات النظام'
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value[:50]}"


class Notification(BaseModel):
    """
    إشعارات النظام
    System notifications
    """
    NOTIFICATION_TYPES = [
        ('INFO', 'معلومات'),
        ('WARNING', 'تحذير'),
        ('ERROR', 'خطأ'),
        ('SUCCESS', 'نجح'),
    ]
    
    title = models.CharField('العنوان', max_length=200)
    message = models.TextField('الرسالة')
    notification_type = models.CharField(
        'نوع الإشعار', 
        max_length=20, 
        choices=NOTIFICATION_TYPES,
        default='INFO'
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        verbose_name='المستخدم',
        related_name='notifications'
    )
    is_read = models.BooleanField('مقروء', default=False)
    read_at = models.DateTimeField('تاريخ القراءة', null=True, blank=True)
    
    class Meta:
        verbose_name = 'إشعار'
        verbose_name_plural = 'الإشعارات'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.user.username}"
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        self.is_read = True
        self.read_at = timezone.now()
        self.save()


class FileUpload(BaseModel):
    """
    ملفات النظام المرفوعة
    System uploaded files
    """
    FILE_TYPES = [
        ('IMAGE', 'صورة'),
        ('DOCUMENT', 'مستند'),
        ('EXCEL', 'ملف إكسل'),
        ('PDF', 'ملف PDF'),
        ('BACKUP', 'نسخة احتياطية'),
    ]
    
    name = models.CharField('اسم الملف', max_length=255)
    file = models.FileField('الملف', upload_to='uploads/%Y/%m/')
    file_type = models.CharField(
        'نوع الملف', 
        max_length=20, 
        choices=FILE_TYPES
    )
    size = models.PositiveIntegerField('حجم الملف (بايت)')
    description = models.TextField('الوصف', blank=True)
    
    class Meta:
        verbose_name = 'ملف مرفوع'
        verbose_name_plural = 'الملفات المرفوعة'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    @property
    def size_formatted(self):
        """حجم الملف مُنسق"""
        if self.size < 1024:
            return f"{self.size} بايت"
        elif self.size < 1024 * 1024:
            return f"{self.size / 1024:.1f} كيلوبايت"
        else:
            return f"{self.size / (1024 * 1024):.1f} ميجابايت"
