"""
العروض الأساسية للنظام المحاسبي
Core views for the accounting system
"""
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Sum
from django.utils import timezone
from datetime import datetime, timedelta

from .models import SystemLog, Notification
from users.models import User, UserGroup
from settings_app.models import Organization, Department, AccountingPeriod
from accounting.models import Account, JournalEntry
from payroll.models import Employee


@login_required
def dashboard(request):
    """
    لوحة التحكم الرئيسية
    Main dashboard
    """
    # إحصائيات عامة
    stats = {
        'total_users': User.objects.filter(is_active=True).count(),
        'total_employees': Employee.objects.filter(is_active=True).count(),
        'total_accounts': Account.objects.filter(is_active=True).count(),
        'total_journal_entries': JournalEntry.objects.filter(is_active=True).count(),
    }
    
    # الأنشطة الأخيرة
    recent_activities = SystemLog.objects.select_related('user').order_by('-created_at')[:10]
    
    # الإشعارات غير المقروءة
    unread_notifications = Notification.objects.filter(
        user=request.user, 
        is_read=False
    ).order_by('-created_at')[:5]
    
    # إحصائيات الأنشطة اليومية
    today = timezone.now().date()
    daily_activities = SystemLog.objects.filter(
        created_at__date=today
    ).values('action').annotate(count=Count('id'))
    
    # الفترة المحاسبية الحالية
    current_period = AccountingPeriod.objects.filter(is_current=True).first()
    
    context = {
        'stats': stats,
        'recent_activities': recent_activities,
        'unread_notifications': unread_notifications,
        'daily_activities': daily_activities,
        'current_period': current_period,
        'current_time': timezone.now(),
    }
    
    return render(request, 'core/dashboard.html', context)


@login_required
def system_info(request):
    """
    معلومات النظام
    System information
    """
    from django import get_version
    import sys
    import platform
    
    # معلومات النظام
    system_info = {
        'django_version': get_version(),
        'python_version': sys.version,
        'platform': platform.platform(),
        'server_time': timezone.now(),
    }
    
    # إحصائيات قاعدة البيانات
    db_stats = {
        'users': User.objects.count(),
        'user_groups': UserGroup.objects.count(),
        'organizations': Organization.objects.count(),
        'departments': Department.objects.count(),
        'employees': Employee.objects.count(),
        'accounts': Account.objects.count(),
        'journal_entries': JournalEntry.objects.count(),
        'system_logs': SystemLog.objects.count(),
    }
    
    # معلومات التطبيق
    app_info = {
        'name': 'النظام المحاسبي المتكامل',
        'version': '1.0.0',
        'developer': 'فريق التطوير المتقدم',
        'release_date': '2024-01-01',
        'description': 'نظام محاسبي شامل لإدارة الحسابات المالية والرواتب',
    }
    
    context = {
        'system_info': system_info,
        'db_stats': db_stats,
        'app_info': app_info,
    }
    
    return render(request, 'core/system_info.html', context)


@login_required
def notifications_list(request):
    """
    قائمة الإشعارات
    Notifications list
    """
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')
    
    # تحديد الإشعارات كمقروءة عند عرضها
    unread_notifications = notifications.filter(is_read=False)
    for notification in unread_notifications:
        notification.mark_as_read()
    
    context = {
        'notifications': notifications,
    }
    
    return render(request, 'core/notifications.html', context)


@login_required
def mark_notification_read(request, notification_id):
    """
    تحديد إشعار كمقروء
    Mark notification as read
    """
    try:
        notification = Notification.objects.get(
            id=notification_id, 
            user=request.user
        )
        notification.mark_as_read()
        messages.success(request, 'تم تحديد الإشعار كمقروء')
    except Notification.DoesNotExist:
        messages.error(request, 'الإشعار غير موجود')
    
    return redirect('core:notifications')


@login_required
def system_logs(request):
    """
    سجلات النظام
    System logs
    """
    logs = SystemLog.objects.select_related('user').order_by('-created_at')
    
    # فلترة حسب المستخدم
    user_filter = request.GET.get('user')
    if user_filter:
        logs = logs.filter(user_id=user_filter)
    
    # فلترة حسب العملية
    action_filter = request.GET.get('action')
    if action_filter:
        logs = logs.filter(action=action_filter)
    
    # فلترة حسب التاريخ
    date_filter = request.GET.get('date')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            logs = logs.filter(created_at__date=filter_date)
        except ValueError:
            pass
    
    # تقسيم الصفحات
    from django.core.paginator import Paginator
    paginator = Paginator(logs, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # قوائم للفلترة
    users = User.objects.filter(is_active=True).order_by('username')
    actions = SystemLog.ACTION_CHOICES
    
    context = {
        'page_obj': page_obj,
        'users': users,
        'actions': actions,
        'current_filters': {
            'user': user_filter,
            'action': action_filter,
            'date': date_filter,
        }
    }
    
    return render(request, 'core/system_logs.html', context)


def handler404(request, exception):
    """معالج خطأ 404"""
    return render(request, 'errors/404.html', status=404)


def handler500(request):
    """معالج خطأ 500"""
    return render(request, 'errors/500.html', status=500)


def handler403(request, exception):
    """معالج خطأ 403"""
    return render(request, 'errors/403.html', status=403)
