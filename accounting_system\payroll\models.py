from django.db import models
from core.models import BaseModel

# نماذج الرواتب - سيتم إضافتها لاحقاً

class Employee(BaseModel):
    """موظف مؤقت للاختبار"""
    name = models.Char<PERSON>ield('اسم الموظف', max_length=100)
    salary = models.DecimalField('الراتب الأساسي', max_digits=15, decimal_places=3, default=0)
    
    class Meta:
        verbose_name = 'موظف'
        verbose_name_plural = 'الموظفون'
    
    def __str__(self):
        return self.name
