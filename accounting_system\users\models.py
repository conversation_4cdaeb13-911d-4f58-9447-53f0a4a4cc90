"""
نماذج إدارة المستخدمين
User management models
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from core.models import BaseModel


class UserGroup(BaseModel):
    """
    مجموعة المستخدمين
    User groups
    """
    group_number = models.CharField('رقم المجموعة', max_length=20, unique=True)
    group_name = models.CharField('اسم المجموعة', max_length=100)
    
    class Meta:
        verbose_name = 'مجموعة المستخدمين'
        verbose_name_plural = 'مجموعات المستخدمين'
        ordering = ['group_number']
    
    def __str__(self):
        return f"{self.group_number} - {self.group_name}"


class User(AbstractUser):
    """
    نموذج المستخدم المخصص
    Custom user model
    """
    USER_TYPES = [
        ('ADMIN', 'مدير'),
        ('USER', 'مستخدم'),
        ('ACCOUNTANT', 'محاسب'),
        ('HR', 'موارد بشرية'),
        ('VIEWER', 'مشاهد'),
    ]
    
    account_number = models.CharField('رقم الحساب', max_length=20, unique=True, null=True, blank=True)
    account_name = models.CharField('اسم الحساب', max_length=100)
    user_type = models.CharField('نوع الحساب', max_length=20, choices=USER_TYPES, default='USER')
    department = models.ForeignKey(
        'settings_app.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='الدائرة'
    )
    user_group = models.ForeignKey(
        UserGroup,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='مجموعة المستخدمين'
    )
    phone = models.CharField('رقم الهاتف', max_length=20, blank=True)
    address = models.TextField('العنوان', blank=True)
    profile_image = models.ImageField('صورة الملف الشخصي', upload_to='profiles/', blank=True, null=True)
    is_active = models.BooleanField('نشط', default=True)
    last_login_ip = models.GenericIPAddressField('آخر IP دخول', null=True, blank=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    
    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'
        ordering = ['username']
    
    def __str__(self):
        return f"{self.username} - {self.account_name}"
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.account_name or self.username
    
    def get_permissions_list(self):
        """قائمة صلاحيات المستخدم"""
        permissions = []
        if self.is_superuser:
            permissions.append('جميع الصلاحيات')
        else:
            for perm in self.user_permissions.all():
                permissions.append(perm.name)
            for group in self.groups.all():
                for perm in group.permissions.all():
                    if perm.name not in permissions:
                        permissions.append(perm.name)
        return permissions


class UserSession(BaseModel):
    """
    جلسات المستخدمين
    User sessions
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    session_key = models.CharField('مفتاح الجلسة', max_length=40)
    ip_address = models.GenericIPAddressField('عنوان IP')
    user_agent = models.TextField('متصفح المستخدم', blank=True)
    login_time = models.DateTimeField('وقت تسجيل الدخول', auto_now_add=True)
    logout_time = models.DateTimeField('وقت تسجيل الخروج', null=True, blank=True)
    is_active = models.BooleanField('نشط', default=True)
    
    class Meta:
        verbose_name = 'جلسة مستخدم'
        verbose_name_plural = 'جلسات المستخدمين'
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.user.username} - {self.login_time}"


class UserPermission(BaseModel):
    """
    صلاحيات المستخدمين المخصصة
    Custom user permissions
    """
    PERMISSION_TYPES = [
        ('VIEW', 'عرض'),
        ('ADD', 'إضافة'),
        ('CHANGE', 'تعديل'),
        ('DELETE', 'حذف'),
        ('EXPORT', 'تصدير'),
        ('IMPORT', 'استيراد'),
        ('APPROVE', 'اعتماد'),
        ('PRINT', 'طباعة'),
    ]
    
    MODULE_CHOICES = [
        ('USERS', 'إدارة المستخدمين'),
        ('SETTINGS', 'الإعدادات'),
        ('HR', 'شؤون الموظفين'),
        ('ACCOUNTING', 'الحسابات'),
        ('PAYROLL', 'الرواتب'),
        ('REPORTS', 'التقارير'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    module = models.CharField('الوحدة', max_length=20, choices=MODULE_CHOICES)
    permission_type = models.CharField('نوع الصلاحية', max_length=20, choices=PERMISSION_TYPES)
    granted = models.BooleanField('ممنوحة', default=True)
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions',
        verbose_name='منحت بواسطة'
    )
    
    class Meta:
        verbose_name = 'صلاحية مستخدم'
        verbose_name_plural = 'صلاحيات المستخدمين'
        unique_together = ['user', 'module', 'permission_type']
    
    def __str__(self):
        return f"{self.user.username} - {self.get_module_display()} - {self.get_permission_type_display()}"
